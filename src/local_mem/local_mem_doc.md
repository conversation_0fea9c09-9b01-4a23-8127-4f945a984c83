# Local Memory (LMEM) 设计文档

## 1. 模块概述

Local Memory (LMEM) 是一个基于 SystemC/TLM 的高性能本地存储系统，为 NPU 中的各个功能单元提供统一的内存访问接口。该模块采用分层架构设计，支持 Scratchpad Memory (SPAD) 和 CIM Cluster Memory (CIMC) 两种不同类型的存储单元，并通过 TLM 2.0 接口提供标准化的内存访问服务。

### 1.1 主要特性

- **分层存储架构**：支持 SPAD 和 CIMC 两种存储类型，满足不同访问需求
- **统一接口**：提供统一的 TLM 2.0 接口，简化上层模块的内存访问
- **256位数据宽度**：支持 256 位对齐的高带宽内存访问
- **并发安全**：基于读写锁机制的线程安全实现
- **CIM 集成**：CIMC 单元与 DCIM 集群模型同步，支持计算内存融合
- **灵活配置**：支持可配置的存储容量和地址映射
- **调试支持**：提供完整的内存转储和统计信息功能

## 2. 架构设计

### 2.1 整体架构

```mermaid
graph TB
    subgraph "Local Memory System"
        subgraph "LocalMemory Module"
            LM1[LocalMemory<br/>SystemC Module]
            LM2[TLM Target Socket<br/>multi_passthrough]
            LM3[Address Decoder<br/>Unit & Offset]
        end
        
        subgraph "Memory Units Array"
            MU1[SPAD Unit 0<br/>4096 Words]
            MU2[SPAD Unit 1<br/>4096 Words]
            MU3[SPAD Unit 2<br/>4096 Words]
            MU4[SPAD Unit 3<br/>4096 Words]
            MU5[CIMC Unit 0<br/>4224 Words]
        end
        
        subgraph "Memory Interfaces"
            MI1[IMemoryUnit<br/>Base Interface]
            MI2[Scratchpad<br/>Implementation]
            MI3[CIMCluster<br/>Implementation]
        end
        
        subgraph "Synchronization"
            SY1[DcimCluster<br/>C++ Model]
            SY2[writeWordLinear<br/>Sync Interface]
        end
    end
    
    subgraph "External Modules"
        EM1[MPU Module]
        EM2[TMU Module]
        EM3[TLU/TSU Modules]
        EM4[VPU Module]
    end
    
    EM1 --> LM2
    EM2 --> LM2
    EM3 --> LM2
    EM4 --> LM2
    
    LM2 --> LM3
    LM3 --> MU1
    LM3 --> MU2
    LM3 --> MU3
    LM3 --> MU4
    LM3 --> MU5
    
    MU1 --> MI2
    MU2 --> MI2
    MU3 --> MI2
    MU4 --> MI2
    MU5 --> MI3
    
    MI3 --> SY1
    SY1 --> SY2
```

### 2.2 主要组件

#### 2.2.1 LocalMemory 主模块
- SystemC 模块，提供 TLM 2.0 接口
- 管理多个内存单元的访问调度
- 地址解码和路由功能
- 统计信息收集和错误处理

#### 2.2.2 存储单元管理器
- **Scratchpad**: 高速暂存存储，支持通用数据访问
- **CIMCluster**: 计算内存集群，与 DCIM 模型集成

#### 2.2.3 接口抽象层
- **IMemoryUnit**: 统一的内存单元接口
- 支持字节级和字级访问
- 提供调试和重置功能

## 3. 接口设计

### 3.1 TLM 接口

#### 3.1.1 目标接口 (target_socket)
```cpp
tlm_utils::multi_passthrough_target_socket<LocalMemory> target_socket;
```
- **功能**: 接收来自各功能单元的内存访问请求
- **数据宽度**: 256位 (32字节) 对齐访问
- **操作类型**: TLM_READ_COMMAND, TLM_WRITE_COMMAND
- **地址格式**: 分层地址编码 (单元索引 + 字偏移)

#### 3.1.2 同步接口 (DcimCluster)
```cpp
DcimCluster* dcim_cluster_ptr;
```
- **功能**: 与 C++ 功能模型同步数据状态
- **操作方法**: writeWordLinear(), readWordLinear()
- **同步时机**: CIMC 写操作时自动同步

### 3.2 内存单元接口

#### 3.2.1 IMemoryUnit 基类接口
```cpp
class IMemoryUnit {
public:
    virtual bool read_bytes(uint64_t byte_offset, uint8_t* data, size_t len) = 0;
    virtual bool write_bytes(uint64_t byte_offset, const uint8_t* data, size_t len) = 0;
    virtual bool read_word(uint64_t word_offset, Word256b& data) = 0;
    virtual bool write_word(uint64_t word_offset, const Word256b& data) = 0;
    virtual void dump(std::ostream& os) const = 0;
    virtual void reset() = 0;
};
```

## 4. 存储架构

### 4.1 存储布局

```mermaid
graph LR
    subgraph "Local Memory Address Space"
        subgraph "SPAD Units (0-3)"
            SP1["SPAD 0<br/>Size: 4096×256bit<br/>Bytes: 131,072"]
            SP2["SPAD 1<br/>Size: 4096×256bit<br/>Bytes: 131,072"]
            SP3["SPAD 2<br/>Size: 4096×256bit<br/>Bytes: 131,072"]
            SP4["SPAD 3<br/>Size: 4096×256bit<br/>Bytes: 131,072"]
        end
        
        subgraph "CIMC Units (4)"
            CM1["CIMC 0<br/>Size: 4224×256bit<br/>Bytes: 135,168<br/>Structure: 8M×33R×16P"]
        end
    end
    
    subgraph "Address Decoding"
        AD1[Unit Index: 3 bits<br/>Range: 0-4]
        AD2[Word Offset: Variable<br/>SPAD: 12 bits<br/>CIMC: 13 bits]
        AD3[256-bit Alignment<br/>Low 5 bits = 0]
    end
```

### 4.2 地址编码格式

#### 4.2.1 地址结构
```
|-------|---------------|-----|
| Unit  | Word Offset   | 0's |
| Index |               |     |
|-------|---------------|-----|
  3 bits   Variable      5 bits
```

#### 4.2.2 地址解码函数
```cpp
bool decode(uint64_t address, size_t& unit_index, uint64_t& word_offset) {
    // 检查256位对齐 (32字节对齐)
    if (address & 0x1F) return false;
    
    // 提取单元索引和字偏移
    unit_index = (address >> (NPUConfig::LMEM_OFFSET + 5)) & 
                 ((1ULL << NPUConfig::LMEM_INDEX) - 1);
    word_offset = (address >> 5) & 
                  ((1ULL << NPUConfig::LMEM_OFFSET) - 1);
}
```

### 4.3 CIMC 存储结构

#### 4.3.1 三维存储映射
- **8个宏 (Macros)**: 顶层存储单元
- **33行/宏 (Rows/Macro)**: 32数据行 + 1指数行
- **16页/行 (Pages/Row)**: 最小访问单元

#### 4.3.2 线性地址映射
```cpp
bool parse_word_offset(uint64_t word_offset,
                       uint32_t& cim_engine_idx,
                       uint32_t& cim_macro_idx_in_engine,
                       uint32_t& cim_macro_row_idx,
                       uint32_t& page) {
    cim_engine_idx = word_offset / NPUConfig::WORDS_PER_ENGINE;
    uint64_t word_offset_in_engine = word_offset % NPUConfig::WORDS_PER_ENGINE;
    
    cim_macro_idx_in_engine = word_offset_in_engine / NPUConfig::WORDS_PER_MACRO;
    uint64_t word_offset_in_macro = word_offset_in_engine % NPUConfig::WORDS_PER_MACRO;
    
    cim_macro_row_idx = word_offset_in_macro / NPUConfig::PAGES_PER_MACRO_ROW;
    page = word_offset_in_macro % NPUConfig::PAGES_PER_MACRO_ROW;
}
```

## 5. 数据流设计

### 5.1 读操作流程

```mermaid
sequenceDiagram
    participant Client
    participant LocalMemory
    participant AddressDecoder
    participant MemoryUnit
    participant DcimCluster
    
    Client->>LocalMemory: TLM Read Request
    LocalMemory->>AddressDecoder: decode(address)
    AddressDecoder-->>LocalMemory: unit_index, word_offset
    
    alt Valid Address
        LocalMemory->>MemoryUnit: read_word(word_offset)
        MemoryUnit-->>LocalMemory: Word256b data
        LocalMemory-->>Client: TLM_OK_RESPONSE
    else Invalid Address
        LocalMemory-->>Client: TLM_ADDRESS_ERROR_RESPONSE
    end
```

### 5.2 写操作流程

```mermaid
sequenceDiagram
    participant Client
    participant LocalMemory
    participant AddressDecoder
    participant MemoryUnit
    participant DcimCluster
    
    Client->>LocalMemory: TLM Write Request
    LocalMemory->>AddressDecoder: decode(address)
    AddressDecoder-->>LocalMemory: unit_index, word_offset
    
    alt Valid Address
        LocalMemory->>MemoryUnit: write_word(word_offset, data)
        
        alt CIMC Unit
            MemoryUnit->>DcimCluster: writeWordLinear(word_offset, data)
            DcimCluster-->>MemoryUnit: sync_success
        end
        
        MemoryUnit-->>LocalMemory: write_success
        LocalMemory-->>Client: TLM_OK_RESPONSE
    else Invalid Address
        LocalMemory-->>Client: TLM_ADDRESS_ERROR_RESPONSE
    end
```

## 6. 并发控制与线程安全

### 6.1 锁机制

#### 6.1.1 读写锁 (shared_mutex)
```cpp
mutable std::shared_mutex access_mutex;
```
- **读操作**: 使用 shared_lock，允许并发读取
- **写操作**: 使用 unique_lock，独占访问
- **范围**: 每个内存单元独立锁控制

#### 6.1.2 原子计数器
```cpp
std::atomic<uint64_t> read_count;
std::atomic<uint64_t> write_count;
```
- 无锁的访问统计
- 线程安全的性能计数

### 6.2 同步策略

#### 6.2.1 SPAD 内存同步
- 纯 SystemC 实现，无需外部同步
- 基于 std::vector<Word256b> 的连续存储

#### 6.2.2 CIMC 内存同步
- SystemC 模型与 C++ 功能模型双向同步
- 写操作时自动触发同步到 C++ 模型
- 失败时发出警告但不阻塞操作

## 7. 配置参数

### 7.1 存储配置

| 参数名称           | 值     | 描述                    |
|-------------------|--------|-------------------------|
| LMEM_WD           | 256    | 本地内存数据宽度 (位)    |
| SPAD_NUM          | 4      | Scratchpad 单元数量     |
| SPAD_DP           | 4096   | 单个 SPAD 深度 (字)     |
| CIMC_NUM          | 1      | CIM Cluster 单元数量    |
| TOTAL_WORDS       | 4224   | CIMC 总字数             |
| TOTAL_MACROS      | 8      | CIMC 宏总数             |
| ROWS_PER_MACRO    | 33     | 每宏行数               |
| PAGES_PER_ROW     | 16     | 每行页数               |

### 7.2 地址配置

| 参数名称           | 值     | 描述                    |
|-------------------|--------|-------------------------|
| LMEM_INDEX        | 3      | 单元索引位宽            |
| LMEM_OFFSET       | 13     | 字偏移位宽 (最大值)     |
| SPAD_ADDR_WD      | 12     | SPAD 地址位宽          |
| CIMC_ADDR_WD      | 13     | CIMC 地址位宽          |

## 8. 接口规范

### 8.1 TLM 事务规范

#### 8.1.1 读事务
```cpp
trans.set_command(tlm::TLM_READ_COMMAND);
trans.set_address(word_aligned_address);
trans.set_data_ptr(word256b_buffer);
trans.set_data_length(32);  // 32 字节
trans.set_streaming_width(32);
```

#### 8.1.2 写事务
```cpp
trans.set_command(tlm::TLM_WRITE_COMMAND);
trans.set_address(word_aligned_address);
trans.set_data_ptr(word256b_data);
trans.set_data_length(32);  // 32 字节
trans.set_streaming_width(32);
```

### 8.2 地址生成函数

```cpp
uint64_t make_address(uint32_t unit_index, uint64_t word_offset) {
    return (unit_index << (NPUConfig::LMEM_OFFSET + 5)) | (word_offset << 5);
}
```

## 9. 错误处理与调试

### 9.1 错误类型

| 错误类型              | 响应状态                    | 描述                |
|----------------------|----------------------------|---------------------|
| 地址未对齐            | TLM_ADDRESS_ERROR_RESPONSE | 地址非32字节对齐     |
| 单元索引越界          | TLM_ADDRESS_ERROR_RESPONSE | 单元索引超出范围     |
| 字偏移越界            | TLM_ADDRESS_ERROR_RESPONSE | 字偏移超出单元范围   |
| 访问操作失败          | TLM_GENERIC_ERROR_RESPONSE | 底层访问失败        |

### 9.2 调试支持

#### 9.2.1 内存转储
```cpp
void dump_memory(const std::string& filename);
```
- 将所有内存单元内容输出到文件
- 包含十六进制数据和结构化信息

#### 9.2.2 统计信息
```cpp
struct Statistics {
    std::atomic<uint64_t> total_reads{0};
    std::atomic<uint64_t> total_writes{0};
    std::atomic<uint64_t> error_accesses{0};
};
```

#### 9.2.3 单元级调试
```cpp
IMemoryUnit* get_memory_unit(uint32_t unit_index);
```
- 提供直接访问内存单元的接口
- 仅用于测试和调试目的

## 10. 性能特性

### 10.1 访问延迟
- **SPAD 访问**: 单周期访问，无额外延迟
- **CIMC 访问**: 包含同步开销，但对外透明
- **TLM 延迟**: 通过 sc_time 参数传递

### 10.2 带宽特性
- **数据宽度**: 256位/周期
- **对齐要求**: 强制32字节对齐
- **并发能力**: 支持多读单写并发模式

### 10.3 容量规格

| 存储类型 | 单元数量 | 单元容量    | 总容量      |
|----------|----------|-------------|-------------|
| SPAD     | 4        | 128 KB      | 512 KB      |
| CIMC     | 1        | 132 KB      | 132 KB      |
| **总计** | **5**    | **-**       | **644 KB**  |

## 11. 使用示例

### 11.1 基本读写操作

```cpp
// 创建本地内存实例
auto local_mem = std::make_unique<LocalMemory>("local_mem");

// 直接访问接口
Word256b write_data, read_data;
fill_pattern(write_data);

// 写入 SPAD 单元 0，字偏移 100
bool write_success = local_mem->write_word(0, 100, write_data);

// 读取 SPAD 单元 0，字偏移 100
bool read_success = local_mem->read_word(0, 100, read_data);
```

### 11.2 TLM 事务创建

```cpp
// 创建读事务
auto* read_trans = local_mem->create_read_trans(0, 100, read_data);

// 创建写事务
auto* write_trans = local_mem->create_write_trans(0, 100, write_data);
```

### 11.3 地址计算

```cpp
// 计算 SPAD 单元 2，字偏移 500 的地址
uint64_t address = make_address(2, 500);

// 解码地址
size_t unit_index;
uint64_t word_offset;
bool valid = decode(address, unit_index, word_offset);
```

## 12. 测试验证

### 12.1 功能测试
- 基本读写操作验证
- 地址解码正确性测试
- 边界条件测试
- 错误处理测试

### 12.2 同步测试
- CIMC 与 C++ 模型同步验证
- 并发访问安全性测试
- 数据一致性验证

### 12.3 性能测试
- 访问延迟测量
- 带宽利用率测试
- 并发性能评估

--- 