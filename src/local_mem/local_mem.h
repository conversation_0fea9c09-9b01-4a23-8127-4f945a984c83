#pragma once

#include <tlm_utils/multi_passthrough_target_socket.h>
#include <array>
#include <atomic>
#include <fstream>
#include <memory>
#include <systemc>
#include <tlm>
#include <vector>
#include "npu_config.h"
// Forward declaration for LocalMemory needed by CIMCluster if we keep that pointer approach,
// but we switched to direct pointer in CIMCluster.
// class LocalMemory;

// Include the full definition of the C++ model cluster
#include "dcim_cluster.hpp"

namespace npu_sc
{

// 256-bit word type (32 bytes)
using Word256b = std::array<uint8_t, NPUConfig::LMEM_WD / 8>;

// Base class for memory units (SPAD and CIMC)
class IMemoryUnit
{
  public:
    virtual bool read_bytes(uint64_t byte_offset, uint8_t* data, size_t len) = 0;
    virtual bool write_bytes(uint64_t byte_offset, const uint8_t* data, size_t len) = 0;
    virtual bool read_word(uint64_t word_offset, Word256b& data) = 0;
    virtual bool write_word(uint64_t word_offset, const Word256b& data) = 0;
    virtual void dump(std::ostream& os) const = 0;
    virtual void reset() = 0;
    virtual ~IMemoryUnit() = default;
};

// Scratchpad memory implementation
class Scratchpad : public IMemoryUnit
{
  public:
    Scratchpad();

    bool read_bytes(uint64_t byte_offset, uint8_t* data, size_t len) override;
    bool write_bytes(uint64_t byte_offset, const uint8_t* data, size_t len) override;
    bool read_word(uint64_t word_offset, Word256b& data) override;
    bool write_word(uint64_t word_offset, const Word256b& data) override;
    void dump(std::ostream& os) const override;
    void reset() override;

  private:
    std::vector<Word256b> memory;
    std::atomic<uint64_t> read_count;
    std::atomic<uint64_t> write_count;

    bool check_byte_bounds(uint64_t byte_offset, size_t len) const;
    bool check_word_bounds(uint64_t word_offset) const;
};

// CIM Cluster memory implementation
class CIMCluster : public IMemoryUnit
{
  public:
    // Note: Addressing modes other than COMPUTE_001 have been removed as per requirements.
    // The concept of modes is removed, assuming COMPUTE_001 structure implicitly.

    CIMCluster();  // Constructor

    // Method to link with the C++ functional model
    void setDcimClusterPtr(DcimCluster* ptr);  // Removed namespace

    // Basic read/write interfaces (inherited from IMemoryUnit)
    bool read_bytes(uint64_t byte_offset, uint8_t* data, size_t len) override;
    bool write_bytes(uint64_t byte_offset, const uint8_t* data, size_t len) override;
    bool read_word(uint64_t word_offset, Word256b& data) override;
    bool write_word(uint64_t word_offset, const Word256b& data) override;

    // Helper function to parse word offset into physical indices
    // word_offset: 0 to 4223 (inclusive)
    // Outputs: cim_engine_idx (0-3), cim_macro_idx_in_engine (0-1),
    //          cim_macro_row_idx (0-32), page (0-15)

    void dump(std::ostream& os) const override;
    void reset() override;

  private:
    // Underlying storage
    std::vector<uint8_t> raw_memory;  // Linear byte array
    std::atomic<uint64_t> read_count;
    std::atomic<uint64_t> write_count;

    DcimCluster* dcim_cluster_ptr;  // Removed namespace // Pointer to the C++ model instance

    // Boundary check
    bool check_byte_bounds(uint64_t byte_offset, size_t len) const;
    // Removed check_3d_bounds
};

// Address decoder
bool decode(uint64_t address, size_t& unit_index, uint64_t& word_offset);
bool parse_word_offset(uint64_t word_offset,
                       uint32_t& cim_engine_idx,
                       uint32_t& cim_macro_idx_in_engine,
                       uint32_t& cim_macro_row_idx,
                       uint32_t& page);
// Main Local Memory module
class LocalMemory : public sc_core::sc_module
{
  public:
    tlm_utils::multi_passthrough_target_socket<LocalMemory> target_socket;

    SC_HAS_PROCESS(LocalMemory);
    // Constructor now takes the C++ model pointer (can be null if sync not needed)
    LocalMemory(sc_core::sc_module_name name, DcimCluster* dcim_ptr = nullptr);

    // Pointer to the corresponding C-model cluster for synchronization
    DcimCluster* dcim_cluster_ptr = nullptr;  // Keep the member variable
    // Remove the setter declaration

    // Direct memory unit access methods (for both SPAD and CIMC)
    bool read_word(uint32_t unit_index, uint64_t word_offset, Word256b& data);
    bool write_word(uint32_t unit_index, uint64_t word_offset, const Word256b& data);

    // Bulk memory access methods - can read/write entire memory units
    bool bulk_read_bytes(uint32_t unit_index, uint64_t byte_offset, uint8_t* data, size_t len);
    bool bulk_write_bytes(uint32_t unit_index, uint64_t byte_offset, const uint8_t* data, size_t len);
    bool bulk_read_words(uint32_t unit_index, uint64_t word_offset, Word256b* data, size_t word_count);
    bool bulk_write_words(uint32_t unit_index, uint64_t word_offset, const Word256b* data, size_t word_count);

    // Convenience methods to read/write entire memory units
    bool read_entire_unit(uint32_t unit_index, std::vector<uint8_t>& data);
    bool write_entire_unit(uint32_t unit_index, const std::vector<uint8_t>& data);

    // Utility methods to get memory unit information
    size_t get_unit_size_bytes(uint32_t unit_index) const;
    size_t get_unit_size_words(uint32_t unit_index) const;
    bool is_spad_unit(uint32_t unit_index) const;
    bool is_cim_unit(uint32_t unit_index) const;

    // Helper methods for TLM transaction creation
    tlm::tlm_generic_payload* create_read_trans(uint32_t unit_index,
                                                uint64_t word_offset,
                                                Word256b& data);
    tlm::tlm_generic_payload* create_write_trans(uint32_t unit_index,
                                                 uint64_t word_offset,
                                                 const Word256b& data);

    // Debug and statistics methods
    void dump_memory(const std::string& filename);
    void print_statistics() const;

    // Get memory unit (for testing only)
    IMemoryUnit* get_memory_unit(uint32_t unit_index)
    {
        if (unit_index >= memory_units.size())
            return nullptr;
        return memory_units[unit_index].get();
    }

    void reset();

  private:
    std::vector<std::unique_ptr<IMemoryUnit>> memory_units;

    struct Statistics
    {
        std::atomic<uint64_t> total_reads{0};
        std::atomic<uint64_t> total_writes{0};
        std::atomic<uint64_t> error_accesses{0};
    } stats;

    // TLM interface method
    void b_transport_lmem(int id, tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
};

// Helper method to convert unit_index and word_offset to address
uint64_t make_address(uint32_t unit_index, uint64_t word_offset);
}  // namespace npu_sc
