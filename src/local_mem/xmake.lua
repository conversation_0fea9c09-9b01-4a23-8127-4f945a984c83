target("local_mem")
    set_kind("static")
    set_group("local_mem")
    add_files("local_mem.cpp")
    add_includedirs(".",{public = true})
    add_deps("common")
    add_deps("dcim_cluster")
    add_deps("sc_logger")
    add_packages("spdlog")
target("test_lmem")
    set_kind("binary")
    add_includedirs("test")
    add_files("test/test_lmem.cpp")
    add_headerfiles("test/test_lmem.h")
    add_deps("local_mem")
    add_tests("test_lmem", {group = "local_mem"})
    add_packages("spdlog")
    add_deps("sc_logger")
includes("../cim_cluster/xmake.lua")
    
