#include "test_lmem.h"
#include <cstring>
#include <vector>
#include <algorithm>
#include "utils/sc_logger.h"

namespace npu_sc
{

// 基本功能测试 - SPAD直接访问
void LocalMemoryTest::BasicSpadAccess()
{
    Word256b write_data, read_data;
    fill_word256b_pattern(write_data);

    // Test SPAD0
    EXPECT_TRUE(local_mem->write_word(0, 0, write_data), "SPAD0 write failed");
    EXPECT_TRUE(local_mem->read_word(0, 0, read_data), "SPAD0 read failed");
    EXPECT_TRUE(compare_word256b(write_data, read_data), "SPAD0 data mismatch");

    // Test last SPAD
    EXPECT_TRUE(local_mem->write_word(NPUConfig::SPAD_NUM - 1, 0, write_data), "Last SPAD write failed");
    EXPECT_TRUE(local_mem->read_word(NPUConfig::SPAD_NUM - 1, 0, read_data), "Last SPAD read failed");
    EXPECT_TRUE(compare_word256b(write_data, read_data), "Last SPAD data mismatch");
}

// 基本功能测试 - 通用接口
void LocalMemoryTest::BasicMemoryUnitAccess()
{
    Word256b write_data, read_data;
    fill_word256b_pattern(write_data);

    // Test SPAD through generic interface
    EXPECT_TRUE(local_mem->write_word(0, 0, write_data), "Generic SPAD write failed");
    EXPECT_TRUE(local_mem->read_word(0, 0, read_data), "Generic SPAD read failed");
    EXPECT_TRUE(compare_word256b(write_data, read_data), "Generic SPAD data mismatch");

    // Test CIMC through generic interface
    EXPECT_TRUE(local_mem->write_word(NPUConfig::SPAD_NUM, 0, write_data), "CIMC write failed");
    EXPECT_TRUE(local_mem->read_word(NPUConfig::SPAD_NUM, 0, read_data), "CIMC read failed");
    EXPECT_TRUE(compare_word256b(write_data, read_data), "CIMC data mismatch");
}

// 边界条件测试
void LocalMemoryTest::BoundaryConditions()
{
    Word256b data;
    fill_word256b_pattern(data);

    // Test last valid word offset
    EXPECT_TRUE(local_mem->write_word(0, NPUConfig::SPAD_DP - 1, data), "Last valid offset write failed");
    EXPECT_TRUE(local_mem->read_word(0, NPUConfig::SPAD_DP - 1, data), "Last valid offset read failed");

    // Test invalid word offset
    EXPECT_FALSE(local_mem->write_word(0, NPUConfig::SPAD_DP, data), "Invalid offset write should fail");
    EXPECT_FALSE(local_mem->read_word(0, NPUConfig::SPAD_DP, data), "Invalid offset read should fail");
}

// CIMC Basic Read/Write and Synchronization Test
void LocalMemoryTest::CIMCSyncTest()
{
    Word256b write_data, read_data_sc;
    uint8_t read_buffer_cpp[NPUConfig::LMEM_WD / 8];  // Buffer for C++ model read
    fill_word256b_pattern(write_data);

    // Get the CIMC unit index (assuming it's the first unit after SPADs)
    uint32_t cimc_unit_index = NPUConfig::SPAD_NUM;

    // Test basic read/write via LocalMemory interface
    uint64_t test_word_offset = 10;  // Choose an arbitrary offset
    EXPECT_TRUE(local_mem->write_word(cimc_unit_index, test_word_offset, write_data), "CIMC write failed");
    EXPECT_TRUE(local_mem->read_word(cimc_unit_index, test_word_offset, read_data_sc), "CIMC read failed");
    EXPECT_TRUE(compare_word256b(write_data, read_data_sc), "CIMC data mismatch");

    // Test Synchronization: Read from C++ model after writing via SystemC model
    ASSERT_NE(dcim_cluster.get(), static_cast<DcimCluster*>(nullptr), "C++ model pointer is null");
    memset(read_buffer_cpp, 0, sizeof(read_buffer_cpp));  // Clear C++ buffer
    EXPECT_TRUE(dcim_cluster->readWordLinear(test_word_offset, read_buffer_cpp), "C++ model read failed");

    // Compare data written by SystemC model with data read from C++ model
    EXPECT_EQ(std::memcmp(write_data.data(), read_buffer_cpp, NPUConfig::LMEM_WD / 8), 0, "Sync data mismatch");

    // Test writing to a different offset
    test_word_offset = NPUConfig::TOTAL_WORDS - 1;  // Test last word
    Word256b write_data2;
    fill_word256b(write_data2, 0xAA);
    EXPECT_TRUE(local_mem->write_word(cimc_unit_index, test_word_offset, write_data2), "Last word write failed");
    memset(read_buffer_cpp, 0, sizeof(read_buffer_cpp));
    EXPECT_TRUE(dcim_cluster->readWordLinear(test_word_offset, read_buffer_cpp), "Last word C++ read failed");
    EXPECT_EQ(std::memcmp(write_data2.data(), read_buffer_cpp, NPUConfig::LMEM_WD / 8), 0, "Last word sync mismatch");

    // Test Bounds Checking (using LocalMemory interface)
    EXPECT_FALSE(local_mem->write_word(cimc_unit_index, NPUConfig::TOTAL_WORDS, write_data), "Out of bounds write should fail");
    EXPECT_FALSE(local_mem->read_word(cimc_unit_index, NPUConfig::TOTAL_WORDS, read_data_sc), "Out of bounds read should fail");
}

// Data Consistency Test
void LocalMemoryTest::DataConsistency()
{
    std::vector<Word256b> test_patterns;

    // Create multiple test patterns
    for (int i = 0; i < 5; i++)
    {
        Word256b pattern;
        fill_word256b(pattern, static_cast<uint8_t>(i));
        test_patterns.push_back(pattern);
    }

    // Write patterns to different locations
    for (size_t i = 0; i < test_patterns.size(); i++)
    {
        EXPECT_TRUE(local_mem->write_word(0, i, test_patterns[i]), "Pattern write failed");
    }

    // Read back and verify
    Word256b read_data;
    for (size_t i = 0; i < test_patterns.size(); i++)
    {
        EXPECT_TRUE(local_mem->read_word(0, i, read_data), "Pattern read failed");
        EXPECT_TRUE(compare_word256b(test_patterns[i], read_data), "Pattern data mismatch");
    }
}

// Bulk字节操作测试
void LocalMemoryTest::BulkByteOperationsTest()
{
    const size_t test_size = 1024; // 1KB测试数据
    std::vector<uint8_t> write_data(test_size);
    std::vector<uint8_t> read_data(test_size);
    
    // 初始化测试数据
    for (size_t i = 0; i < test_size; ++i) {
        write_data[i] = static_cast<uint8_t>(i & 0xFF);
    }
    
    // 测试SPAD单元的bulk字节写入
    EXPECT_TRUE(local_mem->bulk_write_bytes(0, 0, write_data.data(), test_size), 
                "SPAD bulk byte write failed");
    
    // 测试SPAD单元的bulk字节读取
    EXPECT_TRUE(local_mem->bulk_read_bytes(0, 0, read_data.data(), test_size), 
                "SPAD bulk byte read failed");
    
    // 验证数据完整性
    EXPECT_EQ(std::memcmp(write_data.data(), read_data.data(), test_size), 0, 
              "SPAD bulk byte data mismatch");
    
    // 测试CIM单元的bulk字节操作
    uint32_t cim_unit = NPUConfig::SPAD_NUM;
    const size_t cim_test_size = 2048; // 2KB测试数据
    std::vector<uint8_t> cim_write_data(cim_test_size, 0xAB);
    std::vector<uint8_t> cim_read_data(cim_test_size);
    
    EXPECT_TRUE(local_mem->bulk_write_bytes(cim_unit, 0, cim_write_data.data(), cim_test_size), 
                "CIM bulk byte write failed");
    EXPECT_TRUE(local_mem->bulk_read_bytes(cim_unit, 0, cim_read_data.data(), cim_test_size), 
                "CIM bulk byte read failed");
    EXPECT_EQ(std::memcmp(cim_write_data.data(), cim_read_data.data(), cim_test_size), 0, 
              "CIM bulk byte data mismatch");
    
    // 测试偏移写入
    const size_t offset = 512;
    std::fill(write_data.begin(), write_data.end(), 0x55);
    EXPECT_TRUE(local_mem->bulk_write_bytes(0, offset, write_data.data(), test_size), 
                "SPAD bulk byte write with offset failed");
    EXPECT_TRUE(local_mem->bulk_read_bytes(0, offset, read_data.data(), test_size), 
                "SPAD bulk byte read with offset failed");
    EXPECT_EQ(std::memcmp(write_data.data(), read_data.data(), test_size), 0, 
              "SPAD bulk byte data with offset mismatch");
}

// Bulk字操作测试
void LocalMemoryTest::BulkWordOperationsTest()
{
    const size_t word_count = 20; // 20个256位字
    std::vector<Word256b> write_words(word_count);
    std::vector<Word256b> read_words(word_count);
    
    // 初始化测试字数据
    for (size_t w = 0; w < word_count; ++w) {
        for (size_t b = 0; b < write_words[w].size(); ++b) {
            write_words[w][b] = static_cast<uint8_t>((w * 32 + b) & 0xFF);
        }
    }
    
    // 测试SPAD单元的bulk字写入
    EXPECT_TRUE(local_mem->bulk_write_words(0, 0, write_words.data(), word_count), 
                "SPAD bulk word write failed");
    
    // 测试SPAD单元的bulk字读取
    EXPECT_TRUE(local_mem->bulk_read_words(0, 0, read_words.data(), word_count), 
                "SPAD bulk word read failed");
    
    // 验证字数据完整性
    bool words_match = true;
    for (size_t w = 0; w < word_count; ++w) {
        if (!compare_word256b(write_words[w], read_words[w])) {
            words_match = false;
            break;
        }
    }
    EXPECT_TRUE(words_match, "SPAD bulk word data mismatch");
    
    // 测试CIM单元的bulk字操作
    uint32_t cim_unit = NPUConfig::SPAD_NUM;
    const size_t cim_word_count = 10;
    std::vector<Word256b> cim_write_words(cim_word_count);
    std::vector<Word256b> cim_read_words(cim_word_count);
    
    // 初始化CIM测试数据
    for (size_t w = 0; w < cim_word_count; ++w) {
        fill_word256b(cim_write_words[w], static_cast<uint8_t>(0xCC + w));
    }
    
    EXPECT_TRUE(local_mem->bulk_write_words(cim_unit, 100, cim_write_words.data(), cim_word_count), 
                "CIM bulk word write failed");
    EXPECT_TRUE(local_mem->bulk_read_words(cim_unit, 100, cim_read_words.data(), cim_word_count), 
                "CIM bulk word read failed");
    
    words_match = true;
    for (size_t w = 0; w < cim_word_count; ++w) {
        if (!compare_word256b(cim_write_words[w], cim_read_words[w])) {
            words_match = false;
            break;
        }
    }
    EXPECT_TRUE(words_match, "CIM bulk word data mismatch");
}

// 整个内存单元操作测试
void LocalMemoryTest::EntireUnitOperationsTest()
{
    // 测试SPAD整个单元读写
    uint32_t spad_unit = 0;
    
    // 获取SPAD单元大小
    size_t spad_size = local_mem->get_unit_size_bytes(spad_unit);
    EXPECT_TRUE(spad_size == NPUConfig::SPAD_NUM_BYTES, "SPAD size mismatch");
    
    // 创建测试数据
    std::vector<uint8_t> spad_write_data(spad_size);
    for (size_t i = 0; i < spad_size; ++i) {
        spad_write_data[i] = static_cast<uint8_t>((i * 3) & 0xFF); // 简单模式
    }
    
    // 写入整个SPAD单元
    EXPECT_TRUE(local_mem->write_entire_unit(spad_unit, spad_write_data), 
                "Write entire SPAD unit failed");
    
    // 读回整个SPAD单元
    std::vector<uint8_t> spad_read_data;
    EXPECT_TRUE(local_mem->read_entire_unit(spad_unit, spad_read_data), 
                "Read entire SPAD unit failed");
    
    // 验证数据大小
    EXPECT_TRUE(spad_read_data.size() == spad_size, "SPAD read data size mismatch");
    
    // 验证数据内容
    EXPECT_EQ(std::memcmp(spad_write_data.data(), spad_read_data.data(), spad_size), 0, 
              "Entire SPAD unit data mismatch");
    
    // 测试CIM整个单元读写（使用较小的数据量进行测试以节省时间）
    uint32_t cim_unit = NPUConfig::SPAD_NUM;
    
    // 获取CIM单元大小
    size_t cim_size = local_mem->get_unit_size_bytes(cim_unit);
    EXPECT_TRUE(cim_size == NPUConfig::TOTAL_BYTES, "CIM size mismatch");
    
    // 为了测试效率，只测试部分CIM数据
    const size_t cim_test_size = 8192; // 8KB测试数据
    std::vector<uint8_t> cim_test_data(cim_test_size, 0xDD);
    
    EXPECT_TRUE(local_mem->bulk_write_bytes(cim_unit, 0, cim_test_data.data(), cim_test_size), 
                "CIM partial write failed");
    
    std::vector<uint8_t> cim_read_test_data(cim_test_size);
    EXPECT_TRUE(local_mem->bulk_read_bytes(cim_unit, 0, cim_read_test_data.data(), cim_test_size), 
                "CIM partial read failed");
    
    EXPECT_EQ(std::memcmp(cim_test_data.data(), cim_read_test_data.data(), cim_test_size), 0, 
              "CIM partial data mismatch");
}

// 内存单元信息测试
void LocalMemoryTest::MemoryUnitInfoTest()
{
    // 测试SPAD单元信息
    for (uint32_t i = 0; i < NPUConfig::SPAD_NUM; ++i) {
        EXPECT_TRUE(local_mem->is_spad_unit(i), "SPAD unit identification failed");
        EXPECT_FALSE(local_mem->is_cim_unit(i), "SPAD unit misidentified as CIM");
        
        size_t bytes = local_mem->get_unit_size_bytes(i);
        size_t words = local_mem->get_unit_size_words(i);
        
        EXPECT_TRUE(bytes == NPUConfig::SPAD_NUM_BYTES, "SPAD byte size incorrect");
        EXPECT_TRUE(words == NPUConfig::SPAD_DP, "SPAD word count incorrect");
    }
    
    // 测试CIM单元信息
    uint32_t cim_unit = NPUConfig::SPAD_NUM;
    EXPECT_TRUE(local_mem->is_cim_unit(cim_unit), "CIM unit identification failed");
    EXPECT_FALSE(local_mem->is_spad_unit(cim_unit), "CIM unit misidentified as SPAD");
    
    size_t cim_bytes = local_mem->get_unit_size_bytes(cim_unit);
    size_t cim_words = local_mem->get_unit_size_words(cim_unit);
    
    EXPECT_TRUE(cim_bytes == NPUConfig::TOTAL_BYTES, "CIM byte size incorrect");
    EXPECT_TRUE(cim_words == NPUConfig::TOTAL_WORDS, "CIM word count incorrect");
    
    // 测试无效单元索引
    uint32_t invalid_unit = NPUConfig::SPAD_NUM + NPUConfig::CIMC_NUM;
    EXPECT_TRUE(local_mem->get_unit_size_bytes(invalid_unit) == 0, "Invalid unit should return 0 bytes");
    EXPECT_TRUE(local_mem->get_unit_size_words(invalid_unit) == 0, "Invalid unit should return 0 words");
    EXPECT_FALSE(local_mem->is_spad_unit(invalid_unit), "Invalid unit should not be SPAD");
    EXPECT_FALSE(local_mem->is_cim_unit(invalid_unit), "Invalid unit should not be CIM");
}

// 改进的TLM接口测试
void LocalMemoryTest::ImprovedTLMInterfaceTest()
{
    // 测试不同长度的数据传输，验证改进后的TLM接口能处理任意长度
    std::vector<size_t> test_lengths = {32, 64, 128, 256, 512, 1024, 2048}; // 各种长度
    
    for (size_t len : test_lengths) {
        std::vector<uint8_t> write_data(len);
        std::vector<uint8_t> read_data(len);
        
        // 初始化测试数据
        for (size_t i = 0; i < len; ++i) {
            write_data[i] = static_cast<uint8_t>((i + len) & 0xFF);
        }
        
        // 直接调用LocalMemory的bulk接口来验证TLM接口的改进
        // 这模拟了改进后的b_transport_lmem内部调用bulk操作的行为
        
        // 使用bulk操作测试不同长度的数据传输
        EXPECT_TRUE(local_mem->bulk_write_bytes(0, 0, write_data.data(), len), 
                    "Variable length write failed");
        
        EXPECT_TRUE(local_mem->bulk_read_bytes(0, 0, read_data.data(), len), 
                    "Variable length read failed");
        
        // 验证数据完整性
        EXPECT_EQ(std::memcmp(write_data.data(), read_data.data(), len), 0, 
                  "Variable length data mismatch");
        
        // 测试偏移地址的传输
        const size_t offset = 1024;
        std::fill(write_data.begin(), write_data.end(), static_cast<uint8_t>(len & 0xFF));
        
        EXPECT_TRUE(local_mem->bulk_write_bytes(0, offset, write_data.data(), len), 
                    "Variable length write with offset failed");
        
        EXPECT_TRUE(local_mem->bulk_read_bytes(0, offset, read_data.data(), len), 
                    "Variable length read with offset failed");
        
        EXPECT_EQ(std::memcmp(write_data.data(), read_data.data(), len), 0, 
                  "Variable length data with offset mismatch");
    }
}



}  // namespace npu_sc

int sc_main(int argc, char* argv[])
{
    sc_logger::initialize(spdlog::level::debug, "test_lmem.log");

    npu_sc::LocalMemoryTest test;
    bool all_passed = test.RunAllTests();
    
    std::cout << "\n=== Test Summary ===" << std::endl;
    std::cout << "Result: " << (all_passed ? "ALL TESTS PASSED" : "SOME TESTS FAILED") << std::endl;
    sc_logger::print_id_statistics();
    return all_passed ? 0 : 1;
}