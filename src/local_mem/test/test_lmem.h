#pragma once

#include <iostream>
#include <cassert>
#include <cstring>
#include <systemc>
#include "local_mem.h"
// Include the C++ model header
#include "dcim_cluster.hpp"

namespace npu_sc
{

class LocalMemoryTest
{
  protected:
    void SetUp()
    {
        // Create the C++ model instance first
        dcim_cluster.reset(new DcimCluster());
        // Create the SystemC model instance, passing the C++ model pointer
        local_mem.reset(new LocalMemory("local_mem", dcim_cluster.get()));
    }

    void TearDown()
    {
        local_mem.reset();
        dcim_cluster.reset();  // Reset the C++ model instance
    }

    // Helper methods
    bool compare_word256b(const Word256b& a, const Word256b& b)
    {
        return std::memcmp(a.data(), b.data(), NPUConfig::LMEM_WD / 8) == 0;
    }

    void fill_word256b(Word256b& word, uint8_t value)
    {
        std::fill(word.begin(), word.end(), value);
    }

    void fill_word256b_pattern(Word256b& word)
    {
        for (size_t i = 0; i < word.size(); i++)
        {
            word[i] = static_cast<uint8_t>(i);
        }
    }

    // Simple assertion macros
    void EXPECT_TRUE(bool condition, const char* message = "")
    {
        if (!condition)
        {
            std::cerr << "EXPECT_TRUE failed: " << message << std::endl;
            test_failed = true;
        }
    }

    void EXPECT_FALSE(bool condition, const char* message = "")
    {
        if (condition)
        {
            std::cerr << "EXPECT_FALSE failed: " << message << std::endl;
            test_failed = true;
        }
    }

    void EXPECT_EQ(int a, int b, const char* message = "")
    {
        if (a != b)
        {
            std::cerr << "EXPECT_EQ failed: " << a << " != " << b << " " << message << std::endl;
            test_failed = true;
        }
    }

    void ASSERT_NE(void* ptr, void* null_ptr, const char* message = "")
    {
        if (ptr == null_ptr)
        {
            std::cerr << "ASSERT_NE failed: pointer is null " << message << std::endl;
            test_failed = true;
            throw std::runtime_error("Assertion failed");
        }
    }

  public:
    // Test methods
    void BasicSpadAccess();
    void BasicMemoryUnitAccess();
    void BoundaryConditions();
    void CIMCSyncTest();
    void DataConsistency();

    // New bulk operation tests
    void BulkByteOperationsTest();
    void BulkWordOperationsTest();
    void EntireUnitOperationsTest();
    void MemoryUnitInfoTest();
    void ImprovedTLMInterfaceTest();

    // Run all tests
    bool RunAllTests()
    {
        test_failed = false;
        std::cout << "Running LocalMemory tests..." << std::endl;

        SetUp();
        try
        {
            bool current_test_failed;
            
            current_test_failed = test_failed;
            BasicSpadAccess();
            std::cout << "BasicSpadAccess: " << ((test_failed && !current_test_failed) ? "FAILED" : "PASSED") << std::endl;

            current_test_failed = test_failed;
            BasicMemoryUnitAccess();
            std::cout << "BasicMemoryUnitAccess: " << ((test_failed && !current_test_failed) ? "FAILED" : "PASSED") << std::endl;

            current_test_failed = test_failed;
            BoundaryConditions();
            std::cout << "BoundaryConditions: " << ((test_failed && !current_test_failed) ? "FAILED" : "PASSED") << std::endl;

            current_test_failed = test_failed;
            CIMCSyncTest();
            std::cout << "CIMCSyncTest: " << ((test_failed && !current_test_failed) ? "FAILED" : "PASSED") << std::endl;

            current_test_failed = test_failed;
            DataConsistency();
            std::cout << "DataConsistency: " << ((test_failed && !current_test_failed) ? "FAILED" : "PASSED") << std::endl;

            // New bulk operation tests
            current_test_failed = test_failed;
            BulkByteOperationsTest();
            std::cout << "BulkByteOperationsTest: " << ((test_failed && !current_test_failed) ? "FAILED" : "PASSED") << std::endl;

            current_test_failed = test_failed;
            BulkWordOperationsTest();
            std::cout << "BulkWordOperationsTest: " << ((test_failed && !current_test_failed) ? "FAILED" : "PASSED") << std::endl;

            current_test_failed = test_failed;
            EntireUnitOperationsTest();
            std::cout << "EntireUnitOperationsTest: " << ((test_failed && !current_test_failed) ? "FAILED" : "PASSED") << std::endl;

            current_test_failed = test_failed;
            MemoryUnitInfoTest();
            std::cout << "MemoryUnitInfoTest: " << ((test_failed && !current_test_failed) ? "FAILED" : "PASSED") << std::endl;

            current_test_failed = test_failed;
            ImprovedTLMInterfaceTest();
            std::cout << "ImprovedTLMInterfaceTest: " << ((test_failed && !current_test_failed) ? "FAILED" : "PASSED") << std::endl;
        }
        catch (const std::exception& e)
        {
            std::cerr << "Test exception: " << e.what() << std::endl;
            test_failed = true;
        }
        TearDown();

        std::cout << "All tests " << (test_failed ? "FAILED" : "PASSED") << std::endl;
        return !test_failed;
    }

  private:
    std::unique_ptr<LocalMemory> local_mem;
    std::unique_ptr<DcimCluster> dcim_cluster;  // Add C++ model instance
    sc_core::sc_time delay;
    bool test_failed;
};

}  // namespace npu_sc
