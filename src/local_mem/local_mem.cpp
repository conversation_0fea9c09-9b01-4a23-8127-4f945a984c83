#include "dcim_cluster.hpp"

#include <cstdint>
#include <cstdio>
#include <cstring>
#include <iomanip>
#include <ostream>  // Added for dump functions
#include <sstream>
#include "local_mem.h"
#include "npu_config.h"
#include "utils/sc_logger.h"
namespace npu_sc
{

// Scratchpad implementation
Scratchpad::Scratchpad() : memory(NPUConfig::SPAD_DP), read_count(0), write_count(0)
{
    SC_INFO("SPAD_INIT", "Scratchpad initialized: capacity={} words, total_bytes={}", 
               NPUConfig::SPAD_DP, NPUConfig::SPAD_NUM_BYTES);
}

bool Scratchpad::check_byte_bounds(uint64_t byte_offset, size_t len) const
{
    bool valid = (byte_offset + len) <= NPUConfig::SPAD_NUM_BYTES;
    if (!valid) {
        SC_ERROR("SPAD_BOUNDS", "Scratchpad byte bounds check failed: offset={}, len={}, max_bytes={}", 
                    byte_offset, len, NPUConfig::SPAD_NUM_BYTES);
    }
    return valid;
}

bool Scratchpad::check_word_bounds(uint64_t word_offset) const
{
    bool valid = word_offset < NPUConfig::SPAD_DP;
    if (!valid) {
        SC_ERROR("SPAD_BOUNDS", "Scratchpad word bounds check failed: offset={}, max_words={}", 
                    word_offset, NPUConfig::SPAD_DP);
    }
    return valid;
}

bool Scratchpad::read_bytes(uint64_t byte_offset, uint8_t* data, size_t len)
{
    if (!check_byte_bounds(byte_offset, len))
    {
        return false;
    }

    uint64_t word_idx = byte_offset / (NPUConfig::LMEM_WD / 8);
    uint64_t byte_idx = byte_offset % (NPUConfig::LMEM_WD / 8);

    SC_INFO("SPAD_READ", "Scratchpad read_bytes: offset={}, len={}, start_word={}, start_byte={}", 
                byte_offset, len, word_idx, byte_idx);

    size_t bytes_copied = 0;
    while (bytes_copied < len)
    {
        const Word256b& word = memory[word_idx];
        size_t copy_size = std::min(len - bytes_copied, NPUConfig::LMEM_WD / 8 - byte_idx);
        std::memcpy(data + bytes_copied, word.data() + byte_idx, copy_size);

        bytes_copied += copy_size;
        word_idx++;
        byte_idx = 0;
    }

    read_count++;
    SC_INFO("SPAD_READ_OK", "Scratchpad read completed: bytes_copied={}, total_reads={}", 
                bytes_copied, read_count.load());
    return true;
}

bool Scratchpad::write_bytes(uint64_t byte_offset, const uint8_t* data, size_t len)
{
    if (!check_byte_bounds(byte_offset, len))
    {
        return false;
    }

    uint64_t word_idx = byte_offset / (NPUConfig::LMEM_WD / 8);
    uint64_t byte_idx = byte_offset % (NPUConfig::LMEM_WD / 8);

    SC_INFO("SPAD_WRITE", "Scratchpad write_bytes: offset={}, len={}, start_word={}, start_byte={}", 
                byte_offset, len, word_idx, byte_idx);

    size_t bytes_copied = 0;
    while (bytes_copied < len)
    {
        Word256b& word = memory[word_idx];
        size_t copy_size = std::min(len - bytes_copied, NPUConfig::LMEM_WD / 8 - byte_idx);
        std::memcpy(word.data() + byte_idx, data + bytes_copied, copy_size);

        bytes_copied += copy_size;
        word_idx++;
        byte_idx = 0;
    }

    write_count++;
    SC_INFO("SPAD_WRITE_OK", "Scratchpad write completed: bytes_copied={}, total_writes={}", 
                bytes_copied, write_count.load());
    return true;
}

bool Scratchpad::read_word(uint64_t word_offset, Word256b& data)
{
    if (!check_word_bounds(word_offset))
    {
        return false;
    }

    data = memory[word_offset];
    read_count++;
    SC_INFO("SPAD_READ_WORD", "Scratchpad read_word: offset={}, total_reads={}", 
                word_offset, read_count.load());
    return true;
}

bool Scratchpad::write_word(uint64_t word_offset, const Word256b& data)
{
    if (!check_word_bounds(word_offset))
    {
        return false;
    }

    memory[word_offset] = data;
    write_count++;
    SC_INFO("SPAD_WRITE_WORD", "Scratchpad write_word: offset={}, total_writes={}", 
                word_offset, write_count.load());
    return true;
}

void Scratchpad::dump(std::ostream& os) const
{
    SC_INFO("SPAD_DUMP", "Dumping Scratchpad memory: total_words={}, reads={}, writes={}", 
               memory.size(), read_count.load(), write_count.load());
    
    os << "Scratchpad Memory Contents:\n";
    os << "Total Reads: " << read_count.load() << "\n";
    os << "Total Writes: " << write_count.load() << "\n";

    for (size_t i = 0; i < memory.size(); i++)
    {
        os << "Word " << std::setw(4) << i << ": ";
        const auto& word = memory[i];
        for (uint8_t byte : word)
        {
            os << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte) << " ";
        }
        os << std::dec << "\n";
    }
}

void Scratchpad::reset()
{
    // 清空内存
    std::memset(memory.data(), 0, memory.size() * sizeof(Word256b));
    read_count = 0;
    write_count = 0;
    SC_INFO("SPAD_RESET", "Scratchpad reset completed");
}

// CIMCluster implementation
// Constructor uses the constants defined in the header for the new size
CIMCluster::CIMCluster()
    : raw_memory(NPUConfig::TOTAL_BYTES),  // Use the calculated total bytes
      read_count(0),
      write_count(0),
      dcim_cluster_ptr(nullptr)
{
    SC_INFO("CIM_INIT", "CIMCluster initialized: total_bytes={}, total_words={}, engines={}, macros_per_engine={}", 
               NPUConfig::TOTAL_BYTES, NPUConfig::TOTAL_WORDS, NPUConfig::TOTAL_ENGINES, NPUConfig::MACROS_PER_ENGINE);
}  // Initialize pointer to null

void CIMCluster::setDcimClusterPtr(DcimCluster* ptr)
{  // Removed namespace
    dcim_cluster_ptr = ptr;
    if (ptr) {
        SC_INFO("CIM_SYNC", "CIMCluster synchronization enabled with C++ model");
    } else {
        SC_WARN("CIM_SYNC", "CIMCluster synchronization disabled - no C++ model pointer");
    }
}

bool parse_word_offset(uint64_t word_offset,
                       uint32_t& cim_engine_idx,
                       uint32_t& cim_macro_idx_in_engine,
                       uint32_t& cim_macro_row_idx,
                       uint32_t& page)
{
    // Check if word_offset is within the valid range [0, TOTAL_WORDS - 1]
    if (word_offset >= NPUConfig::TOTAL_WORDS)
    {
        SC_ERROR("CIM_PARSE_OFFSET", "Word offset out of bounds: offset={}, max_words={}", 
                    word_offset, NPUConfig::TOTAL_WORDS - 1);
        return false;
    }

    cim_engine_idx = word_offset / NPUConfig::WORDS_PER_ENGINE;
    uint64_t word_offset_in_engine = word_offset % NPUConfig::WORDS_PER_ENGINE;

    cim_macro_idx_in_engine = word_offset_in_engine / NPUConfig::WORDS_PER_MACRO;
    uint64_t word_offset_in_macro = word_offset_in_engine % NPUConfig::WORDS_PER_MACRO;

    cim_macro_row_idx = word_offset_in_macro / NPUConfig::PAGES_PER_MACRO_ROW;
    page = word_offset_in_macro % NPUConfig::PAGES_PER_MACRO_ROW;

    SC_INFO("CIM_PARSE_OFFSET", "Parsed word offset: offset={} -> engine={}, macro={}, row={}, page={}", 
                word_offset, cim_engine_idx, cim_macro_idx_in_engine, cim_macro_row_idx, page);

    return true;
}

bool CIMCluster::check_byte_bounds(uint64_t byte_offset, size_t len) const
{
    bool valid = (byte_offset + len) <= NPUConfig::TOTAL_BYTES;
    if (!valid) {
        SC_ERROR("CIM_BOUNDS", "CIMCluster byte bounds check failed: offset={}, len={}, max_bytes={}", 
                    byte_offset, len, NPUConfig::TOTAL_BYTES);
    }
    return valid;
}

bool CIMCluster::read_bytes(uint64_t byte_offset, uint8_t* data, size_t len)
{
    if (!check_byte_bounds(byte_offset, len))
    {
        return false;
    }

    std::memcpy(data, raw_memory.data() + byte_offset, len);
    read_count++;
    SC_INFO("CIM_READ_BYTES", "CIMCluster read_bytes: offset={}, len={}, total_reads={}", 
                byte_offset, len, read_count.load());
    return true;
}

bool CIMCluster::write_bytes(uint64_t byte_offset, const uint8_t* data, size_t len)
{
    if (!check_byte_bounds(byte_offset, len))
    {
        return false;
    }

    std::memcpy(raw_memory.data() + byte_offset, data, len);
    write_count++;
    SC_INFO("CIM_WRITE_BYTES", "CIMCluster write_bytes: offset={}, len={}, total_writes={}", 
                byte_offset, len, write_count.load());
    return true;
}

bool CIMCluster::read_word(uint64_t word_offset, Word256b& data)
{
    // Check word offset bounds based on TOTAL_WORDS
    if (word_offset >= NPUConfig::TOTAL_WORDS)
    {
        SC_ERROR("CIM_BOUNDS", "CIMCluster read word offset out of bounds: offset={}, max_words={}", 
                    word_offset, NPUConfig::TOTAL_WORDS);
        return false;
    }
    return read_bytes(word_offset * (NPUConfig::LMEM_WD / 8),
                      reinterpret_cast<uint8_t*>(data.data()),
                      NPUConfig::LMEM_WD / 8);
}

bool CIMCluster::write_word(uint64_t word_offset, const Word256b& data)
{
    // Check word offset bounds based on TOTAL_WORDS
    if (word_offset >= NPUConfig::TOTAL_WORDS)
    {
        SC_ERROR("CIM_BOUNDS", "CIMCluster write word offset out of bounds: offset={}, max_words={}", 
                    word_offset, NPUConfig::TOTAL_WORDS);
        return false;
    }
    bool success = write_bytes(word_offset * (NPUConfig::LMEM_WD / 8),
                               reinterpret_cast<const uint8_t*>(data.data()),
                               NPUConfig::LMEM_WD / 8);

    // Synchronization: If local write succeeded and pointer is set, write to C++ model
    if (success && dcim_cluster_ptr)
    {
        // Use the linear write function of the C++ model cluster
        bool sync_success = dcim_cluster_ptr->writeWordLinear(
            word_offset, reinterpret_cast<const uint8_t*>(data.data()));
        if (!sync_success)
        {
            SC_WARN("CIM_SYNC_FAIL", "Failed to synchronize write to dcim_cluster: word_offset={}", 
                       word_offset);
            // Depending on requirements, you might want to revert the local write or flag an error
            // state. For now, we just issue a warning.
        } else {
            SC_INFO("CIM_SYNC_OK", "Successfully synchronized write to dcim_cluster: word_offset={}", 
                        word_offset);
        }
    }
    return success;
}

void CIMCluster::dump(std::ostream& os) const
{
    SC_INFO("CIM_DUMP", "Dumping CIMCluster memory: total_words={}, total_bytes={}, reads={}, writes={}", 
               NPUConfig::TOTAL_WORDS, NPUConfig::TOTAL_BYTES, read_count.load(), write_count.load());
    
    os << "CIMCluster Memory Contents (Total Words: " << NPUConfig::TOTAL_WORDS << "):\n";
    os << "Total Reads: " << read_count.load() << "\n";
    os << "Total Writes: " << write_count.load() << "\n\n";

    // Dump linearly, word by word
    const size_t bytes_per_word = NPUConfig::LMEM_WD / 8;
    for (uint64_t word_idx = 0; word_idx < NPUConfig::TOTAL_WORDS; ++word_idx)
    {
        uint32_t eng_idx, mac_idx, row_idx, page_idx;
        // Use the parse function to display logical structure
        if (parse_word_offset(word_idx, eng_idx, mac_idx, row_idx, page_idx))
        {
            os << "Word " << std::setw(4) << word_idx << " (E:" << eng_idx << " M:" << mac_idx
               << " R:" << row_idx << " P:" << page_idx << "): ";
        }
        else
        {
            os << "Word " << std::setw(4) << word_idx
               << " (Invalid Offset): ";  // Should not happen if loop is correct
        }

        uint64_t byte_offset = word_idx * bytes_per_word;
        for (size_t i = 0; i < bytes_per_word; ++i)
        {
            // Check bounds before accessing raw_memory, although should be safe if TOTAL_BYTES is
            // correct
            if (byte_offset + i < raw_memory.size())
            {
                os << std::hex << std::setw(2) << std::setfill('0')
                   << static_cast<int>(raw_memory[byte_offset + i]) << " ";
            }
            else
            {
                os << " OOB ";  // Out Of Bounds - indicates an issue
            }
        }
        os << std::dec << "\n";
    }
    os << "\n";
}

void CIMCluster::reset()
{
    // Clear memory using the size of the vector (which is TOTAL_BYTES)
    std::memset(raw_memory.data(), 0, raw_memory.size());  // sizeof(uint8_t) is 1
    read_count = 0;
    write_count = 0;
    SC_INFO("CIM_RESET", "CIMCluster reset completed");
}

// Address Decoder implementation
bool decode(uint64_t address, size_t& unit_index, uint64_t& word_offset)
{
    // 检查地址是否256位对齐 (32字节对齐)
    if (address & 0x1F)
    {  // 0x1F = 31 = 2^5-1，检查低5位是否为0
        SC_ERROR("ADDR_DECODE", "Address not 256-bit aligned: addr={:#x}", address);
        return false;
    }

    // 提取LMEM index和offset
    unit_index = (address >> (NPUConfig::LMEM_OFFSET + 5)) & ((1ULL << NPUConfig::LMEM_INDEX) - 1);
    // 将byte地址转换为32字节对齐的word地址
    word_offset = (address >> 5) & ((1ULL << (NPUConfig::LMEM_OFFSET)) - 1);

    // 验证unit_index是否有效
    if (unit_index >= NPUConfig::LMEM_NUM)
    {
        SC_ERROR("ADDR_DECODE", "LMEM index out of bounds: addr={:#x}, index={}, max_index={}", 
                    address, unit_index, NPUConfig::LMEM_NUM - 1);
        return false;
    }

    // 根据unit_index验证offset范围
    if (unit_index < NPUConfig::SPAD_NUM)
    {
        // SPAD: 检查offset是否在SPAD地址范围内
        if (word_offset >= (1ULL << (NPUConfig::SPAD_ADDR_WD)))
        {
            SC_ERROR("ADDR_DECODE", "SPAD offset out of bounds: addr={:#x}, index={}, offset={}, max_offset={}", 
                        address, unit_index, word_offset, (1ULL << NPUConfig::SPAD_ADDR_WD) - 1);
            return false;
        }
    }
    else
    {
        // CIMC: 检查offset是否在CIMC地址范围内
        if (word_offset >= NPUConfig::TOTAL_WORDS)
        {
            SC_ERROR("ADDR_DECODE", "CIMC offset out of bounds: addr={:#x}, index={}, offset={}, max_offset={}", 
                        address, unit_index, word_offset, NPUConfig::TOTAL_WORDS - 1);
            return false;
        }
    }

    SC_INFO("ADDR_DECODE", "Address decoded: addr={:#x} -> unit={}, word_offset={}", 
                address, unit_index, word_offset);
    return true;
}

// LocalMemory implementation
// Update constructor to accept and store the pointer
LocalMemory::LocalMemory(sc_core::sc_module_name name, DcimCluster* dcim_ptr)
    : sc_module(name), dcim_cluster_ptr(dcim_ptr)  // Store pointer immediately
{
    SC_INFO("LMEM_INIT", "LocalMemory constructor: module_name={}, dcim_ptr={}", 
               this->name(), (dcim_ptr ? "valid" : "null"));
    
    // Initialize memory units
    for (int i = 0; i < NPUConfig::SPAD_NUM; i++)
    {
        memory_units.push_back(std::unique_ptr<Scratchpad>(new Scratchpad()));
        SC_INFO("LMEM_INIT", "Created SPAD unit {}", i);
    }
    // Create CIMC units
    for (int i = 0; i < NPUConfig::CIMC_NUM; i++)
    {
        // Create the SystemC CIMCluster instance
        std::unique_ptr<CIMCluster> cimc_unit(new CIMCluster());

        // Set the pointer to the corresponding C++ model DcimCluster instance.
        // This assumes 'this->dcim_cluster_ptr' (the pointer to the overall C++ DcimCluster model)
        // has been set in LocalMemory, likely via its constructor or a dedicated setter method
        // called by the testbench/top-level module.
        // We also assume a 1:1 mapping based on index 'i' for simplicity.
        // If the C++ model has multiple DcimCluster instances, adjust logic accordingly.
        if (this->dcim_cluster_ptr != nullptr)
        {
            // Assuming the C++ model 'dcim_cluster_ptr' points to the single instance
            // that corresponds to this SystemC CIMCluster instance (index i).
            // If the C++ side also has multiple clusters, you'd need a way to map index 'i'
            // to the correct C++ cluster pointer.
            // Use the pointer stored during construction
            cimc_unit->setDcimClusterPtr(this->dcim_cluster_ptr);
        }
        else
        {
            // Warning is no longer needed here as the pointer is set or null from the start
            // fprintf(stderr, "Warning: dcim_cluster_ptr not set in LocalMemory constructor for
            // CIMCluster %d. Synchronization disabled.\n", i); If dcim_cluster_ptr is null,
            // setDcimClusterPtr(nullptr) is called, which is fine.
            cimc_unit->setDcimClusterPtr(
                this->dcim_cluster_ptr);  // Pass the potentially null pointer
        }

        memory_units.push_back(std::move(cimc_unit));
        SC_INFO("LMEM_INIT", "Created CIM unit {}", i);
    }
    // Register TLM interface
    target_socket.register_b_transport(this, &LocalMemory::b_transport_lmem);
    
    SC_INFO("LMEM_INIT", "LocalMemory initialization completed: total_units={} (SPAD={}, CIM={})", 
               memory_units.size(), NPUConfig::SPAD_NUM, NPUConfig::CIMC_NUM);
}

bool LocalMemory::read_word(uint32_t unit_index, uint64_t word_offset, Word256b& data)
{
    if (unit_index >= memory_units.size())
    {
        SC_ERROR("LMEM_ERROR", "read_word: Invalid unit_index={}, max={}", unit_index, memory_units.size());
        return false;
    }
    return memory_units[unit_index]->read_word(word_offset, data);
}

bool LocalMemory::write_word(uint32_t unit_index, uint64_t word_offset, const Word256b& data)
{
    if (unit_index >= memory_units.size())
    {
        SC_ERROR("LMEM_ERROR", "write_word: Invalid unit_index={}, max={}", unit_index, memory_units.size());
        return false;
    }
    // This call now triggers the synchronization logic within CIMCluster::write_word
    return memory_units[unit_index]->write_word(word_offset, data);
}

// Bulk memory access methods
bool LocalMemory::bulk_read_bytes(uint32_t unit_index, uint64_t byte_offset, uint8_t* data, size_t len)
{
    if (unit_index >= memory_units.size())
    {
        SC_ERROR("LMEM_ERROR", "LMEM bulk_read_bytes: Invalid unit_index={}, max={}", unit_index, memory_units.size());
        return false;
    }
    
    SC_INFO("LMEM_READ", "LMEM bulk_read_bytes: unit={}, byte_offset={}, len={}", unit_index, byte_offset, len);
    
    bool success = memory_units[unit_index]->read_bytes(byte_offset, data, len);
    if (success) {
        stats.total_reads++;
        SC_INFO("LMEM_READ_OK", "LMEM bulk_read_bytes SUCCESS: unit={}, byte_offset={}, len={}", unit_index, byte_offset, len);
    } else {
        SC_ERROR("LMEM_READ_ERR", "LMEM bulk_read_bytes ERROR: unit={}, byte_offset={}, len={}", unit_index, byte_offset, len);
        stats.error_accesses++;
    }
    return success;
}

bool LocalMemory::bulk_write_bytes(uint32_t unit_index, uint64_t byte_offset, const uint8_t* data, size_t len)
{
    if (unit_index >= memory_units.size())
    {
        SC_ERROR("LMEM_ERROR", "LMEM bulk_write_bytes: Invalid unit_index={}, max={}", unit_index, memory_units.size());
        return false;
    }
    
    SC_INFO("LMEM_WRITE", "LMEM bulk_write_bytes: unit={}, byte_offset={}, len={}", unit_index, byte_offset, len);
    
    bool success = memory_units[unit_index]->write_bytes(byte_offset, data, len);
    if (success) {
        stats.total_writes++;
        SC_INFO("LMEM_WRITE_OK", "LMEM bulk_write_bytes SUCCESS: unit={}, byte_offset={}, len={}", unit_index, byte_offset, len);
    } else {
        SC_ERROR("LMEM_WRITE_ERR", "LMEM bulk_write_bytes ERROR: unit={}, byte_offset={}, len={}", unit_index, byte_offset, len);
        stats.error_accesses++;
    }
    return success;
}

bool LocalMemory::bulk_read_words(uint32_t unit_index, uint64_t word_offset, Word256b* data, size_t word_count)
{
    if (unit_index >= memory_units.size())
    {
        SC_ERROR("LMEM_ERROR", "LMEM bulk_read_words: Invalid unit_index={}, max={}", unit_index, memory_units.size());
        return false;
    }
    
    SC_INFO("LMEM_READ_WORDS", "LMEM bulk_read_words: unit={}, word_offset={}, word_count={}", unit_index, word_offset, word_count);
    
    // Read words one by one
    for (size_t i = 0; i < word_count; ++i)
    {
        if (!memory_units[unit_index]->read_word(word_offset + i, data[i]))
        {
            SC_ERROR("LMEM_READ_WORDS_ERR", "LMEM bulk_read_words ERROR: unit={}, word_offset={}, failed at word {}", 
                        unit_index, word_offset, i);
            stats.error_accesses++;
            return false;
        }
    }
    
    stats.total_reads += word_count;
    SC_INFO("LMEM_READ_WORDS_OK", "LMEM bulk_read_words SUCCESS: unit={}, word_offset={}, word_count={}", 
                unit_index, word_offset, word_count);
    return true;
}

bool LocalMemory::bulk_write_words(uint32_t unit_index, uint64_t word_offset, const Word256b* data, size_t word_count)
{
    if (unit_index >= memory_units.size())
    {
        SC_ERROR("LMEM_ERROR", "LMEM bulk_write_words: Invalid unit_index={}, max={}", unit_index, memory_units.size());
        return false;
    }
    
    SC_INFO("LMEM_WRITE_WORDS", "LMEM bulk_write_words: unit={}, word_offset={}, word_count={}", unit_index, word_offset, word_count);
    
    // Write words one by one
    for (size_t i = 0; i < word_count; ++i)
    {
        if (!memory_units[unit_index]->write_word(word_offset + i, data[i]))
        {
            SC_ERROR("LMEM_WRITE_WORDS_ERR", "LMEM bulk_write_words ERROR: unit={}, word_offset={}, failed at word {}", 
                        unit_index, word_offset, i);
            stats.error_accesses++;
            return false;
        }
    }
    
    stats.total_writes += word_count;
    SC_INFO("LMEM_WRITE_WORDS_OK", "LMEM bulk_write_words SUCCESS: unit={}, word_offset={}, word_count={}", 
                unit_index, word_offset, word_count);
    return true;
}

bool LocalMemory::read_entire_unit(uint32_t unit_index, std::vector<uint8_t>& data)
{
    if (unit_index >= memory_units.size())
    {
        SC_ERROR("LMEM_ERROR", "LMEM read_entire_unit: Invalid unit_index={}, max={}", unit_index, memory_units.size());
        return false;
    }
    
    // Determine the size of the memory unit
    size_t unit_size;
    if (unit_index < NPUConfig::SPAD_NUM)
    {
        // SPAD unit
        unit_size = NPUConfig::SPAD_NUM_BYTES;
        SC_INFO("LMEM_READ_ENTIRE_UNIT", "LMEM read_entire_unit: Reading entire SPAD unit={}, size={} bytes", unit_index, unit_size);
    }
    else
    {
        // CIM unit
        unit_size = NPUConfig::TOTAL_BYTES;
        SC_INFO("LMEM_READ_ENTIRE_UNIT", "LMEM read_entire_unit: Reading entire CIM unit={}, size={} bytes", unit_index, unit_size);
    }
    
    // Resize the output vector
    data.resize(unit_size);
    
    // Read the entire unit
    bool success = bulk_read_bytes(unit_index, 0, data.data(), unit_size);
    if (!success)
    {
        SC_ERROR("LMEM_READ_ENTIRE_UNIT_ERR", "LMEM read_entire_unit ERROR: unit={}", unit_index);
        data.clear();
    }
    
    return success;
}

bool LocalMemory::write_entire_unit(uint32_t unit_index, const std::vector<uint8_t>& data)
{
    if (unit_index >= memory_units.size())
    {
        SC_ERROR("LMEM_ERROR", "LMEM write_entire_unit: Invalid unit_index={}, max={}", unit_index, memory_units.size());
        return false;
    }
    
    // Determine the expected size of the memory unit
    size_t expected_size;
    if (unit_index < NPUConfig::SPAD_NUM)
    {
        // SPAD unit
        expected_size = NPUConfig::SPAD_NUM_BYTES;
        SC_INFO("LMEM_WRITE_ENTIRE_UNIT", "LMEM write_entire_unit: Writing entire SPAD unit={}, expected_size={} bytes", 
                   unit_index, expected_size);
    }
    else
    {
        // CIM unit
        expected_size = NPUConfig::TOTAL_BYTES;
        SC_INFO("LMEM_WRITE_ENTIRE_UNIT", "LMEM write_entire_unit: Writing entire CIM unit={}, expected_size={} bytes", 
                   unit_index, expected_size);
    }   
    
    // Check if the data size matches the expected size
    if (data.size() != expected_size)
    {
        SC_ERROR("LMEM_WRITE_ENTIRE_UNIT_ERR", "LMEM write_entire_unit: Size mismatch for unit={}, provided={}, expected={}", 
                    unit_index, data.size(), expected_size);
        return false;
    }
    
    // Write the entire unit
    bool success = bulk_write_bytes(unit_index, 0, data.data(), data.size());
    if (!success)
    {
        SC_ERROR("LMEM_WRITE_ENTIRE_UNIT_ERR", "LMEM write_entire_unit ERROR: unit={}", unit_index);
    }
    
    return success;
}

// Utility methods to get memory unit information
size_t LocalMemory::get_unit_size_bytes(uint32_t unit_index) const
{
    if (unit_index >= memory_units.size())
        return 0;
    
    if (unit_index < NPUConfig::SPAD_NUM)
    {
        return NPUConfig::SPAD_NUM_BYTES;
    }
    else
    {
        return NPUConfig::TOTAL_BYTES;
    }
}

size_t LocalMemory::get_unit_size_words(uint32_t unit_index) const
{
    if (unit_index >= memory_units.size())
        return 0;
    
    if (unit_index < NPUConfig::SPAD_NUM)
    {
        return NPUConfig::SPAD_DP;
    }
    else
    {
        return NPUConfig::TOTAL_WORDS;
    }
}

bool LocalMemory::is_spad_unit(uint32_t unit_index) const
{
    return unit_index < NPUConfig::SPAD_NUM;
}

bool LocalMemory::is_cim_unit(uint32_t unit_index) const
{
    return unit_index >= NPUConfig::SPAD_NUM && unit_index < (NPUConfig::SPAD_NUM + NPUConfig::CIMC_NUM);
}

tlm::tlm_generic_payload* LocalMemory::create_read_trans(uint32_t unit_index,
                                                         uint64_t word_offset,
                                                         Word256b& data)
{
    auto* trans = new tlm::tlm_generic_payload();

    trans->set_command(tlm::TLM_READ_COMMAND);
    trans->set_address(make_address(unit_index, word_offset));
    trans->set_data_ptr(reinterpret_cast<uint8_t*>(data.data()));
    trans->set_data_length(NPUConfig::LMEM_WD / 8);  // 256位 = 32字节
    trans->set_streaming_width(NPUConfig::LMEM_WD / 8);
    trans->set_byte_enable_ptr(nullptr);
    trans->set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

    SC_INFO("LMEM_TLM", "Created read transaction: unit={}, word_offset={}, addr={:#x}", 
                unit_index, word_offset, trans->get_address());
    return trans;
}

tlm::tlm_generic_payload* LocalMemory::create_write_trans(uint32_t unit_index,
                                                          uint64_t word_offset,
                                                          const Word256b& data)
{
    auto* trans = new tlm::tlm_generic_payload();

    // 因为tlm_generic_payload的data_ptr是非const的，需要创建一个临时拷贝
    auto* data_copy = new uint8_t[NPUConfig::LMEM_WD / 8];
    std::memcpy(data_copy, data.data(), NPUConfig::LMEM_WD / 8);

    trans->set_command(tlm::TLM_WRITE_COMMAND);
    trans->set_address(make_address(unit_index, word_offset));
    trans->set_data_ptr(data_copy);
    trans->set_data_length(NPUConfig::LMEM_WD / 8);
    trans->set_streaming_width(NPUConfig::LMEM_WD / 8);
    trans->set_byte_enable_ptr(nullptr);
    trans->set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

    SC_INFO("LMEM_TLM", "Created write transaction: unit={}, word_offset={}, addr={:#x}", 
                unit_index, word_offset, trans->get_address());
    return trans;
}

void LocalMemory::b_transport_lmem(int id, tlm::tlm_generic_payload& trans, sc_core::sc_time& delay)
{
    uint64_t addr = trans.get_address();
    uint8_t* data = trans.get_data_ptr();
    unsigned int len = trans.get_data_length();

    // Decode address
    size_t unit_index;
    uint64_t word_offset;
    if (!decode(addr, unit_index, word_offset))
    {
        SC_ERROR("LMEM_ERROR", "LMEM Address decode failed: addr={:#x}, len={}", addr, len);
        trans.set_response_status(tlm::TLM_ADDRESS_ERROR_RESPONSE);
        stats.error_accesses++;
        return;
    }

    bool success;
    uint64_t byte_offset = word_offset * (NPUConfig::LMEM_WD / 8);
    
    if (trans.is_read())
    {
        SC_INFO("LMEM_READ", "LMEM READ: function=b_transport_lmem, addr={:#x}, unit={}, word_offset={}, byte_offset={}, len={}", 
                    addr, unit_index, word_offset, byte_offset, len);
        
        // Use bulk read for arbitrary length support
        success = bulk_read_bytes(unit_index, byte_offset, data, len);
        if (success) {
            SC_INFO("LMEM_READ_OK", "LMEM read SUCCESS: addr={:#x}", addr);
            
            // Log data content (limit to reasonable size for logging)
            std::ostringstream data_stream;
            data_stream << "Read data: ";
            size_t log_len = (len < 32) ? len : 32; // Limit to 32 bytes for logging
            for (size_t i = 0; i < log_len; ++i) {
                data_stream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(data[i]) << " ";
            }
            if (len > 32) data_stream << "... (" << len << " bytes total)";
            SC_INFO("LMEM_READ_DATA", "LMEM read DATA: {}", data_stream.str());
        } else {
            SC_ERROR("LMEM_READ_ERR", "LMEM read ERROR: addr={:#x}", addr);
        }
    }
    else
    {
        SC_INFO("LMEM_WRITE", "LMEM write: function=b_transport_lmem, addr={:#x}, unit={}, word_offset={}, byte_offset={}, len={}", 
                    addr, unit_index, word_offset, byte_offset, len);
        
        // Log data content (limit to reasonable size for logging)
        std::ostringstream data_stream;
        data_stream << "Write data: ";
                 size_t log_len = (len < 32) ? len : 32; // Limit to 32 bytes for logging
        for (size_t i = 0; i < log_len; ++i) {
            data_stream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(data[i]) << " ";
        }
        if (len > 32) data_stream << "... (" << len << " bytes total)";
        SC_INFO("LMEM_WRITE_DATA", "LMEM write DATA: {}", data_stream.str());
        
        // Use bulk write for arbitrary length support
        success = bulk_write_bytes(unit_index, byte_offset, data, len);
        if (success) {
            SC_INFO("LMEM_WRITE_OK", "LMEM write SUCCESS: addr={:#x}", addr);
        } else {
            SC_ERROR("LMEM_WRITE_ERR", "LMEM write ERROR: addr={:#x}", addr);
        }
    }

    if (!success)
    {
        stats.error_accesses++;
        trans.set_response_status(tlm::TLM_GENERIC_ERROR_RESPONSE);
    }
    else
    {
        trans.set_response_status(tlm::TLM_OK_RESPONSE);
    }
}

void LocalMemory::dump_memory(const std::string& filename)
{
    SC_INFO("LMEM_DUMP", "Dumping memory to file: {}", filename);
    
    std::ofstream ofs(filename);
    if (!ofs)
    {
        SC_ERROR("LMEM_DUMP_ERR", "Failed to open dump file: {}", filename);
        return;
    }

    ofs << "Local Memory Dump\n";
    ofs << "=================\n\n";

    for (size_t i = 0; i < memory_units.size(); i++)
    {
        ofs << "Memory Unit " << i << ":\n";
        ofs << "----------------\n";
        memory_units[i]->dump(ofs);
        ofs << "\n";
    }
    
    SC_INFO("LMEM_DUMP_OK", "Memory dump completed: {}", filename);
}

void LocalMemory::print_statistics() const
{
    SC_INFO("LMEM_STATS", "Local Memory Statistics: reads={}, writes={}, errors={}", 
               stats.total_reads.load(), stats.total_writes.load(), stats.error_accesses.load());
}

uint64_t make_address(uint32_t unit_index, uint64_t word_offset)
{
    // word_offset已经是256bit的偏移，需要左移5位变成字节地址
    uint64_t addr = (unit_index << (NPUConfig::LMEM_OFFSET + 5)) | (word_offset << 5);
    SC_INFO("ADDR_MAKE", "Made address: unit={}, word_offset={} -> addr={:#x}", 
                unit_index, word_offset, addr);
    return addr;
}

void LocalMemory::reset()
{
    SC_INFO("LMEM_RESET", "Resetting all memory units");
    for (auto& unit : memory_units)
    {
        unit->reset();
    }
    stats.total_reads = 0;
    stats.total_writes = 0;
    stats.error_accesses = 0;
    SC_INFO("LMEM_RESET_OK", "LocalMemory reset completed");
}

}  // namespace npu_sc
