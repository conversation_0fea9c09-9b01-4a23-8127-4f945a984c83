#include "ac_datatypes.h"
// #include <gtest/gtest.h>
#include "../inc/input_table.h"
#include "../inc/weight_table.h"
// #include "spdlog/spdlog.h"

// TEST(ac_int16_t, test_ac_int16_t) {
//     for (int i=0;i<32;i++){
//         ac_int16_t a = in_table_tr_int16[3][i];
//         spdlog::info("test_ac_int16_t a:{}", a);
//     }
// }

// TEST(ac_int8_t, test_ac_int8_t) {
//     for (int i=0;i<32;i++){
//         ac_int8_t a = in_table_tr_int8[1][i];
//         spdlog::info("test_ac_int8_t a:{}", a);
//     }
// }

// TEST(ac_int4_t, test_ac_int4_t) {
//     for (int i=0;i<32;i++){
//         ac_int4_t a = in_table_tr_int4[0][i];
//         spdlog::info("test_ac_int4_t a:{}", a);
//     }
// }

// TEST(ac_fp8e4_t, test_ac_fp8e4_t) {
//     for (int i=0;i<32;i++){
//         ac_fp8e4_t a ;
//         a.set_data(wt_mhex_fp8e4[0][i]);
//         spdlog::info("test_ac_fp8e4_t a:{}", a);
//     }
// }

// TEST(ac_fp8e5_t, test_ac_fp8e5_t) {
//     for (int i=0;i<32;i++){
//         ac_fp8e5_t a ;
//         a.set_data(wt_mhex_fp8e5[0][i]);
//         spdlog::info("test_ac_fp8e5_t a:{}", a);
//     }
// }

// TEST(ac_bbfloat16_t, test_ac_bbfloat16_t) {
//     for (int i=0;i<32;i++){
//         ac_bbfloat16_t a ;
//         a.set_data(wt_mhex_bbf16[0][i]);
//         spdlog::info("test_ac_bbfloat16_t a:{}", a);
//     }
// }

// TEST(ac_bfloat16_t, test_ac_bfloat16_t) {
//     for (int i=0;i<32;i++){
//         ac_bfloat16_t a ;
//         a.set_data(wt_mhex_bf16[0][i]);
//         spdlog::info("test_ac_bfloat16_t a:{}", a);
//     }
// }

// TEST(ac_float16_t, test_ac_float16_t) {
//     for (int i=0;i<32;i++){
//         ac_float16_t a ;
//         a.set_data(wt_mhex_fp16[0][i]);
//         spdlog::info("test_ac_float16_t a:{}", a);
//     }
// }

// int main(int argc, char **argv) {
//     spdlog::set_pattern("[%l] %v");
//     ::testing::InitGoogleTest(&argc, argv);
//     return RUN_ALL_TESTS();
// }