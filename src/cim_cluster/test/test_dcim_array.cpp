#include <cmath>  // For fabs
#include <cstdint>
#include <cstdio>   // For printf
#include <cstring>  // For memcmp, memset
#include <iostream>
#include <vector>

// Include the class header
#include "../inc/dcim_array.hpp"

// Define number of 16-bit words per row (DCIM_NUM_BYTES_PER_ROW/2)
#define DCIM_NUM_WORDS_PER_ROW (DCIM_NUM_BYTES_PER_ROW / 2)

// Include C headers within extern "C"
extern "C"
{
#include "../inc/dcim_com.h"
#include "../inc/dcim_matrix_utils.h"  // For preparing test data
}

// Include test data tables
#include "../inc/input_table.h"
#include "../inc/weight_table.h"
#include "ac_datatypes.h"  // For verification

// --- Test Configuration ---
// Select the weight format to test compute by uncommenting one define
// #define TEST_WT_FP16
// #define TEST_WT_BF16
// #define TEST_WT_BBF16
// #define TEST_WT_FP8E4
// #define TEST_WT_FP8E5
// #define TEST_WT_INT16
// #define TEST_WT_INT8
#define TEST_WT_INT4

// Select the input format to test compute
#define TEST_IN_TYPE INT4

// --- Helper Function for Binary Printing ---
void print_binary_arr(uint16_t value, int bits)
{
    for (int i = bits - 1; i >= 0; i--)
    {
        printf("%c", (value >> i) & 1 ? '1' : '0');
    }
}

// --- Test Runner ---
int main()
{
    printf("--- Starting DcimArray Class Test ---\n");
    int errors = 0;
    int test_count = 0;

    // --- Test 1: Storage - writePage / readPage ---
    test_count++;
    printf("\n--- Test %d: DcimArray::writePage / readPage ---\n", test_count);
    {
        DcimArray test_array;
        uint8_t write_buffer[DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW];
        uint8_t read_buffer[DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW];
        int test_page_idx = 3;

        // Prepare test data (using FP16 aligned data for simplicity)
        printf("Preparing test page data (using FP16 aligned)...\n");
        bool prep_ok = convertAlignedColumnMajorToPageRowMajor(
            (const uint8_t*)wt_algn_fp16,  // Source: Aligned Column Major
            FP16,                          // Type used for packing structure
            (uint8_t*)write_buffer         // Destination: Page Row Major
        );

        if (!prep_ok)
        {
            fprintf(stderr, "  FAIL: Failed to prepare test page data.\n");
            errors++;
        }
        else
        {
            // Write the page
            printf("Writing to page %d...\n", test_page_idx);
            bool write_ok = test_array.writePage(test_page_idx, &write_buffer[0][0]);
            if (!write_ok)
            {
                fprintf(stderr, "  FAIL: DcimArray::writePage returned false.\n");
                errors++;
            }
            else
            {
                // Read the page back
                printf("Reading from page %d...\n", test_page_idx);
                memset(read_buffer,
                       0xAA,
                       sizeof(read_buffer));  // Fill read buffer to ensure it's overwritten
                bool read_ok = test_array.readPage(test_page_idx, &read_buffer[0][0]);
                if (!read_ok)
                {
                    fprintf(stderr, "  FAIL: DcimArray::readPage returned false.\n");
                    errors++;
                }
                else
                {
                    // Compare
                    if (memcmp(write_buffer, read_buffer, sizeof(write_buffer)) == 0)
                    {
                        printf("  PASS: writePage/readPage data matches.\n");
                    }
                    else
                    {
                        fprintf(stderr, "  FAIL: Data mismatch after writePage/readPage.\n");
                        errors++;
                        // Optional: Print mismatch details
                    }
                }
            }
        }
    }

    // --- Test 2: Storage - writeRow / readRow ---
    test_count++;
    printf("\n--- Test %d: DcimArray::writeRow / readRow ---\n", test_count);
    {
        DcimArray test_array;
        uint8_t write_page_buffer[DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW];
        uint8_t write_row_buffer[DCIM_NUM_BYTES_PER_ROW];
        uint8_t read_row_buffer[DCIM_NUM_BYTES_PER_ROW];
        int test_page_idx = 5;
        int test_row_idx = 10;  // Test a specific row

        // Prepare a full page first (using BF16 data for variety)
        bool prep_ok = convertAlignedColumnMajorToPageRowMajor(
            (const uint8_t*)wt_algn_bf16, BF16, (uint8_t*)write_page_buffer);

        if (!prep_ok)
        {
            fprintf(stderr, "  FAIL: Failed to prepare test page data for row test.\n");
            errors++;
        }
        else
        {
            // Write the full page initially
            if (!test_array.writePage(test_page_idx,
                                      reinterpret_cast<const uint8_t*>(write_page_buffer)))
            {
                fprintf(stderr, "  FAIL: Initial writePage failed for row test.\n");
                errors++;
            }
            else
            {
                // Prepare the specific row data (modify one value for distinction)
                memcpy(write_row_buffer, write_page_buffer[test_row_idx], sizeof(write_row_buffer));
                write_row_buffer[0] = 0xABCD;  // Change first word

                // Write the specific row
                printf(
                    "Writing modified data to page %d, row %d...\n", test_page_idx, test_row_idx);
                bool write_ok = test_array.writeRow(test_page_idx, test_row_idx, write_row_buffer);
                if (!write_ok)
                {
                    fprintf(stderr, "  FAIL: DcimArray::writeRow returned false.\n");
                    errors++;
                }
                else
                {
                    // Read the specific row back
                    printf("Reading from page %d, row %d...\n", test_page_idx, test_row_idx);
                    memset(read_row_buffer, 0xBB, sizeof(read_row_buffer));  // Fill read buffer
                    bool read_ok = test_array.readRow(test_page_idx, test_row_idx, read_row_buffer);
                    if (!read_ok)
                    {
                        fprintf(stderr, "  FAIL: DcimArray::readRow returned false.\n");
                        errors++;
                    }
                    else
                    {
                        // Compare
                        if (memcmp(write_row_buffer, read_row_buffer, sizeof(write_row_buffer)) ==
                            0)
                        {
                            printf("  PASS: writeRow/readRow data matches.\n");
                        }
                        else
                        {
                            fprintf(stderr, "  FAIL: Data mismatch after writeRow/readRow.\n");
                            errors++;
                        }
                    }
                    // Optional: Read a different row to ensure it wasn't affected
                    int other_row_idx = test_row_idx + 1;
                    if (test_array.readRow(test_page_idx, other_row_idx, read_row_buffer))
                    {
                        if (memcmp(write_page_buffer[other_row_idx],
                                   read_row_buffer,
                                   sizeof(read_row_buffer)) != 0)
                        {
                            fprintf(stderr,
                                    "  FAIL: writeRow affected an unrelated row (%d).\n",
                                    other_row_idx);
                            errors++;
                        }
                        else
                        {
                            printf("  PASS: Unrelated row (%d) was not affected by writeRow.\n",
                                   other_row_idx);
                        }
                    }
                }
            }
        }
    }

    // --- Test 3: Compute ---
    test_count++;
    printf("\n--- Test %d: DcimArray::compute ---\n", test_count);
    {
        DcimArray compute_array;
        uint8_t in_tpy = TEST_IN_TYPE;
        uint8_t wt_tpy = 0;
        const uint16_t(*raw_input_table_ptr)[32] =
            nullptr;  // Used for golden reference calculation
        const uint16_t(*raw_weight_table_ptr)[32] =
            nullptr;  // Used for golden reference calculation if FP type
        const uint16_t(*aligned_weight_table_ptr)[33] =
            nullptr;  // Used for preparing page data AND golden ref if INT type
        int num_weight_cols = 0;
        uint8_t in_tbn[10] = {0, 1, 2, 3, 3, 4, 5, 6, 7, 8};  // Mapping for input tables

// --- Determine Test Types and Data Pointers (Similar to test_storage_compute) ---
// Select pointers based on the *weight* type being tested for simplicity,
// assuming corresponding input tables exist. TEST_IN_TYPE defines the actual input format used.
// Check input_table.h for available aligned input tables (e.g., in_table_algn_fp16).
#ifdef TEST_WT_FP16
        wt_tpy = FP16;
        raw_input_table_ptr = &in_table_tr_fp16[in_tbn[in_tpy]];
        raw_weight_table_ptr = &wt_mhex_fp16[0];
        aligned_weight_table_ptr = &wt_algn_fp16[0];
        num_weight_cols = 16;
#elif defined(TEST_WT_BF16)
        wt_tpy = BF16;
        raw_input_table_ptr = &in_table_tr_bf16[in_tbn[in_tpy]];
        raw_weight_table_ptr = &wt_mhex_bf16[0];
        aligned_weight_table_ptr = &wt_algn_bf16[0];
        num_weight_cols = 16;
#elif defined(TEST_WT_BBF16)
        wt_tpy = BBF16;
        raw_input_table_ptr = &in_table_tr_bbf16[in_tbn[in_tpy]];
        raw_weight_table_ptr = &wt_mhex_bbf16[0];
        aligned_weight_table_ptr = &wt_algn_bbf16[0];
        num_weight_cols = 32;
#elif defined(TEST_WT_FP8E4)
        wt_tpy = FP8E4;
        raw_input_table_ptr = &in_table_tr_fp8e4[in_tbn[in_tpy]];
        raw_weight_table_ptr = &wt_mhex_fp8e4[0];
        aligned_weight_table_ptr = &wt_algn_fp8e4[0];
        num_weight_cols = 32;
#elif defined(TEST_WT_FP8E5)
        wt_tpy = FP8E5;
        raw_input_table_ptr = &in_table_tr_fp8e5[in_tbn[in_tpy]];
        raw_weight_table_ptr = &wt_mhex_fp8e5[0];
        aligned_weight_table_ptr = &wt_algn_fp8e5[0];
        num_weight_cols = 32;
#elif defined(TEST_WT_INT16)
        wt_tpy = INT16;
        raw_input_table_ptr = &in_table_tr_int16[in_tbn[in_tpy]];
        raw_weight_table_ptr = nullptr;
        aligned_weight_table_ptr = &wt_algn_int16[0];
        num_weight_cols = 16;
#elif defined(TEST_WT_INT8)
        wt_tpy = INT8;
        raw_input_table_ptr = &in_table_tr_int8[in_tbn[in_tpy]];
        raw_weight_table_ptr = nullptr;
        aligned_weight_table_ptr = &wt_algn_int8[0];
        num_weight_cols = 32;
#elif defined(TEST_WT_INT4)
        wt_tpy = INT4;
        raw_input_table_ptr = &in_table_tr_int4[in_tbn[in_tpy]];
        raw_weight_table_ptr = nullptr;
        aligned_weight_table_ptr = &wt_algn_int4[0];
        num_weight_cols = 64;
#else
    #error "No weight type defined for compute testing!"
        return 1;
#endif
        printf("Testing Compute: Input=%d, Weight=%d\n", in_tpy, wt_tpy);

        // --- Prepare Input ---
        // Golden reference calculation still needs raw input
        uint16_t raw_input_vec[32];
        if (!raw_input_table_ptr)
        {
            errors++;
            fprintf(stderr, "  FAIL: Raw input table pointer is null.\n");
            goto compute_end;
        }
        memcpy(raw_input_vec, raw_input_table_ptr, 32 * sizeof(uint16_t));

        uint16_t aligned_input_vec[DCIM_NUM_ROWS_PER_PAGE];  // Size 33
        printf("Copying pre-aligned input vector...\n");
        if (in_tpy < 4)
        {  // Handle INT types separately if needed
            memcpy(aligned_input_vec, raw_input_vec, 32 * sizeof(uint16_t));
            aligned_input_vec[32] = 0;  // Set exponent part to 0 for INT types
        }
        else
        {
            // float_data_align(raw_input_vec, in_tpy, aligned_input_vec);
            compute_array.alignInputVector(
                (const uint8_t*)raw_input_vec, in_tpy, (uint8_t*)aligned_input_vec);
        }

        // --- Prepare Weight Page ---
        uint8_t weight_page_buffer[DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW];
        memset(weight_page_buffer, 0, sizeof(weight_page_buffer));
        bool page_prep_ok = false;
        const uint16_t* aligned_col_major_ptr =
            nullptr;  // This will point to the aligned weight data in column-major format

        // Use the aligned weight table directly as the source for page conversion
        if (!aligned_weight_table_ptr)
        {
            errors++;
            fprintf(stderr, "  FAIL: Aligned weight table pointer is null.\n");
            goto compute_end;
        }
        aligned_col_major_ptr =
            (const uint16_t*)aligned_weight_table_ptr;  // Cast from [cols][33] to flat pointer

        printf("Converting aligned weights to page format...\n");
        page_prep_ok = convertAlignedColumnMajorToPageRowMajor(
            (const uint8_t*)aligned_col_major_ptr, wt_tpy, (uint8_t*)weight_page_buffer);

        if (!page_prep_ok)
        {
            errors++;
            fprintf(stderr, "  FAIL: convertAlignedColumnMajorToPageRowMajor failed.\n");
            goto compute_end;
        }

        // --- Write Page to DcimArray Instance ---
        int compute_page_idx = 0;
        printf("Writing test page %d to DcimArray instance...\n", compute_page_idx);
        if (!compute_array.writePage(compute_page_idx,
                                     reinterpret_cast<const uint8_t*>(weight_page_buffer)))
        {
            errors++;
            fprintf(stderr, "  FAIL: compute_array.writePage failed.\n");
            goto compute_end;
        }

        // --- Execute Compute ---
        uint32_t actual_output_hex[64];
        printf("Calling compute_array.compute()...\n");
        bool compute_ok = compute_array.compute((const uint8_t*)aligned_input_vec,
                                                in_tpy,
                                                compute_page_idx,
                                                wt_tpy,
                                                (uint8_t*)actual_output_hex);

        if (!compute_ok)
        {
            errors++;
            fprintf(stderr, "  FAIL: compute_array.compute() returned false.\n");
            goto compute_end;
        }

        // --- Calculate Expected Results (using ac_datatypes) ---
        printf("Calculating expected results...\n");
        std::vector<float> expected_output_float(64, 0.0);
        for (int j = 0; j < num_weight_cols; ++j)
        {
            float expected_dot_product = 0.0;
            for (int i = 0; i < 32; ++i)
            {
                float in_val_d = 0.0;
                float wt_val_d = 0.0;
                // Convert raw input
                switch (in_tpy)
                {
                    case INT4:
                    {
                        ac_int4_t in_ac = raw_input_vec[i];
                        in_val_d = static_cast<float>(in_ac.to_int());
                        break;
                    }
                    case INT8:
                    {
                        ac_int8_t in_ac = raw_input_vec[i];
                        in_val_d = static_cast<float>(in_ac.to_int());
                        break;
                    }
                    case INT16:
                    {
                        ac_int16_t in_ac = raw_input_vec[i];
                        in_val_d = static_cast<float>(in_ac.to_int());
                        break;
                    }
                    case FP16:
                    {
                        ac_float16_t in_ac;
                        in_ac.set_data(raw_input_vec[i]);
                        in_val_d = in_ac.to_float();
                        break;
                    }
                    case BF16:
                    {
                        ac_bfloat16_t in_ac;
                        in_ac.set_data(raw_input_vec[i]);
                        in_val_d = in_ac.to_float();
                        break;
                    }
                    case BBF16:
                    {
                        ac_bbfloat16_t in_ac;
                        in_ac.set_data(raw_input_vec[i]);
                        in_val_d = in_ac.to_float();
                        break;
                    }
                    case FP8E4:
                    {
                        ac_fp8e4_t in_ac;
                        in_ac.set_data(raw_input_vec[i]);
                        in_val_d = in_ac.to_float();
                        break;
                    }
                    case FP8E5:
                    {
                        ac_fp8e5_t in_ac;
                        in_ac.set_data(raw_input_vec[i]);
                        in_val_d = in_ac.to_float();
                        break;
                    }
                    default:
                        break;
                }
                // Get weight value (raw for FP, aligned for INT)
                uint16_t wt_val_verify = 0;
                // Golden reference uses raw weights for FP types, aligned weights for INT types
                if (wt_tpy == FP16 || wt_tpy == BF16 || wt_tpy == BBF16 || wt_tpy == FP8E4 ||
                    wt_tpy == FP8E5)
                {  // Check if FP type
                    if (!raw_weight_table_ptr)
                    {
                        fprintf(stderr, "ERR: raw_weight_table_ptr null in golden calc\n");
                        continue;
                    }
                    wt_val_verify = raw_weight_table_ptr[j][i];
                }
                else
                {  // INT type
                    if (!aligned_weight_table_ptr)
                    {
                        fprintf(stderr, "ERR: aligned_weight_table_ptr null in golden calc\n");
                        continue;
                    }
                    wt_val_verify = aligned_weight_table_ptr[j][i];
                }
                // Convert weight
                switch (wt_tpy)
                {
                    case INT4:
                    {
                        ac_int4_t wt_ac = wt_val_verify;
                        wt_val_d = static_cast<float>(wt_ac.to_int());
                        break;
                    }
                    case INT8:
                    {
                        ac_int8_t wt_ac = wt_val_verify;
                        wt_val_d = static_cast<float>(wt_ac.to_int());
                        break;
                    }
                    case INT16:
                    {
                        ac_int16_t wt_ac = wt_val_verify;
                        wt_val_d = static_cast<float>(wt_ac.to_int());
                        break;
                    }
                    case FP16:
                    {
                        ac_float16_t wt_ac;
                        wt_ac.set_data(wt_val_verify);
                        wt_val_d = wt_ac.to_float();
                        break;
                    }
                    case BF16:
                    {
                        ac_bfloat16_t wt_ac;
                        wt_ac.set_data(wt_val_verify);
                        wt_val_d = wt_ac.to_float();
                        break;
                    }
                    case BBF16:
                    {
                        ac_bbfloat16_t wt_ac;
                        wt_ac.set_data(wt_val_verify);
                        wt_val_d = wt_ac.to_float();
                        break;
                    }
                    case FP8E4:
                    {
                        ac_fp8e4_t wt_ac;
                        wt_ac.set_data(wt_val_verify);
                        wt_val_d = wt_ac.to_float();
                        break;
                    }
                    case FP8E5:
                    {
                        ac_fp8e5_t wt_ac;
                        wt_ac.set_data(wt_val_verify);
                        wt_val_d = wt_ac.to_float();
                        break;
                    }
                    default:
                        break;
                }
                expected_dot_product += in_val_d * wt_val_d;
            }
            expected_output_float[j] = expected_dot_product;
        }

        // --- Verify Compute Results ---
        printf("Verifying compute results...\n");
        const float tolerance = 1e-4f;
        int compute_errors = 0;
        for (int j = 0; j < num_weight_cols; ++j)
        {
            float actual_float = 0.0f;
            mh2f_conv(actual_output_hex[j],
                      FP32,
                      &actual_float);  // Convert FP32 hex result back to float
            float error = std::fabs(expected_output_float[j] - actual_float);
            if (error > tolerance)
            {
                fprintf(stderr,
                        "  FAIL: Mismatch at index %d: Expected %.8f, Got %.8f (Hex: 0x%08X), "
                        "AbsError: %.8f\n",
                        j,
                        expected_output_float[j],
                        actual_float,
                        actual_output_hex[j],
                        error);
                compute_errors++;
            }
        }
        // Check padding
        for (int j = num_weight_cols; j < 64; ++j)
        {
            if (actual_output_hex[j] != 0)
            {
                fprintf(stderr,
                        "  FAIL: Padding Error at index %d: Expected 0x00000000, Got 0x%08X\n",
                        j,
                        actual_output_hex[j]);
                compute_errors++;
            }
        }

        if (compute_errors == 0)
        {
            printf("  PASS: Compute results verified successfully within tolerance (%.1e).\n",
                   tolerance);
        }
        else
        {
            errors += compute_errors;
        }
    }
compute_end:;  // Label for goto

    // --- Final Summary ---
    printf("\n--- DcimArray Test Summary ---\n");
    if (errors == 0)
    {
        printf("PASS: All %d tests passed!\n", test_count);
        return 0;  // Success
    }
    else
    {
        printf("FAIL: %d errors found in %d tests.\n", errors, test_count);
        return 1;  // Failure
    }
}