#include <cmath>  // For fabs
#include <cstdint>
#include <cstdio>   // For printf
#include <cstring>  // For memcmp, memset, memcpy
#include <iostream>
#include <numeric>  // For std::accumulate (optional)
#include <vector>

// Include the class header
#include "../inc/dcim_cluster.hpp"  // Changed from dcim_engine.hpp

// Define number of 16-bit words per row (DCIM_NUM_BYTES_PER_ROW/2)
#define DCIM_NUM_WORDS_PER_ROW (DCIM_NUM_BYTES_PER_ROW / 2)

// Include C headers within extern "C"
extern "C"
{
#include "../inc/dcim_com.h"
#include "../inc/dcim_matrix_utils.h"  // For preparing test data
}

// Include test data tables
#include "../inc/input_table.h"
#include "../inc/weight_table.h"
#include "ac_datatypes.h"  // For verification

// --- Test Configuration ---
// Select the weight format to test compute by uncommenting one define
// #define TEST_WT_FP16
// #define TEST_WT_BF16
// #define TEST_WT_BBF16
// #define TEST_WT_FP8E4
// #define TEST_WT_FP8E5
// #define TEST_WT_INT16
// #define TEST_WT_INT8
#define TEST_WT_INT4

// Select the input format to test compute
// #define TEST_IN_TYPE INT4
// #define TEST_IN_TYPE INT8
#define TEST_IN_TYPE INT16
// #define TEST_IN_TYPE FP16
// #define TEST_IN_TYPE BF16
// #define TEST_IN_TYPE BBF16
// #define TEST_IN_TYPE FP8E4
// #define TEST_IN_TYPE FP8E5

// --- Helper Function for Verification ---
// Calculates expected result for a single macro
// Returns true on success, false on failure
bool calculate_single_macro_expected(
    uint8_t in_tpy,
    uint8_t wt_tpy,
    const uint16_t (*raw_input_table_ptr)[32],
    const uint16_t (*raw_weight_table_ptr)[32],      // Null for INT types
    const uint16_t (*aligned_weight_table_ptr)[33],  // Used for INT types
    int num_weight_cols,
    std::vector<float>& expected_float_output  // Output vector (size 64)
)
{
    if (!raw_input_table_ptr || !aligned_weight_table_ptr)
    {
        fprintf(stderr, "ERR: Null data table pointer in golden calculation.\n");
        return false;
    }
    if (expected_float_output.size() != 64)
    {
        fprintf(stderr, "ERR: Incorrect size for expected_float_output vector.\n");
        return false;
    }

    uint16_t single_raw_input_vec[32];
    memcpy(single_raw_input_vec, raw_input_table_ptr, 32 * sizeof(uint16_t));

    for (int j = 0; j < num_weight_cols; ++j)
    {
        float expected_dot_product = 0.0;
        for (int i = 0; i < 32; ++i)
        {
            float in_val_d = 0.0;
            float wt_val_d = 0.0;
            // Convert raw input
            switch (in_tpy)
            {
                case INT4:
                {
                    ac_int4_t in_ac = single_raw_input_vec[i];
                    in_val_d = static_cast<float>(in_ac.to_int());
                    break;
                }
                case INT8:
                {
                    ac_int8_t in_ac = single_raw_input_vec[i];
                    in_val_d = static_cast<float>(in_ac.to_int());
                    break;
                }
                case INT16:
                {
                    ac_int16_t in_ac = single_raw_input_vec[i];
                    in_val_d = static_cast<float>(in_ac.to_int());
                    break;
                }
                case FP16:
                {
                    ac_float16_t in_ac;
                    in_ac.set_data(single_raw_input_vec[i]);
                    in_val_d = in_ac.to_float();
                    break;
                }
                case BF16:
                {
                    ac_bfloat16_t in_ac;
                    in_ac.set_data(single_raw_input_vec[i]);
                    in_val_d = in_ac.to_float();
                    break;
                }
                case BBF16:
                {
                    ac_bbfloat16_t in_ac;
                    in_ac.set_data(single_raw_input_vec[i]);
                    in_val_d = in_ac.to_float();
                    break;
                }
                case FP8E4:
                {
                    ac_fp8e4_t in_ac;
                    in_ac.set_data(single_raw_input_vec[i]);
                    in_val_d = in_ac.to_float();
                    break;
                }
                case FP8E5:
                {
                    ac_fp8e5_t in_ac;
                    in_ac.set_data(single_raw_input_vec[i]);
                    in_val_d = in_ac.to_float();
                    break;
                }
                default:
                    break;
            }
            // Get weight value (raw for FP, aligned for INT)
            uint16_t wt_val_verify = 0;
            if (wt_tpy == FP16 || wt_tpy == BF16 || wt_tpy == BBF16 || wt_tpy == FP8E4 ||
                wt_tpy == FP8E5)
            {
                if (!raw_weight_table_ptr)
                {
                    fprintf(stderr, "ERR: raw_weight_table_ptr null in golden calc for FP type\n");
                    return false;
                }
                wt_val_verify = raw_weight_table_ptr[j][i];
            }
            else
            {  // INT types use aligned table
                if (!aligned_weight_table_ptr)
                {
                    fprintf(stderr,
                            "ERR: aligned_weight_table_ptr null in golden calc for INT type\n");
                    return false;
                }
                wt_val_verify = aligned_weight_table_ptr[j][i];
            }
            // Convert weight
            switch (wt_tpy)
            {
                case INT4:
                {
                    ac_int4_t wt_ac = wt_val_verify;
                    wt_val_d = static_cast<float>(wt_ac.to_int());
                    break;
                }
                case INT8:
                {
                    ac_int8_t wt_ac = wt_val_verify;
                    wt_val_d = static_cast<float>(wt_ac.to_int());
                    break;
                }
                case INT16:
                {
                    ac_int16_t wt_ac = wt_val_verify;
                    wt_val_d = static_cast<float>(wt_ac.to_int());
                    break;
                }
                case FP16:
                {
                    ac_float16_t wt_ac;
                    wt_ac.set_data(wt_val_verify);
                    wt_val_d = wt_ac.to_float();
                    break;
                }
                case BF16:
                {
                    ac_bfloat16_t wt_ac;
                    wt_ac.set_data(wt_val_verify);
                    wt_val_d = wt_ac.to_float();
                    break;
                }
                case BBF16:
                {
                    ac_bbfloat16_t wt_ac;
                    wt_ac.set_data(wt_val_verify);
                    wt_val_d = wt_ac.to_float();
                    break;
                }
                case FP8E4:
                {
                    ac_fp8e4_t wt_ac;
                    wt_ac.set_data(wt_val_verify);
                    wt_val_d = wt_ac.to_float();
                    break;
                }
                case FP8E5:
                {
                    ac_fp8e5_t wt_ac;
                    wt_ac.set_data(wt_val_verify);
                    wt_val_d = wt_ac.to_float();
                    break;
                }
                default:
                    break;
            }
            expected_dot_product += in_val_d * wt_val_d;
        }
        expected_float_output[j] = expected_dot_product;
    }
    // Zero out padding
    for (int j = num_weight_cols; j < 64; ++j)
    {
        expected_float_output[j] = 0.0f;
    }
    return true;
}

// --- Test Runner ---
int main()
{
    printf("--- Starting DcimCluster Class Test ---\n");
    int errors = 0;
    int test_count = 0;

    // --- Instantiate Cluster ---
    printf("Instantiating DcimCluster...\n");
    DcimCluster test_cluster;  // Uses default constructor

    // --- Common Setup for Compute Tests ---
    uint8_t in_tpy = TEST_IN_TYPE;
    uint8_t wt_tpy = 0;
    const uint16_t(*raw_input_table_ptr)[32] = nullptr;
    const uint16_t(*raw_weight_table_ptr)[32] = nullptr;
    const uint16_t(*aligned_weight_table_ptr)[33] = nullptr;
    int num_weight_cols = 0;
    uint8_t in_tbn[10] = {0, 1, 2, 3, 3, 4, 5, 6, 7, 8};  // Mapping for input tables

// Determine Test Types and Data Pointers
#ifdef TEST_WT_FP16
    wt_tpy = FP16;
    raw_input_table_ptr = &in_table_tr_fp16[in_tbn[in_tpy]];
    raw_weight_table_ptr = &wt_mhex_fp16[0];
    aligned_weight_table_ptr = &wt_algn_fp16[0];
    num_weight_cols = 16;
#elif defined(TEST_WT_BF16)
    wt_tpy = BF16;
    raw_input_table_ptr = &in_table_tr_bf16[in_tbn[in_tpy]];
    raw_weight_table_ptr = &wt_mhex_bf16[0];
    aligned_weight_table_ptr = &wt_algn_bf16[0];
    num_weight_cols = 16;
#elif defined(TEST_WT_BBF16)
    wt_tpy = BBF16;
    raw_input_table_ptr = &in_table_tr_bbf16[in_tbn[in_tpy]];
    raw_weight_table_ptr = &wt_mhex_bbf16[0];
    aligned_weight_table_ptr = &wt_algn_bbf16[0];
    num_weight_cols = 32;
#elif defined(TEST_WT_FP8E4)
    wt_tpy = FP8E4;
    raw_input_table_ptr = &in_table_tr_fp8e4[in_tbn[in_tpy]];
    raw_weight_table_ptr = &wt_mhex_fp8e4[0];
    aligned_weight_table_ptr = &wt_algn_fp8e4[0];
    num_weight_cols = 32;
#elif defined(TEST_WT_FP8E5)
    wt_tpy = FP8E5;
    raw_input_table_ptr = &in_table_tr_fp8e5[in_tbn[in_tpy]];
    raw_weight_table_ptr = &wt_mhex_fp8e5[0];
    aligned_weight_table_ptr = &wt_algn_fp8e5[0];
    num_weight_cols = 32;
#elif defined(TEST_WT_INT16)
    wt_tpy = INT16;
    raw_input_table_ptr = &in_table_tr_int16[in_tbn[in_tpy]];
    raw_weight_table_ptr = nullptr;
    aligned_weight_table_ptr = &wt_algn_int16[0];
    num_weight_cols = 16;
#elif defined(TEST_WT_INT8)
    wt_tpy = INT8;
    raw_input_table_ptr = &in_table_tr_int8[in_tbn[in_tpy]];
    raw_weight_table_ptr = nullptr;
    aligned_weight_table_ptr = &wt_algn_int8[0];
    num_weight_cols = 32;
#elif defined(TEST_WT_INT4)
    wt_tpy = INT4;
    raw_input_table_ptr = &in_table_tr_int4[in_tbn[in_tpy]];
    raw_weight_table_ptr = nullptr;
    aligned_weight_table_ptr = &wt_algn_int4[0];
    num_weight_cols = 64;
#else
    #error "No weight type defined for compute testing!"
    return 1;
#endif
    printf("Common Setup: Input Type=%d, Weight Type=%d\n", in_tpy, wt_tpy);

    // Prepare Single Raw Input Vector
    uint16_t single_raw_input_vec[32];
    if (!raw_input_table_ptr)
    {
        errors++;
        fprintf(stderr, "FAIL: Raw input table pointer is null. Skipping compute tests.\n");
    }
    else
    {
        memcpy(single_raw_input_vec, raw_input_table_ptr, 32 * sizeof(uint16_t));
    }

    // Prepare Full Raw Input Block (Replicate single input 8 times)
    std::vector<uint8_t> cluster_raw_input_block(DcimCluster::TOTAL_RAW_INPUT_BYTES);
    printf("Replicating single raw input vector %zu times for cluster block...\n",
           DcimCluster::CIME_NUM * DcimCluster::MACROS_PER_ENGINE);
    for (size_t i = 0; i < DcimCluster::CIME_NUM * DcimCluster::MACROS_PER_ENGINE; ++i)
    {
        memcpy(cluster_raw_input_block.data() +
                   (i * DcimCluster::RAW_ELEMENTS_PER_MACRO * DcimCluster::BYTES_PER_ELEMENT),
               single_raw_input_vec,
               DcimCluster::RAW_ELEMENTS_PER_MACRO * DcimCluster::BYTES_PER_ELEMENT);
    }

    // Prepare Single Weight Page
    uint8_t weight_page_buffer[DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW];
    bool page_prep_ok = false;
    if (errors == 0)
    {  // Only proceed if setup is okay so far
        memset(weight_page_buffer, 0, sizeof(weight_page_buffer));
        if (!aligned_weight_table_ptr)
        {
            errors++;
            fprintf(stderr,
                    "FAIL: Aligned weight table pointer is null. Skipping compute tests.\n");
        }
        else
        {
            printf("Converting aligned weights to page format...\n");
            page_prep_ok = convertAlignedColumnMajorToPageRowMajor(
                (const uint8_t*)aligned_weight_table_ptr, wt_tpy, (uint8_t*)weight_page_buffer);
            if (!page_prep_ok)
            {
                errors++;
                fprintf(stderr,
                        "FAIL: convertAlignedColumnMajorToPageRowMajor failed. Skipping compute "
                        "tests.\n");
            }
        }
    }

    // Write Same Page to All Engines/Macros
    int compute_page_idx = 0;
    bool common_setup_ok = (errors == 0 && page_prep_ok);
    if (common_setup_ok)
    {
        printf("Writing same test page %d to all %zu engines (all macros)...\n",
               compute_page_idx,
               DcimCluster::CIME_NUM);
        for (size_t e = 0; e < DcimCluster::CIME_NUM; ++e)
        {
            for (size_t m = 0; m < DcimCluster::MACROS_PER_ENGINE; ++m)
            {
                if (!test_cluster.writePage(e,
                                            m,
                                            compute_page_idx,
                                            reinterpret_cast<const uint8_t*>(weight_page_buffer)))
                {
                    errors++;
                    fprintf(stderr,
                            "FAIL: test_cluster.writePage failed for engine %zu, macro %zu. "
                            "Skipping compute tests.\n",
                            e,
                            m);
                    common_setup_ok = false;
                    break;  // Stop writing for this engine
                }
            }
            if (!common_setup_ok)
                break;  // Stop writing to further engines
        }
    }

    // Calculate Single Macro Expected Result (Float) - Only if setup is OK
    std::vector<float> single_macro_expected_float(64);
    if (common_setup_ok)
    {
        printf("Calculating single macro expected result...\n");
        if (!calculate_single_macro_expected(in_tpy,
                                             wt_tpy,
                                             raw_input_table_ptr,
                                             raw_weight_table_ptr,
                                             aligned_weight_table_ptr,
                                             num_weight_cols,
                                             single_macro_expected_float))
        {
            errors++;
            fprintf(
                stderr,
                "FAIL: Failed to calculate single macro golden result. Skipping compute tests.\n");
            common_setup_ok = false;  // Mark setup as failed
        }
    }

    // --- Compute Tests ---
    // Only run if common setup was successful
    if (common_setup_ok)
    {
        // --- Test Mode 1: Output Fusion ---
        test_count++;
        printf("\n--- Test %d: DcimCluster::computeRawInput - Mode 1 (Output Fusion) ---\n",
               test_count);
        {
            test_cluster.setOperationMode(DcimCluster::OperationMode::PATTERN1_OUTPUT_FUSION);
            printf("Mode set to PATTERN1_OUTPUT_FUSION\n");

            std::vector<uint8_t> output_buffer(DcimCluster::TOTAL_OUTPUT_BYTES);
            memset(output_buffer.data(), 0xAA, output_buffer.size());  // Pre-fill

            printf("Calling computeRawInput...\n");
            bool compute_ok = test_cluster.computeRawInput(cluster_raw_input_block.data(),
                                                           in_tpy,
                                                           compute_page_idx,
                                                           wt_tpy,
                                                           output_buffer.data());

            if (!compute_ok)
            {
                errors++;
                fprintf(stderr, "  FAIL: computeRawInput returned false for Mode 1.\n");
            }
            else
            {
                printf("Verifying results for Mode 1...\n");
                // Expected: Single Macro Result * 8 (4 engines * 2 macros)
                std::vector<float> mode1_expected_float(64);
                size_t total_macros = DcimCluster::CIME_NUM * DcimCluster::MACROS_PER_ENGINE;
                for (int j = 0; j < 64; ++j)
                {
                    mode1_expected_float[j] = single_macro_expected_float[j] * total_macros;
                }

                uint32_t* actual_output_ptr = reinterpret_cast<uint32_t*>(output_buffer.data());
                int compute_errors = 0;
                const float tolerance = 1e-4f;

                // Verify first 64 results
                for (int j = 0; j < 64; ++j)
                {
                    float actual_float = 0.0f;
                    // mh2f_conv(actual_output_ptr[j], FP32, &actual_float);
                    memcpy(&actual_float, actual_output_ptr + j, sizeof(uint32_t));
                    float error = std::fabs(mode1_expected_float[j] - actual_float);
                    float relative_tolerance =
                        tolerance * std::max(1.0f, std::fabs(mode1_expected_float[j]));
                    if (error > relative_tolerance)
                    {
                        fprintf(stderr,
                                "  FAIL [Mode 1]: Mismatch at index %d: Expected %.8f, Got %.8f "
                                "(Hex: 0x%08X), AbsError: %.8f > Tol: %.8f\n",
                                j,
                                mode1_expected_float[j],
                                actual_float,
                                actual_output_ptr[j],
                                error,
                                relative_tolerance);
                        compute_errors++;
                    }
                }
                // Verify padding (next 192 results should be 0, as accumulate writes only first 64)
                for (int j = 64; j < 256; ++j)
                {
                    if (actual_output_ptr[j] != 0)
                    {  // Check if it was overwritten from pre-fill
                        // Accumulate only writes first 64, rest should remain untouched or be
                        // zeroed by cluster impl. Let's assume the implementation zeros unused
                        // parts or leaves them. Check against prefill. If the implementation zeros:
                        // if (actual_output_ptr[j] != 0) {
                        fprintf(stderr,
                                "  WARN [Mode 1]: Output buffer modified beyond expected 64 "
                                "elements at index %d (Got 0x%08X, expected 0xAAAAAAAA)\n",
                                j,
                                actual_output_ptr[j]);
                        // This might not be a strict error depending on contract, treat as warning
                        // for now. compute_errors++;
                    }
                }

                if (compute_errors == 0)
                {
                    printf("  PASS: Mode 1 compute results verified successfully.\n");
                }
                else
                {
                    errors += compute_errors;
                }
            }
        }

        // --- Test Mode 2: Input Dist & Output Fusion ---
        test_count++;
        printf("\n--- Test %d: DcimCluster::computeRawInput - Mode 2 (Input Dist & Output Fusion) "
               "---\n",
               test_count);
        {
            test_cluster.setOperationMode(
                DcimCluster::OperationMode::PATTERN2_INPUT_DIST_OUTPUT_FUSION);
            printf("Mode set to PATTERN2_INPUT_DIST_OUTPUT_FUSION\n");

            std::vector<uint8_t> output_buffer(DcimCluster::TOTAL_OUTPUT_BYTES);
            memset(output_buffer.data(), 0xBB, output_buffer.size());  // Pre-fill

            printf("Calling computeRawInput...\n");
            bool compute_ok = test_cluster.computeRawInput(cluster_raw_input_block.data(),
                                                           in_tpy,
                                                           compute_page_idx,
                                                           wt_tpy,
                                                           output_buffer.data());

            if (!compute_ok)
            {
                errors++;
                fprintf(stderr, "  FAIL: computeRawInput returned false for Mode 2.\n");
            }
            else
            {
                printf("Verifying results for Mode 2...\n");
                // Expected: Single Macro Result * 4 (2 engines * 2 macros per group)
                std::vector<float> mode2_expected_float(64);
                size_t macros_per_group =
                    2 * DcimCluster::MACROS_PER_ENGINE;  // 2 Engines contribute to each sum
                for (int j = 0; j < 64; ++j)
                {
                    mode2_expected_float[j] = single_macro_expected_float[j] * macros_per_group;
                }

                uint32_t* actual_output_ptr = reinterpret_cast<uint32_t*>(output_buffer.data());
                int compute_errors = 0;
                const float tolerance = 1e-4f;

                // Verify first group (0-63)
                for (int j = 0; j < 64; ++j)
                {
                    float actual_float = 0.0f;
                    // mh2f_conv(actual_output_ptr[j], FP32, &actual_float);
                    memcpy(&actual_float, actual_output_ptr + j, sizeof(uint32_t));
                    float error = std::fabs(mode2_expected_float[j] - actual_float);
                    float relative_tolerance =
                        tolerance * std::max(1.0f, std::fabs(mode2_expected_float[j]));
                    if (error > relative_tolerance)
                    {
                        fprintf(stderr,
                                "  FAIL [Mode 2 Grp 1]: Mismatch at index %d: Expected %.8f, Got "
                                "%.8f (Hex: 0x%08X), AbsError: %.8f > Tol: %.8f\n",
                                j,
                                mode2_expected_float[j],
                                actual_float,
                                actual_output_ptr[j],
                                error,
                                relative_tolerance);
                        compute_errors++;
                    }
                }
                // Verify second group (64-127)
                for (int j = 0; j < 64; ++j)
                {
                    float actual_float = 0.0f;
                    // mh2f_conv(actual_output_ptr[j + 64], FP32, &actual_float); // Offset by 64
                    memcpy(&actual_float, actual_output_ptr + j + 64, sizeof(uint32_t));
                    float error = std::fabs(mode2_expected_float[j] -
                                            actual_float);  // Compare against same expected value
                    float relative_tolerance =
                        tolerance * std::max(1.0f, std::fabs(mode2_expected_float[j]));
                    if (error > relative_tolerance)
                    {
                        fprintf(stderr,
                                "  FAIL [Mode 2 Grp 2]: Mismatch at index %d: Expected %.8f, Got "
                                "%.8f (Hex: 0x%08X), AbsError: %.8f > Tol: %.8f\n",
                                j + 64,
                                mode2_expected_float[j],
                                actual_float,
                                actual_output_ptr[j + 64],
                                error,
                                relative_tolerance);
                        compute_errors++;
                    }
                }
                // Verify padding (next 128 results should be untouched)
                for (int j = 128; j < 256; ++j)
                {
                    if (actual_output_ptr[j] != 0)
                    {
                        fprintf(stderr,
                                "  WARN [Mode 2]: Output buffer modified beyond expected 128 "
                                "elements at index %d (Got 0x%08X, expected 0xBBBBBBBB)\n",
                                j,
                                actual_output_ptr[j]);
                        // compute_errors++;
                    }
                }

                if (compute_errors == 0)
                {
                    printf("  PASS: Mode 2 compute results verified successfully.\n");
                }
                else
                {
                    errors += compute_errors;
                }
            }
        }

        // --- Test Mode 3: Input Distribution ---
        test_count++;
        printf("\n--- Test %d: DcimCluster::computeRawInput - Mode 3 (Input Distribution) ---\n",
               test_count);
        {
            test_cluster.setOperationMode(DcimCluster::OperationMode::PATTERN3_INPUT_DIST);
            printf("Mode set to PATTERN3_INPUT_DIST\n");

            std::vector<uint8_t> output_buffer(DcimCluster::TOTAL_OUTPUT_BYTES);
            memset(output_buffer.data(), 0xCC, output_buffer.size());  // Pre-fill

            printf("Calling computeRawInput...\n");
            bool compute_ok = test_cluster.computeRawInput(cluster_raw_input_block.data(),
                                                           in_tpy,
                                                           compute_page_idx,
                                                           wt_tpy,
                                                           output_buffer.data());

            if (!compute_ok)
            {
                errors++;
                fprintf(stderr, "  FAIL: computeRawInput returned false for Mode 3.\n");
            }
            else
            {
                printf("Verifying results for Mode 3...\n");
                // Expected: Single Macro Result * 2 (1 engine * 2 macros per group)
                std::vector<float> mode3_expected_float(64);
                size_t macros_per_engine_mode3 =
                    DcimCluster::MACROS_PER_ENGINE;  // Each engine computes independently
                for (int j = 0; j < 64; ++j)
                {
                    mode3_expected_float[j] =
                        single_macro_expected_float[j] * macros_per_engine_mode3;
                }

                uint32_t* actual_output_ptr = reinterpret_cast<uint32_t*>(output_buffer.data());
                int compute_errors = 0;
                const float tolerance = 1e-4f;

                // Verify all 4 groups (0-63, 64-127, 128-191, 192-255)
                for (int group = 0; group < DcimCluster::CIME_NUM; ++group)
                {
                    for (int j = 0; j < 64; ++j)
                    {
                        int index = group * 64 + j;
                        float actual_float = 0.0f;
                        // mh2f_conv(actual_output_ptr[index], FP32, &actual_float);
                        memcpy(&actual_float, actual_output_ptr + index, sizeof(uint32_t));
                        float error =
                            std::fabs(mode3_expected_float[j] -
                                      actual_float);  // Compare against same expected value
                        float relative_tolerance =
                            tolerance * std::max(1.0f, std::fabs(mode3_expected_float[j]));
                        if (error > relative_tolerance)
                        {
                            fprintf(stderr,
                                    "  FAIL [Mode 3 Grp %d]: Mismatch at index %d: Expected %.8f, "
                                    "Got %.8f (Hex: 0x%08X), AbsError: %.8f > Tol: %.8f\n",
                                    group,
                                    index,
                                    mode3_expected_float[j],
                                    actual_float,
                                    actual_output_ptr[index],
                                    error,
                                    relative_tolerance);
                            compute_errors++;
                        }
                    }
                }

                if (compute_errors == 0)
                {
                    printf("  PASS: Mode 3 compute results verified successfully.\n");
                }
                else
                {
                    errors += compute_errors;
                }
            }
        }

    }  // End of if(common_setup_ok) block for compute tests

    // --- Final Summary ---
    printf("\n--- DcimCluster Test Summary ---\n");
    if (errors == 0)
    {
        printf("PASS: All %d compute tests passed!\n", test_count);
        return 0;  // Success
    }
    else
    {
        printf("FAIL: %d errors found in %d compute tests.\n", errors, test_count);
        return 1;  // Failure
    }
}