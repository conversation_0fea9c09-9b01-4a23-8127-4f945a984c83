#include <math.h>
#include <string.h>
#include "../inc/dcim_com.h"
#include "../inc/input_table.h"
#include "../inc/weight_table.h"
#include "ac_datatypes.h"

int main(int argc, char** argv)
{
    // input and weight
    uint16_t in_mhex[32] = {0};
    uint16_t wt_algn[64][33];
    uint16_t wt_mhex[64][32];

    uint16_t in_algn_16b[33] = {0};
    uint32_t hex_out = 0;

    float flt_out = 0.0;
    float rm_out = 0.0;

    uint8_t in_tpy = INT4;
    uint8_t wt_tpy = INT4;
    // INT4-INT8-INT12-INT16-FP32-FP16-BF16-BBF16-FP8E4-FP8E5
    uint8_t wt_tbl[10] = {64, 32, 16, 16, 16, 16, 16, 32, 32, 32};
    uint8_t in_tbn[10] = {0, 1, 2, 3, 3, 4, 5, 6, 7, 8};

// #define TEST_WT_FP16
#ifdef TEST_WT_FP16
    in_tpy = INT4;
    wt_tpy = FP16;

    for (int i = 0; i < 32; i++)
    {
        in_mhex[i] = in_table_tr_fp16[in_tbn[in_tpy]][i];
    }
    for (int j = 0; j < wt_tbl[wt_tpy]; j++)
    {
        for (int i = 0; i < 33; i++)
        {
            wt_algn[j][i] = wt_algn_fp16[j][i];
        }
        for (int i = 0; i < 32; i++)
        {
            wt_mhex[j][i] = wt_mhex_fp16[j][i];
        }
    }
#endif

// #define TEST_WT_BF16
#ifdef TEST_WT_BF16
    in_tpy = INT4;
    wt_tpy = BF16;

    for (int i = 0; i < 32; i++)
    {
        in_mhex[i] = in_table_tr_bf16[in_tbn[in_tpy]][i];
    }
    for (int j = 0; j < wt_tbl[wt_tpy]; j++)
    {
        for (int i = 0; i < 33; i++)
        {
            wt_algn[j][i] = wt_algn_bf16[j][i];
        }
        for (int i = 0; i < 32; i++)
        {
            wt_mhex[j][i] = wt_mhex_bf16[j][i];
        }
    }
#endif

// #define TEST_WT_BBF16
#ifdef TEST_WT_BBF16
    in_tpy = INT4;
    wt_tpy = BBF16;

    for (int i = 0; i < 32; i++)
    {
        in_mhex[i] = in_table_tr_bbf16[in_tbn[in_tpy]][i];
    }
    for (int j = 0; j < wt_tbl[wt_tpy]; j++)
    {
        for (int i = 0; i < 33; i++)
        {
            wt_algn[j][i] = wt_algn_bbf16[j][i];
        }
        for (int i = 0; i < 32; i++)
        {
            wt_mhex[j][i] = wt_mhex_bbf16[j][i];
        }
    }
#endif

// #define TEST_WT_FP8E4
#ifdef TEST_WT_FP8E4
    in_tpy = INT4;
    wt_tpy = FP8E4;

    for (int i = 0; i < 32; i++)
    {
        in_mhex[i] = in_table_tr_fp8e4[in_tbn[in_tpy]][i];
    }
    for (int j = 0; j < wt_tbl[wt_tpy]; j++)
    {
        for (int i = 0; i < 33; i++)
        {
            wt_algn[j][i] = wt_algn_fp8e4[j][i];
        }
        for (int i = 0; i < 32; i++)
        {
            wt_mhex[j][i] = wt_mhex_fp8e4[j][i];
        }
    }
#endif

// #define TEST_WT_FP8E5
#ifdef TEST_WT_FP8E5
    in_tpy = INT4;
    wt_tpy = FP8E5;

    for (int i = 0; i < 32; i++)
    {
        in_mhex[i] = in_table_tr_fp8e5[in_tbn[in_tpy]][i];
    }
    for (int j = 0; j < wt_tbl[wt_tpy]; j++)
    {
        for (int i = 0; i < 33; i++)
        {
            wt_algn[j][i] = wt_algn_fp8e5[j][i];
        }
        for (int i = 0; i < 32; i++)
        {
            wt_mhex[j][i] = wt_mhex_fp8e5[j][i];
        }
    }
#endif

// #define TEST_WT_INT16
#ifdef TEST_WT_INT16
    in_tpy = INT4;
    wt_tpy = INT16;

    for (int i = 0; i < 32; i++)
    {
        in_mhex[i] = in_table_tr_int16[in_tbn[in_tpy]][i];
    }
    for (int j = 0; j < wt_tbl[wt_tpy]; j++)
    {
        for (int i = 0; i < 33; i++)
        {
            wt_algn[j][i] = wt_algn_int16[j][i];
        }
        for (int i = 0; i < 32; i++)
        {
            wt_mhex[j][i] = wt_algn_int16[j][i];
        }
    }
#endif

// #define TEST_WT_INT8
#ifdef TEST_WT_INT8
    in_tpy = INT4;
    wt_tpy = INT8;

    for (int i = 0; i < 32; i++)
    {
        in_mhex[i] = in_table_tr_int8[in_tbn[in_tpy]][i];
    }
    for (int j = 0; j < wt_tbl[wt_tpy]; j++)
    {
        for (int i = 0; i < 33; i++)
        {
            wt_algn[j][i] = wt_algn_int8[j][i];
        }
        for (int i = 0; i < 32; i++)
        {
            wt_mhex[j][i] = wt_algn_int8[j][i];
        }
    }
#endif

// #define TEST_WT_INT4
#ifdef TEST_WT_INT4
    in_tpy = INT4;
    wt_tpy = INT4;

    for (int i = 0; i < 32; i++)
    {
        // in_mhex[i] = in_table_tr_int4[in_tbn[in_tpy]][i];
        in_mhex[i] = 0;
    }
    for (int j = 0; j < wt_tbl[wt_tpy]; j++)
    {
        for (int i = 0; i < 33; i++)
        {
            // wt_algn[j][i] = wt_algn_int4[j][i];
            wt_algn[j][i] = 1;
        }
        for (int i = 0; i < 32; i++)
        {
            // wt_mhex[j][i] = wt_algn_int4[j][i];
            wt_mhex[j][i] = 1;
        }
    }
#endif

    // input format conv
    if (in_tpy < 4)
    {
        for (int i = 0; i < 32; i++)
            // in_algn_16b[i] = in_mhex[i];
            in_algn_16b[i] = 0;
        in_algn_16b[32] = 0;
    }
    else
    {
        float_data_align(in_mhex, in_tpy, in_algn_16b);
    }
    // for(int j=0;j<wt_tbl[wt_tpy];j++) {
    //   printf("=> wt tab: %02d \n", j);
    //   // dcim_macro
    //   hex_out = dcim_macro_com(wt_algn[j], wt_tpy, in_algn_16b, in_tpy);
    //   mh2f_conv(hex_out, FP32, flt_out);
    //   printf("\n");
    // }
    // 添加验证代码
    for (int j = 0; j < wt_tbl[wt_tpy]; j++)
    {
        printf("\n=== Verification for weight row %d ===\n", j);

        // --- Recalculate expected result using ac_datatypes ---
        float expected = 0.0;
        printf("Input vector (INT4): ");
        for (int i = 0; i < 32; ++i)
        {
            ac_int4_t in_ac = in_mhex[i];
            printf("%3d ", in_ac.to_int());  // Print actual int value
        }
        printf("\n");

        printf("Weight vector (%d):  ", wt_tpy);  // Indicate weight type
        for (int i = 0; i < 32; ++i)
        {
            double wt_val_d = 0.0;
            // Use original wt_mhex data for calculation
            switch (wt_tpy)
            {
                case INT4:
                {
                    ac_int4_t wt_ac = wt_mhex[j][i];
                    wt_val_d = wt_ac.to_int();
                    printf("%3d ", wt_ac.to_int());
                    break;
                }
                case INT8:
                {
                    ac_int8_t wt_ac = wt_mhex[j][i];
                    wt_val_d = wt_ac.to_int();
                    printf("%3d ", wt_ac.to_int());
                    break;
                }
                case INT16:
                {
                    ac_int16_t wt_ac = wt_mhex[j][i];
                    wt_val_d = wt_ac.to_int();
                    printf("%6d ", wt_ac.to_int());  // Wider print for INT16
                    break;
                }
                    // Add other integer types if needed (e.g., INT12)

                case FP16:
                {
                    ac_float16_t wt_ac;
                    wt_ac.set_data(wt_mhex[j][i]);
                    wt_val_d = wt_ac.to_float();
                    printf("%9.4f ", wt_ac.to_float());  // Print float value
                    break;
                }
                case BF16:
                {
                    ac_bfloat16_t wt_ac;
                    wt_ac.set_data(wt_mhex[j][i]);
                    wt_val_d = wt_ac.to_float();
                    printf("%9.4f ", wt_ac.to_float());
                    break;
                }
                case BBF16:
                {
                    ac_bbfloat16_t wt_ac;
                    wt_ac.set_data(wt_mhex[j][i]);
                    wt_val_d = wt_ac.to_float();
                    printf("%9.4f ", wt_ac.to_float());
                    break;
                }
                case FP8E4:
                {
                    ac_fp8e4_t wt_ac;
                    wt_ac.set_data(wt_mhex[j][i]);
                    wt_val_d = wt_ac.to_float();
                    printf("%9.4f ", wt_ac.to_float());
                    break;
                }
                case FP8E5:
                {
                    ac_fp8e5_t wt_ac;
                    wt_ac.set_data(wt_mhex[j][i]);
                    wt_val_d = wt_ac.to_float();
                    printf("%9.4f ", wt_ac.to_float());
                    break;
                }
                    // FP32 might need special handling if it uses uint32_t in wt_mhex
                default:
                    printf("ERR ");  // Indicate unsupported type
                    break;           // Handle error or default case
            }

            // Calculate contribution to expected value
            // Assuming input is always INT4 for now based on example code.
            ac_int4_t in_ac = in_mhex[i];
            float in_val_d = (float)in_ac.to_int();
            expected += in_val_d * wt_val_d;
        }
        printf("\n");

        printf("Expected result: %f\n", expected);  // Print recalculated expected value

        // 4. 获取实际结果
        hex_out = dcim_macro_com(wt_algn[j], wt_tpy, in_algn_16b, in_tpy);
        float actual = 0.0;
        memcpy(&actual, &hex_out, sizeof(float));

        // mh2f_conv(hex_out, FP32, &actual); //与上述memcpy等价
        printf("Actual result: %f\n", actual);

        // 5. 计算误差
        double error = fabs(expected - actual);  // Compare recalculated expected vs actual
        printf("Absolute error: %f\n", error);

        printf("\n");
    }
}