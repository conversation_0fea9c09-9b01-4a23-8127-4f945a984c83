#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <cmath>  // For fabs
#include <vector>

// Define number of 16-bit words per row (DCIM_NUM_BYTES_PER_ROW/2)
#define DCIM_NUM_WORDS_PER_ROW (DCIM_NUM_BYTES_PER_ROW / 2)

// Include project headers
extern "C"
{  // Use extern "C" for C headers
#include "../inc/dcim_com.h"
#include "../inc/dcim_compute.h"
#include "../inc/dcim_matrix_utils.h"  // Include the new utils header
#include "../inc/dcim_storage.h"
}
#include "../inc/input_table.h"   // Contains raw input vectors
#include "../inc/weight_table.h"  // Contains raw and aligned weight matrices
#include "ac_datatypes.h"         // For verification using ac_types

// --- Test Configuration ---
// Select the weight format to test by uncommenting one define
// #define TEST_WT_FP16
// #define TEST_WT_BF16
// #define TEST_WT_BBF16
// #define TEST_WT_FP8E4
// #define TEST_WT_FP8E5
// #define TEST_WT_INT16
// #define TEST_WT_INT8
// #define TEST_WT_INT4

// Select the input format to test
#define TEST_IN_TYPE INT4  // Example: Use INT4 input for all weight tests

// --- Helper Function for Binary Printing ---
void print_binary(uint16_t value, int bits)
{
    for (int i = bits - 1; i >= 0; i--)
    {
        printf("%c", (value >> i) & 1 ? '1' : '0');
    }
}

// --- Other Helper Functions (Commented out) ---
/* ... existing commented out functions ... */

// --- Main Test ---
int main()
{
    printf("--- Starting DCIM Storage Compute Test ---\n");

    // --- Determine Test Types and Data Pointers ---
    uint8_t in_tpy = TEST_IN_TYPE;
    uint8_t wt_tpy = 0;  // Determined by #ifdefs
    const uint16_t(*raw_input_table_ptr)[32] = nullptr;
    const uint16_t(*raw_weight_table_ptr)[32] = nullptr;
    const uint16_t(*aligned_weight_table_ptr)[33] = nullptr;
    int num_weight_cols = 0;  // Max columns for the selected weight type

    // Mapping from data_fmt enum index to table index in input_table.h
    uint8_t in_tbn[10] = {
        0, 1, 2, 3, 3, 4, 5, 6, 7, 8};  // INT4,INT8,INT12,INT16,FP32,FP16,BF16,BBF16,FP8E4,FP8E5

#ifdef TEST_WT_FP16
    wt_tpy = FP16;
    raw_input_table_ptr = &in_table_tr_fp16[in_tbn[in_tpy]];
    raw_weight_table_ptr = &wt_mhex_fp16[0];
    aligned_weight_table_ptr = &wt_algn_fp16[0];
    num_weight_cols = 16;
    printf("Testing: Input=%d (INT4), Weight=%d (FP16)\n", in_tpy, wt_tpy);
#elif defined(TEST_WT_BF16)
    wt_tpy = BF16;
    raw_input_table_ptr = &in_table_tr_bf16[in_tbn[in_tpy]];
    raw_weight_table_ptr = &wt_mhex_bf16[0];
    aligned_weight_table_ptr = &wt_algn_bf16[0];
    num_weight_cols = 16;
    printf("Testing: Input=%d (INT4), Weight=%d (BF16)\n", in_tpy, wt_tpy);
#elif defined(TEST_WT_BBF16)
    wt_tpy = BBF16;
    raw_input_table_ptr = &in_table_tr_bbf16[in_tbn[in_tpy]];
    raw_weight_table_ptr = &wt_mhex_bbf16[0];
    aligned_weight_table_ptr = &wt_algn_bbf16[0];
    num_weight_cols = 32;  // BBF16 uses 32 columns according to weight_table.h
    printf("Testing: Input=%d (INT4), Weight=%d (BBF16)\n", in_tpy, wt_tpy);
#elif defined(TEST_WT_FP8E4)
    wt_tpy = FP8E4;
    raw_input_table_ptr = &in_table_tr_fp8e4[in_tbn[in_tpy]];
    raw_weight_table_ptr = &wt_mhex_fp8e4[0];
    aligned_weight_table_ptr = &wt_algn_fp8e4[0];
    num_weight_cols = 32;
    printf("Testing: Input=%d (INT4), Weight=%d (FP8E4)\n", in_tpy, wt_tpy);
#elif defined(TEST_WT_FP8E5)
    wt_tpy = FP8E5;
    raw_input_table_ptr = &in_table_tr_fp8e5[in_tbn[in_tpy]];
    raw_weight_table_ptr = &wt_mhex_fp8e5[0];
    aligned_weight_table_ptr = &wt_algn_fp8e5[0];
    num_weight_cols = 32;
    printf("Testing: Input=%d (INT4), Weight=%d (FP8E5)\n", in_tpy, wt_tpy);
#elif defined(TEST_WT_INT16)
    wt_tpy = INT16;
    raw_input_table_ptr = &in_table_tr_int16[in_tbn[in_tpy]];
    // raw_weight_table_ptr = nullptr; // Not assigned for INT types
    aligned_weight_table_ptr = &wt_algn_int16[0];
    num_weight_cols = 16;
    printf("Testing: Input=%d (INT4), Weight=%d (INT16)\n", in_tpy, wt_tpy);
#elif defined(TEST_WT_INT8)
    wt_tpy = INT8;
    raw_input_table_ptr = &in_table_tr_int8[in_tbn[in_tpy]];
    // raw_weight_table_ptr = nullptr; // Not assigned for INT types
    aligned_weight_table_ptr = &wt_algn_int8[0];
    num_weight_cols = 32;
    printf("Testing: Input=%d (INT4), Weight=%d (INT8)\n", in_tpy, wt_tpy);
#elif defined(TEST_WT_INT4)
    wt_tpy = INT4;
    raw_input_table_ptr = &in_table_tr_int4[in_tbn[in_tpy]];
    // raw_weight_table_ptr = nullptr; // Not assigned for INT types
    aligned_weight_table_ptr = &wt_algn_int4[0];
    num_weight_cols = 64;
    printf("Testing: Input=%d (INT4), Weight=%d (INT4)\n", in_tpy, wt_tpy);
#else
    #error "No weight type defined for testing!"
    return 1;
#endif

    // --- Load Data ---
    uint16_t raw_input_vec[32];
    memcpy(raw_input_vec, raw_input_table_ptr, 32 * sizeof(uint16_t));

    // Prepare aligned weight page data using matrix utils
    uint16_t aligned_weight_page[DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_WORDS_PER_ROW];
    memset(aligned_weight_page, 0, sizeof(aligned_weight_page));  // Clear buffer first

    bool conversion_ok = false;
    const uint16_t* aligned_col_major_ptr =
        nullptr;  // Pointer to the data needed by convertAlignedColumnMajorToPageRowMajor

    // Allocate a temporary buffer for FP types that need raw->aligned conversion first
    std::vector<uint16_t> temp_aligned_col_major_buffer;

    if (wt_tpy == FP16 || wt_tpy == BF16 || wt_tpy == BBF16 || wt_tpy == FP8E4 || wt_tpy == FP8E5)
    {
        if (raw_weight_table_ptr == nullptr)
        {
            fprintf(stderr, "Error: Raw weight table pointer is null for FP type %d.\n", wt_tpy);
            return 1;
        }
        // Step 1: Convert Raw Column Major to Aligned Column Major
        size_t buffer_size = num_weight_cols * DCIM_NUM_ROWS_PER_PAGE;
        temp_aligned_col_major_buffer.resize(buffer_size);
        printf("Converting raw weights to aligned column-major format...\n");
        conversion_ok = convertRawToAlignedColumnMajor(
            (const uint8_t*)raw_weight_table_ptr,  // Cast from [cols][32] to flat pointer
            wt_tpy,
            (uint8_t*)temp_aligned_col_major_buffer.data());
        if (!conversion_ok)
        {
            fprintf(stderr, "Error during convertRawToAlignedColumnMajor for type %d.\n", wt_tpy);
            return 1;
        }
        aligned_col_major_ptr = temp_aligned_col_major_buffer.data();
    }
    else if (wt_tpy == INT16 || wt_tpy == INT8 || wt_tpy == INT4)
    {
        // For INT types, the aligned_weight_table_ptr already points to the required aligned
        // column-major data
        if (aligned_weight_table_ptr == nullptr)
        {
            fprintf(
                stderr, "Error: Aligned weight table pointer is null for INT type %d.\n", wt_tpy);
            return 1;
        }
        aligned_col_major_ptr =
            (const uint16_t*)aligned_weight_table_ptr;  // Cast from [cols][33] to flat pointer
        conversion_ok = true;                           // No first step conversion needed
    }
    else
    {
        fprintf(stderr, "Error: Unsupported weight type %d for page preparation.\n", wt_tpy);
        return 1;
    }

    // Step 2: Convert Aligned Column Major to Page Row Major format
    if (conversion_ok && aligned_col_major_ptr != nullptr)
    {
        printf("Converting aligned column-major weights to page (row-major) format...\n");
        conversion_ok = convertAlignedColumnMajorToPageRowMajor(
            (const uint8_t*)aligned_col_major_ptr, wt_tpy, (uint8_t*)aligned_weight_page);
        if (!conversion_ok)
        {
            fprintf(stderr,
                    "Error during convertAlignedColumnMajorToPageRowMajor for type %d.\n",
                    wt_tpy);
            return 1;
        }
    }
    else if (conversion_ok && aligned_col_major_ptr == nullptr)
    {
        fprintf(stderr,
                "Internal Error: aligned_col_major_ptr is null after supposed successful "
                "preparation step.\n");
        return 1;
    }
    // aligned_weight_page now contains the correctly formatted data for storage/computation

    // --- Prepare for Computation ---
    int test_page_idx = 0;
    // printf("Writing aligned test page %d to storage...\n", test_page_idx); // Suppress
    // intermediate output
    write_storage_page(test_page_idx, (uint8_t*)aligned_weight_page);

    // Align the raw input vector
    uint16_t aligned_input_vec[DCIM_NUM_ROWS_PER_PAGE];  // Size 33
    // printf("Aligning input vector (Type: %d)...\n", in_tpy); // Suppress intermediate output
    if (in_tpy < 4)
    {  // Handle INT types separately if needed
        // printf("  (Using INT alignment: copy + zero exponent)\n"); // Suppress intermediate
        // output
        memcpy(aligned_input_vec, raw_input_vec, 32 * sizeof(uint16_t));
        aligned_input_vec[32] = 0;  // Set exponent part to 0 for INT types
    }
    else
    {
        // printf("  (Using float_data_align)\n"); // Suppress intermediate output
        // Assuming float_data_align works correctly for the selected FP input type
        float_data_align(raw_input_vec, in_tpy, aligned_input_vec);
    }
    // print_u16_vector("Raw Input        ", raw_input_vec, 32);
    // print_u16_vector("Aligned Input    ", aligned_input_vec, 33);

    // --- Execute Computation ---
    uint32_t actual_output_hex[64];
    // printf("Calling dcim_compute_from_storage_aligned_input...\n"); // Suppress intermediate
    // output
    dcim_compute_from_storage_aligned_input(
        (uint8_t*)aligned_input_vec, in_tpy, test_page_idx, wt_tpy, (uint8_t*)actual_output_hex);

    // --- Calculate Expected Results using ac_datatypes ---
    // printf("Calculating expected results using ac_datatypes...\n"); // Suppress intermediate
    // output
    std::vector<float> expected_output_float(64, 0.0);  // Use double for intermediate precision

    for (int j = 0; j < num_weight_cols; ++j)
    {
        float expected_dot_product = 0.0;
        for (int i = 0; i < 32; ++i)
        {
            float in_val_d = 0.0;
            float wt_val_d = 0.0;

            // Convert raw input based on TEST_IN_TYPE
            switch (in_tpy)
            {
                case INT4:
                {
                    ac_int4_t in_ac = raw_input_vec[i];
                    in_val_d = static_cast<float>(in_ac.to_int());
                    break;
                }
                case INT8:
                {
                    ac_int8_t in_ac = raw_input_vec[i];
                    in_val_d = static_cast<float>(in_ac.to_int());
                    break;
                }
                case INT16:
                {
                    ac_int16_t in_ac = raw_input_vec[i];
                    in_val_d = static_cast<float>(in_ac.to_int());
                    break;
                }
                case FP16:
                {
                    ac_float16_t in_ac;
                    in_ac.set_data(raw_input_vec[i]);
                    in_val_d = in_ac.to_float();
                    break;
                }
                case BF16:
                {
                    ac_bfloat16_t in_ac;
                    in_ac.set_data(raw_input_vec[i]);
                    in_val_d = in_ac.to_float();
                    break;
                }
                case BBF16:
                {
                    ac_bbfloat16_t in_ac;
                    in_ac.set_data(raw_input_vec[i]);
                    in_val_d = in_ac.to_float();
                    break;
                }
                case FP8E4:
                {
                    ac_fp8e4_t in_ac;
                    in_ac.set_data(raw_input_vec[i]);
                    in_val_d = in_ac.to_float();
                    break;
                }
                case FP8E5:
                {
                    ac_fp8e5_t in_ac;
                    in_ac.set_data(raw_input_vec[i]);
                    in_val_d = in_ac.to_float();
                    break;
                }
                default:
                    printf("ERROR: Unsupported input type for verification!\n");
                    break;
            }

            // Get weight value for verification. Use raw for FP, aligned for INT.
            uint16_t wt_val_for_verification = 0;
            if (wt_tpy >= FP32)
            {  // FP types have separate raw data
                if (raw_weight_table_ptr == nullptr)
                {
                    printf("ERROR: raw_weight_table_ptr is null for FP type!\n");
                    continue;
                }
                wt_val_for_verification = raw_weight_table_ptr[j][i];
            }
            else
            {  // INT types use the aligned data table directly for verification
                if (aligned_weight_table_ptr == nullptr)
                {
                    printf("ERROR: aligned_weight_table_ptr is null for INT type!\n");
                    continue;
                }
                // Accessing aligned data [col][row]
                wt_val_for_verification = aligned_weight_table_ptr[j][i];
            }

            // Convert weight based on TEST_WT_TYPE using the determined value
            switch (wt_tpy)
            {
                case INT4:
                {
                    ac_int4_t wt_ac = wt_val_for_verification;
                    wt_val_d = static_cast<float>(wt_ac.to_int());
                    break;
                }
                case INT8:
                {
                    ac_int8_t wt_ac = wt_val_for_verification;
                    wt_val_d = static_cast<float>(wt_ac.to_int());
                    break;
                }
                case INT16:
                {
                    ac_int16_t wt_ac = wt_val_for_verification;
                    wt_val_d = static_cast<float>(wt_ac.to_int());
                    break;
                }
                case FP16:
                {
                    ac_float16_t wt_ac;
                    wt_ac.set_data(wt_val_for_verification);
                    wt_val_d = wt_ac.to_float();
                    break;
                }
                case BF16:
                {
                    ac_bfloat16_t wt_ac;
                    wt_ac.set_data(wt_val_for_verification);
                    wt_val_d = wt_ac.to_float();
                    break;
                }
                case BBF16:
                {
                    ac_bbfloat16_t wt_ac;
                    wt_ac.set_data(wt_val_for_verification);
                    wt_val_d = wt_ac.to_float();
                    break;
                }
                case FP8E4:
                {
                    ac_fp8e4_t wt_ac;
                    wt_ac.set_data(wt_val_for_verification);
                    wt_val_d = wt_ac.to_float();
                    break;
                }
                case FP8E5:
                {
                    ac_fp8e5_t wt_ac;
                    wt_ac.set_data(wt_val_for_verification);
                    wt_val_d = wt_ac.to_float();
                    break;
                }
                default:
                    printf("ERROR: Unsupported weight type (%d) for verification!\n", wt_tpy);
                    break;
            }

            expected_dot_product += in_val_d * wt_val_d;
        }
        expected_output_float[j] = expected_dot_product;
    }

    // --- Verify Results ---
    // printf("Verifying results...\n"); // Suppress intermediate output
    int errors = 0;
    float max_abs_error = 0.0f;
    // float max_rel_error = 0.0f; // Relative error not tracked per row anymore
    const float tolerance = 1e-4f;  // Tolerance for float comparison
    struct FailureInfo
    {
        int index;
        float expected;
        float actual;
        uint32_t actual_hex;
        float error;
        bool is_padding_error;
    };
    std::vector<FailureInfo> failures;

    for (int j = 0; j < num_weight_cols; ++j)
    {  // Iterate only through relevant columns
        printf("\n=== Verification for weight row %d ===\n", j);

        // Print Input Vector (assuming INT4 as per TEST_IN_TYPE)
        printf("Input vector (INT4): ");
        for (int i = 0; i < 32; ++i)
        {
            ac_int4_t in_ac = raw_input_vec[i];
            printf("%3d ", in_ac.to_int());
        }
        printf("\n");

        // Print Aligned Input Vector (Binary)
        printf("align Input Vector(%d)(binary): ", in_tpy);
        for (int i = 0; i < 33; ++i)
        {
            print_binary(aligned_input_vec[i], 16);  // Assuming 16 bits for aligned input elements
            printf(" ");
        }
        printf("\n");

        // Print Aligned Weight Mantissa Vector (Binary)
        printf("align Weight mant vector (%d)(binary): ", wt_tpy);
        if (aligned_weight_table_ptr == nullptr)
        {
            printf("ERR_ALGN_NULL\n");
        }
        else
        {
            for (int i = 0; i < 32; ++i)
            {
                print_binary(aligned_weight_table_ptr[j][i],
                             16);  // Assuming 16 bits for aligned weight elements
                printf(" ");
            }
            printf("\n");
        }

        // Print Aligned Weight Exponent (Binary)
        printf("align Weight exp (%d)(binary): ", wt_tpy);
        if (aligned_weight_table_ptr == nullptr)
        {
            printf("ERR_ALGN_NULL\n");
        }
        else
        {
            print_binary(aligned_weight_table_ptr[j][32], 16);  // Assuming 16 bits for exponent
            printf("\n");
        }

        // Print Weight Vector (Values - using original values used for expected calculation)
        printf("Weight vector (%d):  ", wt_tpy);
        for (int i = 0; i < 32; ++i)
        {
            uint16_t wt_val_for_verification = 0;
            if (wt_tpy >= FP32)
            {  // FP types
                if (raw_weight_table_ptr == nullptr)
                {
                    printf("ERR_RAW_NULL ");
                    continue;
                }
                wt_val_for_verification = raw_weight_table_ptr[j][i];
            }
            else
            {  // INT types
                if (aligned_weight_table_ptr == nullptr)
                {
                    printf("ERR_ALGN_NULL ");
                    continue;
                }
                wt_val_for_verification = aligned_weight_table_ptr[j][i];
            }

            switch (wt_tpy)
            {
                case INT4:
                {
                    ac_int4_t wt_ac = wt_val_for_verification;
                    printf("%3d ", wt_ac.to_int());
                    break;
                }
                case INT8:
                {
                    ac_int8_t wt_ac = wt_val_for_verification;
                    printf("%3d ", wt_ac.to_int());
                    break;
                }
                case INT16:
                {
                    ac_int16_t wt_ac = wt_val_for_verification;
                    printf("%6d ", wt_ac.to_int());
                    break;
                }
                case FP16:
                {
                    ac_float16_t wt_ac;
                    wt_ac.set_data(wt_val_for_verification);
                    printf("%9.4f ", wt_ac.to_float());
                    break;
                }
                case BF16:
                {
                    ac_bfloat16_t wt_ac;
                    wt_ac.set_data(wt_val_for_verification);
                    printf("%9.4f ", wt_ac.to_float());
                    break;
                }
                case BBF16:
                {
                    ac_bbfloat16_t wt_ac;
                    wt_ac.set_data(wt_val_for_verification);
                    printf("%9.4f ", wt_ac.to_float());
                    break;
                }
                case FP8E4:
                {
                    ac_fp8e4_t wt_ac;
                    wt_ac.set_data(wt_val_for_verification);
                    printf("%9.4f ", wt_ac.to_float());
                    break;
                }
                case FP8E5:
                {
                    ac_fp8e5_t wt_ac;
                    wt_ac.set_data(wt_val_for_verification);
                    printf("%9.4f ", wt_ac.to_float());
                    break;
                }
                default:
                {
                    printf("ERR_TYPE ");
                    break;
                }
            }
        }
        printf("\n");

        // Print Expected Result
        float expected_float = expected_output_float[j];
        printf("Expected result: %f\n", expected_float);

        // Print Actual Result
        float actual_output_float = 0.0f;
        mh2f_conv(actual_output_hex[j], FP32, &actual_output_float);
        printf("Actual result: %f\n", actual_output_float);

        // Print Absolute Error and Check
        float error = std::fabs(expected_float - actual_output_float);
        printf("Absolute error: %f\n", error);

        // Update error tracking
        if (error > max_abs_error)
            max_abs_error = error;
        // Simplified error check based on absolute error only for PASS/FAIL summary
        if (error > tolerance)
        {  // Check only absolute error against tolerance
            failures.push_back(
                {j, expected_float, actual_output_float, actual_output_hex[j], error, false});
            errors++;
        }
    }

    // Check padding columns separately (optional, can be removed if not needed)
    for (int j = num_weight_cols; j < 64; ++j)
    {
        if (actual_output_hex[j] != 0)
        {  // Check padding error
            // printf("\nERROR at padding index %d: Expected 0x00000000, Got 0x%08X\n", j,
            // actual_output_hex[j]); // Don't print here, store for summary
            failures.push_back({j, 0.0f, 0.0f, actual_output_hex[j], 0.0f, true});
            errors++;
        }
    }

    printf("\n--- Test Summary ---\n");
    if (errors == 0)
    {
        printf("PASS: All results verified successfully within tolerance (%.1e).\n", tolerance);
    }
    else
    {
        printf("FAIL: %d errors found.\n", errors);
        printf("Failed test cases:\n");
        for (const auto& fail : failures)
        {
            if (fail.is_padding_error)
            {
                printf("  Padding Error at index %d: Expected 0x00000000, Got 0x%08X\n",
                       fail.index,
                       fail.actual_hex);
            }
            else
            {
                printf(
                    "  Error at index %d: Expected %.8f, Got %.8f (Hex: 0x%08X), AbsError: %.8f\n",
                    fail.index,
                    fail.expected,
                    fail.actual,
                    fail.actual_hex,
                    fail.error);
            }
        }
    }
    printf("Max absolute float error (for relevant columns): %.8f\n", max_abs_error);
    // printf("Max relative float error (for relevant columns): %.8f\n", max_rel_error); // Relative
    // error not printed per row anymore
    printf("Note: Comparison uses ac_datatypes for expected values based on raw inputs/weights.\n");
    printf("      Actual result is from dcim_macro_com (FP32 output) converted back to float.\n");
    printf("      Ensure aligned weight page loading logic is correct for the tested type.\n");

    return errors;
}