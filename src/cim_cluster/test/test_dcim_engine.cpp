#include <cmath>  // For fabs
#include <cstdint>
#include <cstdio>   // For printf
#include <cstring>  // For memcmp, memset, memcpy
#include <iostream>
#include <numeric>  // For std::accumulate (optional)
#include <vector>

// Include the class header
#include "../inc/dcim_engine.hpp"  // Changed from dcim_array.hpp

// Define number of 16-bit words per row (DCIM_NUM_BYTES_PER_ROW/2)
#define DCIM_NUM_WORDS_PER_ROW (DCIM_NUM_BYTES_PER_ROW / 2)

// Include C headers within extern "C"
extern "C"
{
#include "../inc/dcim_com.h"
#include "../inc/dcim_matrix_utils.h"  // For preparing test data
}

// Include test data tables
#include "../inc/input_table.h"
#include "../inc/weight_table.h"
#include "ac_datatypes.h"  // For verification

// --- Test Configuration ---
// Select the weight format to test compute by uncommenting one define
// #define TEST_WT_FP16
// #define TEST_WT_BF16
// #define TEST_WT_BBF16
// #define TEST_WT_FP8E4
// #define TEST_WT_FP8E5
// #define TEST_WT_INT16
// #define TEST_WT_INT8
#define TEST_WT_INT4

// Select the input format to test compute
#define TEST_IN_TYPE INT4

// --- Helper Function for Binary Printing (Optional, kept from original) ---
void print_binary_arr(uint16_t value, int bits)
{
    for (int i = bits - 1; i >= 0; i--)
    {
        printf("%c", (value >> i) & 1 ? '1' : '0');
    }
}

// --- Test Runner ---
int main()
{
    printf("--- Starting DcimEngine Class Test ---\n");
    int errors = 0;
    int test_count = 0;

    // --- Instantiate Engine ---
    // Uses DCIM_ENGINE_N_MACROS defined in dcim_engine.hpp (currently 2)
    printf("Instantiating DcimEngine with %d macros...\n", DCIM_ENGINE_N_MACROS);
    DcimEngine test_engine(DCIM_ENGINE_N_MACROS);
    if (test_engine.getNumMacros() != DCIM_ENGINE_N_MACROS)
    {
        fprintf(stderr,
                "FATAL: Engine constructed with incorrect number of macros (%zu vs %d)\n",
                test_engine.getNumMacros(),
                DCIM_ENGINE_N_MACROS);
        return 1;
    }

    // --- Test 1: Storage - writePage / readPage (Testing on macro 0) ---
    test_count++;
    printf("\n--- Test %d: DcimEngine::writePage / readPage (on macro 0) ---\n", test_count);
    {
        // DcimEngine test_engine(DCIM_ENGINE_N_MACROS); // Instance created above
        uint8_t write_buffer[DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW];
        uint8_t read_buffer[DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW];
        size_t test_macro_idx = 0;  // Test on the first macro
        int test_page_idx = 3;

        // Prepare test data (using FP16 aligned data for simplicity)
        printf("Preparing test page data (using FP16 aligned)...\n");
        bool prep_ok = convertAlignedColumnMajorToPageRowMajor(
            (const uint8_t*)wt_algn_fp16,  // Source: Aligned Column Major
            FP16,                          // Type used for packing structure
            (uint8_t*)write_buffer         // Destination: Page Row Major
        );

        if (!prep_ok)
        {
            fprintf(stderr, "  FAIL: Failed to prepare test page data.\n");
            errors++;
        }
        else
        {
            // Write the page to the specific macro
            printf("Writing to macro %zu, page %d...\n", test_macro_idx, test_page_idx);
            bool write_ok =
                test_engine.writePage(test_macro_idx, test_page_idx, &write_buffer[0][0]);
            if (!write_ok)
            {
                fprintf(stderr, "  FAIL: DcimEngine::writePage returned false.\n");
                errors++;
            }
            else
            {
                // Read the page back from the specific macro
                printf("Reading from macro %zu, page %d...\n", test_macro_idx, test_page_idx);
                memset(read_buffer,
                       0xAA,
                       sizeof(read_buffer));  // Fill read buffer to ensure it's overwritten
                bool read_ok =
                    test_engine.readPage(test_macro_idx, test_page_idx, &read_buffer[0][0]);
                if (!read_ok)
                {
                    fprintf(stderr, "  FAIL: DcimEngine::readPage returned false.\n");
                    errors++;
                }
                else
                {
                    // Compare
                    if (memcmp(write_buffer, read_buffer, sizeof(write_buffer)) == 0)
                    {
                        printf("  PASS: writePage/readPage data matches for macro %zu.\n",
                               test_macro_idx);
                    }
                    else
                    {
                        fprintf(stderr,
                                "  FAIL: Data mismatch after writePage/readPage for macro %zu.\n",
                                test_macro_idx);
                        errors++;
                    }
                }
            }
        }
    }

    // --- Test 2: Storage - writeRow / readRow (Testing on macro 0) ---
    test_count++;
    printf("\n--- Test %d: DcimEngine::writeRow / readRow (on macro 0) ---\n", test_count);
    {
        // DcimEngine test_engine(DCIM_ENGINE_N_MACROS); // Instance created above
        uint8_t write_page_buffer[DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW];
        uint8_t write_row_buffer[DCIM_NUM_BYTES_PER_ROW];
        uint8_t read_row_buffer[DCIM_NUM_BYTES_PER_ROW];
        size_t test_macro_idx = 0;  // Test on the first macro
        int test_page_idx = 5;
        int test_row_idx = 10;  // Test a specific row

        // Prepare a full page first (using BF16 data for variety)
        bool prep_ok = convertAlignedColumnMajorToPageRowMajor(
            (const uint8_t*)wt_algn_bf16, BF16, (uint8_t*)write_page_buffer);

        if (!prep_ok)
        {
            fprintf(stderr, "  FAIL: Failed to prepare test page data for row test.\n");
            errors++;
        }
        else
        {
            // Write the full page initially to the specific macro
            if (!test_engine.writePage(test_macro_idx,
                                       test_page_idx,
                                       reinterpret_cast<const uint8_t*>(write_page_buffer)))
            {
                fprintf(stderr,
                        "  FAIL: Initial writePage failed for row test on macro %zu.\n",
                        test_macro_idx);
                errors++;
            }
            else
            {
                // Prepare the specific row data (modify one value for distinction)
                memcpy(write_row_buffer, write_page_buffer[test_row_idx], sizeof(write_row_buffer));
                // Modify the first word (ensure endianness doesn't confuse if printing)
                ((uint16_t*)write_row_buffer)[0] = 0xABCD;

                // Write the specific row to the specific macro
                printf("Writing modified data to macro %zu, page %d, row %d...\n",
                       test_macro_idx,
                       test_page_idx,
                       test_row_idx);
                bool write_ok = test_engine.writeRow(
                    test_macro_idx, test_page_idx, test_row_idx, write_row_buffer);
                if (!write_ok)
                {
                    fprintf(stderr, "  FAIL: DcimEngine::writeRow returned false.\n");
                    errors++;
                }
                else
                {
                    // Read the specific row back from the specific macro
                    printf("Reading from macro %zu, page %d, row %d...\n",
                           test_macro_idx,
                           test_page_idx,
                           test_row_idx);
                    memset(read_row_buffer, 0xBB, sizeof(read_row_buffer));  // Fill read buffer
                    bool read_ok = test_engine.readRow(
                        test_macro_idx, test_page_idx, test_row_idx, read_row_buffer);
                    if (!read_ok)
                    {
                        fprintf(stderr, "  FAIL: DcimEngine::readRow returned false.\n");
                        errors++;
                    }
                    else
                    {
                        // Compare
                        if (memcmp(write_row_buffer, read_row_buffer, sizeof(write_row_buffer)) ==
                            0)
                        {
                            printf("  PASS: writeRow/readRow data matches for macro %zu.\n",
                                   test_macro_idx);
                        }
                        else
                        {
                            fprintf(stderr,
                                    "  FAIL: Data mismatch after writeRow/readRow for macro %zu.\n",
                                    test_macro_idx);
                            errors++;
                        }
                    }
                    // Optional: Read a different row to ensure it wasn't affected
                    int other_row_idx = test_row_idx + 1;
                    if (test_engine.readRow(
                            test_macro_idx, test_page_idx, other_row_idx, read_row_buffer))
                    {
                        if (memcmp(write_page_buffer[other_row_idx],
                                   read_row_buffer,
                                   sizeof(read_row_buffer)) != 0)
                        {
                            fprintf(
                                stderr,
                                "  FAIL: writeRow affected an unrelated row (%d) on macro %zu.\n",
                                other_row_idx,
                                test_macro_idx);
                            errors++;
                        }
                        else
                        {
                            printf("  PASS: Unrelated row (%d) was not affected by writeRow on "
                                   "macro %zu.\n",
                                   other_row_idx,
                                   test_macro_idx);
                        }
                    }
                }
            }
        }
    }

    // --- Test 3: Compute ---
    test_count++;
    printf("\n--- Test %d: DcimEngine::computeRawInput ---\n", test_count);
    {
        // DcimEngine test_engine(DCIM_ENGINE_N_MACROS); // Instance created above
        uint8_t in_tpy = TEST_IN_TYPE;
        uint8_t wt_tpy = 0;
        const uint16_t(*raw_input_table_ptr)[32] = nullptr;  // Source for the single input vector
        const uint16_t(*raw_weight_table_ptr)[32] =
            nullptr;  // Used for golden reference calculation if FP type
        const uint16_t(*aligned_weight_table_ptr)[33] =
            nullptr;  // Used for preparing page data AND golden ref if INT type
        int num_weight_cols = 0;
        uint8_t in_tbn[10] = {0, 1, 2, 3, 3, 4, 5, 6, 7, 8};  // Mapping for input tables

// --- Determine Test Types and Data Pointers (Same as DcimArray test) ---
#ifdef TEST_WT_FP16
        wt_tpy = FP16;
        raw_input_table_ptr = &in_table_tr_fp16[in_tbn[in_tpy]];
        raw_weight_table_ptr = &wt_mhex_fp16[0];
        aligned_weight_table_ptr = &wt_algn_fp16[0];
        num_weight_cols = 16;
#elif defined(TEST_WT_BF16)
        wt_tpy = BF16;
        raw_input_table_ptr = &in_table_tr_bf16[in_tbn[in_tpy]];
        raw_weight_table_ptr = &wt_mhex_bf16[0];
        aligned_weight_table_ptr = &wt_algn_bf16[0];
        num_weight_cols = 16;
#elif defined(TEST_WT_BBF16)
        wt_tpy = BBF16;
        raw_input_table_ptr = &in_table_tr_bbf16[in_tbn[in_tpy]];
        raw_weight_table_ptr = &wt_mhex_bbf16[0];
        aligned_weight_table_ptr = &wt_algn_bbf16[0];
        num_weight_cols = 32;
#elif defined(TEST_WT_FP8E4)
        wt_tpy = FP8E4;
        raw_input_table_ptr = &in_table_tr_fp8e4[in_tbn[in_tpy]];
        raw_weight_table_ptr = &wt_mhex_fp8e4[0];
        aligned_weight_table_ptr = &wt_algn_fp8e4[0];
        num_weight_cols = 32;
#elif defined(TEST_WT_FP8E5)
        wt_tpy = FP8E5;
        raw_input_table_ptr = &in_table_tr_fp8e5[in_tbn[in_tpy]];
        raw_weight_table_ptr = &wt_mhex_fp8e5[0];
        aligned_weight_table_ptr = &wt_algn_fp8e5[0];
        num_weight_cols = 32;
#elif defined(TEST_WT_INT16)
        wt_tpy = INT16;
        raw_input_table_ptr = &in_table_tr_int16[in_tbn[in_tpy]];
        raw_weight_table_ptr = nullptr;
        aligned_weight_table_ptr = &wt_algn_int16[0];
        num_weight_cols = 16;
#elif defined(TEST_WT_INT8)
        wt_tpy = INT8;
        raw_input_table_ptr = &in_table_tr_int8[in_tbn[in_tpy]];
        raw_weight_table_ptr = nullptr;
        aligned_weight_table_ptr = &wt_algn_int8[0];
        num_weight_cols = 32;
#elif defined(TEST_WT_INT4)
        wt_tpy = INT4;
        raw_input_table_ptr = &in_table_tr_int4[in_tbn[in_tpy]];
        raw_weight_table_ptr = nullptr;
        aligned_weight_table_ptr = &wt_algn_int4[0];
        num_weight_cols = 64;
#else
    #error "No weight type defined for compute testing!"
        return 1;
#endif
        printf("Testing Compute: Input=%d, Weight=%d\n", in_tpy, wt_tpy);

        // --- Prepare Input for Engine ---
        uint16_t single_raw_input_vec[32];  // The single input stimulus
        if (!raw_input_table_ptr)
        {
            errors++;
            fprintf(stderr, "  FAIL: Raw input table pointer is null.\n");
            goto compute_end;
        }
        memcpy(single_raw_input_vec, raw_input_table_ptr, 32 * sizeof(uint16_t));

        // Create the combined raw input block by replicating the single input
        const size_t raw_input_bytes_per_macro = 32 * sizeof(uint16_t);
        std::vector<uint8_t> engine_raw_input_block(DCIM_ENGINE_N_MACROS *
                                                    raw_input_bytes_per_macro);
        printf("Replicating single raw input vector %d times for engine...\n",
               DCIM_ENGINE_N_MACROS);
        for (size_t i = 0; i < DCIM_ENGINE_N_MACROS; ++i)
        {
            memcpy(engine_raw_input_block.data() + (i * raw_input_bytes_per_macro),
                   single_raw_input_vec,
                   raw_input_bytes_per_macro);
        }

        // --- Prepare Weight Page (Same for all macros) ---
        uint8_t weight_page_buffer[DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW];
        memset(weight_page_buffer, 0, sizeof(weight_page_buffer));
        bool page_prep_ok = false;
        const uint16_t* aligned_col_major_ptr = nullptr;

        if (!aligned_weight_table_ptr)
        {
            errors++;
            fprintf(stderr, "  FAIL: Aligned weight table pointer is null.\n");
            goto compute_end;
        }
        aligned_col_major_ptr = (const uint16_t*)aligned_weight_table_ptr;

        printf("Converting aligned weights to page format...\n");
        page_prep_ok = convertAlignedColumnMajorToPageRowMajor(
            (const uint8_t*)aligned_col_major_ptr, wt_tpy, (uint8_t*)weight_page_buffer);

        if (!page_prep_ok)
        {
            errors++;
            fprintf(stderr, "  FAIL: convertAlignedColumnMajorToPageRowMajor failed.\n");
            goto compute_end;
        }

        // --- Write Same Page to All Macros in Engine ---
        int compute_page_idx = 0;
        printf("Writing same test page %d to all %d macros...\n",
               compute_page_idx,
               DCIM_ENGINE_N_MACROS);
        bool all_writes_ok = true;
        for (size_t i = 0; i < DCIM_ENGINE_N_MACROS; ++i)
        {
            if (!test_engine.writePage(
                    i, compute_page_idx, reinterpret_cast<const uint8_t*>(weight_page_buffer)))
            {
                errors++;
                fprintf(stderr, "  FAIL: test_engine.writePage failed for macro %zu.\n", i);
                all_writes_ok = false;
            }
        }
        if (!all_writes_ok)
            goto compute_end;

        // --- Execute Engine Compute ---
        uint32_t actual_output_hex[64];
        printf("Calling test_engine.computeRawInput()...\n");
        bool compute_ok = test_engine.computeRawInput(engine_raw_input_block.data(),
                                                      in_tpy,
                                                      compute_page_idx,
                                                      wt_tpy,
                                                      (uint8_t*)actual_output_hex);

        if (!compute_ok)
        {
            errors++;
            fprintf(stderr, "  FAIL: test_engine.computeRawInput() returned false.\n");
            goto compute_end;
        }

        // --- Calculate Expected Results (Single Macro * N) ---
        printf("Calculating expected results (Single Macro * %d)...\n", DCIM_ENGINE_N_MACROS);
        std::vector<float> single_macro_expected_float(64, 0.0);
        // Calculate expected for one macro first
        for (int j = 0; j < num_weight_cols; ++j)
        {
            float expected_dot_product = 0.0;
            for (int i = 0; i < 32; ++i)
            {
                float in_val_d = 0.0;
                float wt_val_d = 0.0;
                // Convert raw input (using single_raw_input_vec)
                switch (in_tpy)
                {
                    case INT4:
                    {
                        ac_int4_t in_ac = single_raw_input_vec[i];
                        in_val_d = static_cast<float>(in_ac.to_int());
                        break;
                    }
                    case INT8:
                    {
                        ac_int8_t in_ac = single_raw_input_vec[i];
                        in_val_d = static_cast<float>(in_ac.to_int());
                        break;
                    }
                    case INT16:
                    {
                        ac_int16_t in_ac = single_raw_input_vec[i];
                        in_val_d = static_cast<float>(in_ac.to_int());
                        break;
                    }
                    case FP16:
                    {
                        ac_float16_t in_ac;
                        in_ac.set_data(single_raw_input_vec[i]);
                        in_val_d = in_ac.to_float();
                        break;
                    }
                    case BF16:
                    {
                        ac_bfloat16_t in_ac;
                        in_ac.set_data(single_raw_input_vec[i]);
                        in_val_d = in_ac.to_float();
                        break;
                    }
                    case BBF16:
                    {
                        ac_bbfloat16_t in_ac;
                        in_ac.set_data(single_raw_input_vec[i]);
                        in_val_d = in_ac.to_float();
                        break;
                    }
                    case FP8E4:
                    {
                        ac_fp8e4_t in_ac;
                        in_ac.set_data(single_raw_input_vec[i]);
                        in_val_d = in_ac.to_float();
                        break;
                    }
                    case FP8E5:
                    {
                        ac_fp8e5_t in_ac;
                        in_ac.set_data(single_raw_input_vec[i]);
                        in_val_d = in_ac.to_float();
                        break;
                    }
                    default:
                        break;
                }
                // Get weight value (raw for FP, aligned for INT)
                uint16_t wt_val_verify = 0;
                if (wt_tpy == FP16 || wt_tpy == BF16 || wt_tpy == BBF16 || wt_tpy == FP8E4 ||
                    wt_tpy == FP8E5)
                {
                    if (!raw_weight_table_ptr)
                    {
                        fprintf(stderr, "ERR: raw_weight_table_ptr null in golden calc\n");
                        continue;
                    }
                    wt_val_verify = raw_weight_table_ptr[j][i];
                }
                else
                {
                    if (!aligned_weight_table_ptr)
                    {
                        fprintf(stderr, "ERR: aligned_weight_table_ptr null in golden calc\n");
                        continue;
                    }
                    wt_val_verify = aligned_weight_table_ptr[j][i];
                }
                // Convert weight
                switch (wt_tpy)
                {
                    case INT4:
                    {
                        ac_int4_t wt_ac = wt_val_verify;
                        wt_val_d = static_cast<float>(wt_ac.to_int());
                        break;
                    }
                    case INT8:
                    {
                        ac_int8_t wt_ac = wt_val_verify;
                        wt_val_d = static_cast<float>(wt_ac.to_int());
                        break;
                    }
                    case INT16:
                    {
                        ac_int16_t wt_ac = wt_val_verify;
                        wt_val_d = static_cast<float>(wt_ac.to_int());
                        break;
                    }
                    case FP16:
                    {
                        ac_float16_t wt_ac;
                        wt_ac.set_data(wt_val_verify);
                        wt_val_d = wt_ac.to_float();
                        break;
                    }
                    case BF16:
                    {
                        ac_bfloat16_t wt_ac;
                        wt_ac.set_data(wt_val_verify);
                        wt_val_d = wt_ac.to_float();
                        break;
                    }
                    case BBF16:
                    {
                        ac_bbfloat16_t wt_ac;
                        wt_ac.set_data(wt_val_verify);
                        wt_val_d = wt_ac.to_float();
                        break;
                    }
                    case FP8E4:
                    {
                        ac_fp8e4_t wt_ac;
                        wt_ac.set_data(wt_val_verify);
                        wt_val_d = wt_ac.to_float();
                        break;
                    }
                    case FP8E5:
                    {
                        ac_fp8e5_t wt_ac;
                        wt_ac.set_data(wt_val_verify);
                        wt_val_d = wt_ac.to_float();
                        break;
                    }
                    default:
                        break;
                }
                expected_dot_product += in_val_d * wt_val_d;
            }
            single_macro_expected_float[j] = expected_dot_product;
        }

        // Scale by number of macros for final engine expectation
        std::vector<float> engine_expected_output_float(64);
        for (int j = 0; j < 64; ++j)
        {
            engine_expected_output_float[j] = single_macro_expected_float[j] * DCIM_ENGINE_N_MACROS;
        }

        // --- Verify Compute Results ---
        printf("Verifying compute results...\n");
        const float tolerance =
            1e-4f;  // Adjust tolerance if needed, especially for lower precision types
        int compute_errors = 0;
        for (int j = 0; j < num_weight_cols; ++j)
        {
            float actual_float = 0.0f;
            // mh2f_conv(actual_output_hex[j], FP32, &actual_float); // Convert FP32 hex result back
            // to float
            memcpy(&actual_float, actual_output_hex + j, sizeof(uint32_t));
            float error = std::fabs(engine_expected_output_float[j] - actual_float);
            // Increase tolerance slightly relative to expected magnitude
            float relative_tolerance =
                tolerance * std::max(1.0f, std::fabs(engine_expected_output_float[j]));
            if (error > relative_tolerance)
            {
                fprintf(stderr,
                        "  FAIL: Mismatch at index %d: Expected %.8f, Got %.8f (Hex: 0x%08X), "
                        "AbsError: %.8f > Tol: %.8f\n",
                        j,
                        engine_expected_output_float[j],
                        actual_float,
                        actual_output_hex[j],
                        error,
                        relative_tolerance);
                compute_errors++;
            }
        }
        // Check padding
        for (int j = num_weight_cols; j < 64; ++j)
        {
            // Expected padding should also be N * 0 = 0
            if (actual_output_hex[j] != 0)
            {
                fprintf(stderr,
                        "  FAIL: Padding Error at index %d: Expected 0x00000000, Got 0x%08X\n",
                        j,
                        actual_output_hex[j]);
                compute_errors++;
            }
        }

        if (compute_errors == 0)
        {
            printf("  PASS: Compute results verified successfully within tolerance (%.1e).\n",
                   tolerance);
        }
        else
        {
            errors += compute_errors;
        }
    }
compute_end:;  // Label for goto

    // --- Final Summary ---
    printf("\n--- DcimEngine Test Summary ---\n");
    if (errors == 0)
    {
        printf("PASS: All %d tests passed!\n", test_count);
        return 0;  // Success
    }
    else
    {
        printf("FAIL: %d errors found in %d tests.\n", errors, test_count);
        return 1;  // Failure
    }
}