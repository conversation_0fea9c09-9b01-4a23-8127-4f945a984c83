# DCIM 架构文档
## 1. 说明
### 1.1 设计目标

- 提供高效的向量-矩阵乘法计算能力
- 支持多种数据格式（INT4/8/12/16, FP8E4/E5, FP16, BF16, BBF16）
- 灵活的页式存储结构，便于管理和访问权重数据
- 提供C和C++接口，便于集成到更高层次的系统中

### 1.2 核心特性

- 统一的数据对齐和计算模型
- 高效的向量-矩阵乘法实现
- 灵活的页式存储管理
- 支持多种数据类型和精度
- 支持数据转换和对齐操作

## 2. 系统架构

DCIM系统架构层级如下图所示：

```mermaid
graph TD
    A[应用层] --> B[DCIM C++接口 - DcimArray类]
    A --> C[DCIM C接口]
    
    subgraph "DCIM核心模块"
        B --> D[计算子系统]
        B --> E[存储子系统]
        B --> F[数据操作子系统]
        C --> D
        C --> E
        C --> F
        D --> G[核心计算 - dcim_macro_com]
        E --> H[页式存储管理]
        F --> I[数据转换] & J[数据提取]
    end
    
    K[公共定义 - 数据类型/数据对齐] --> D & E & F
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style D fill:#dfd,stroke:#333,stroke-width:1px
    style E fill:#dfd,stroke:#333,stroke-width:1px
    style F fill:#dfd,stroke:#333,stroke-width:1px
    style K fill:#ffd,stroke:#333,stroke-width:1px
```

DCIM系统主要由以下几个核心组件组成：

### 2.1 存储子系统 (Storage Subsystem)

负责管理DCIM的数据存储，采用页式结构：
- 总共16页内存 (`DCIM_NUM_PAGES`)
- 每页33行 (`DCIM_NUM_ROWS_PER_PAGE`)，32行数据 + 1行指数
- 每行32字节 (`DCIM_NUM_BYTES_PER_ROW`)，共256位/行

主要文件：`dcim_storage.h/c`

### 2.2 计算子系统 (Compute Subsystem)

实现了向量-矩阵乘法的核心逻辑：
- 支持对齐向量与存储矩阵的乘法计算
- 根据数据类型动态调整计算策略
- 支持指数对齐和缩放

主要文件：`dcim_compute.h/c`, `dcim_macro.c`

### 2.3 数据操作子系统 (Data Manipulation Subsystem)

提供数据格式转换、提取和对齐的功能：
- 从矩阵中提取对齐的权重列
- 将原始数据转换为对齐格式
- 在不同存储布局间转换数据

主要文件：`dcim_extract.h/c`, `dcim_matrix_utils.h/c`

### 2.4 C++封装 (C++ Wrapper)

提供面向对象的接口，封装底层C实现：
- 存储管理（页/行的读写）
- 计算功能
- 错误处理和边界检查

主要文件：`dcim_array.hpp/cpp`

### 2.5 公共定义 (Common Definitions)

包含所有模块共享的常量、数据类型和通用功能：
- 数据格式定义
- 转换函数
- 对齐函数

主要文件：`dcim_com.h`

## 3. 数据流

DCIM系统的典型数据流如下图所示：

```mermaid
flowchart TD
    subgraph "数据准备阶段"
        A[原始权重数据<br>列优先] --> |convertRawToAlignedColumnMajor| B[对齐权重数据<br>列优先]
        B --> |convertAlignedColumnMajorToPageRowMajor| C[对齐权重数据<br>页行优先]
    end
    
    subgraph "数据存储阶段"
        C --> |write_storage_page<br>DcimArray::writePage| D[DCIM内部存储]
    end
    
    subgraph "输入处理阶段"
        E[原始输入向量] --> |float_data_align<br>DcimArray::alignInputVector| F[对齐输入向量]
    end
    
    subgraph "计算阶段"
        F --> G[计算函数]
        D --> G
        G --> |extract_aligned_weight_column| H[提取权重列]
        H --> |dcim_macro_com| I[点积计算]
        F --> I
        I --> J[计算结果<br>FP32格式]
    end
    
    subgraph "结果处理阶段"
        J --> K[应用处理]
    end
    
    style A fill:#ffcccc,stroke:#333
    style B fill:#ccffcc,stroke:#333
    style C fill:#ccffcc,stroke:#333
    style D fill:#ccccff,stroke:#333
    style E fill:#ffcccc,stroke:#333
    style F fill:#ccffcc,stroke:#333
    style G fill:#ffffcc,stroke:#333
    style H fill:#ffffcc,stroke:#333
    style I fill:#ffffcc,stroke:#333
    style J fill:#ffddaa,stroke:#333
    style K fill:#ddaaff,stroke:#333
```

DCIM系统的典型数据流如下：

1. **数据准备阶段**：
   - 原始权重数据通过`convertRawToAlignedColumnMajor`转换为对齐的列优先格式
   - 对齐的列优先数据通过`convertAlignedColumnMajorToPageRowMajor`转换为页行优先格式

2. **数据存储阶段**：
   - 使用`write_storage_page`或`DcimArray::writePage`将页数据写入存储系统

3. **输入处理阶段**：
   - 原始输入向量通过`float_data_align`或`DcimArray::alignInputVector`转换为对齐格式

4. **计算阶段**：
   - 使用`dcim_compute_from_storage_aligned_input`或`DcimArray::compute`执行向量-矩阵乘法
   - 内部通过`extract_aligned_weight_column`从存储中提取权重列
   - 使用`dcim_macro_com`执行点积计算

5. **结果处理阶段**：
   - 计算结果以FP32格式返回

## 4. 数据格式支持

DCIM系统支持多种数据格式，定义在`data_fmt`枚举中：

- `INT4`: 4位整数
- `INT8`: 8位整数
- `INT12`: 12位整数
- `INT16`: 16位整数
- `FP32`: 32位浮点数
- `FP16`: 16位浮点数
- `BF16`: 16位脑浮点数
- `BBF16`: 8位脑浮点数
- `FP8E4`: 8位浮点数（4位指数）
- `FP8E5`: 8位浮点数（5位指数）

不同数据类型在存储和计算过程中有不同的特性：
- 整数类型（INT4/8/12/16）不需要对齐操作，但需要处理符号扩展
- 浮点类型需要对齐操作，将共享指数存储在第33行
- 不同类型在一页中能存储的列数不同：INT4(64列), INT8/FP8(32列), INT16/FP16/BF16(16列)

## 5. 核心函数文档

### 5.1 存储管理函数

#### 5.1.1 C接口

```c
/* 从DCIM存储中读取特定页面的特定行
 * @param page_idx 页索引 (0 到 DCIM_NUM_PAGES - 1)
 * @param row_idx 行索引 (0 到 DCIM_NUM_ROWS_PER_PAGE - 1)
 * @param buffer 存储读取数据的缓冲区 (大小为 DCIM_NUM_BYTES_PER_ROW)
 */
void read_storage_row(int page_idx, int row_idx, uint8_t *buffer);

/* 向DCIM存储中的特定页面的特定行写入数据
 * @param page_idx 页索引 (0 到 DCIM_NUM_PAGES - 1)
 * @param row_idx 行索引 (0 到 DCIM_NUM_ROWS_PER_PAGE - 1)
 * @param buffer 包含要写入数据的缓冲区 (大小为 DCIM_NUM_BYTES_PER_ROW)
 */
void write_storage_row(int page_idx, int row_idx, const uint8_t *buffer);

/* 从DCIM存储中读取整个页面
 * @param page_idx 页索引 (0 到 DCIM_NUM_PAGES - 1)
 * @param page_buffer 存储读取页面数据的缓冲区 (大小为 [DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW])
 */
void read_storage_page(int page_idx, uint8_t *page_buffer);

/* 向DCIM存储中的整个页面写入数据
 * @param page_idx 页索引 (0 到 DCIM_NUM_PAGES - 1)
 * @param page_buffer 包含要写入页面数据的缓冲区 (大小为 [DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW])
 */
void write_storage_page(int page_idx, const uint8_t *page_buffer);
```

#### 5.1.2 C++接口

```cpp
/* 向DCIM内部存储的整个页面写入预对齐数据
 * @param page_idx 页索引 (0 到 DCIM_NUM_PAGES - 1)
 * @param page_buffer 包含预对齐页面数据的缓冲区 (大小为 [DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW])
 * @return 如果写入成功返回true，否则返回false (例如，索引无效)
 */
bool DcimArray::writePage(int page_idx, const uint8_t *page_buffer);

/* 从DCIM内部存储中读取整个页面
 * @param page_idx 页索引 (0 到 DCIM_NUM_PAGES - 1)
 * @param page_buffer 存储读取页面数据的缓冲区 (大小为 [DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW])
 * @return 如果读取成功返回true，否则返回false (例如，索引无效)
 */
bool DcimArray::readPage(int page_idx, uint8_t *page_buffer);

/* 向DCIM内部存储的特定页面的特定行写入预对齐数据
 * @param page_idx 页索引 (0 到 DCIM_NUM_PAGES - 1)
 * @param row_idx 行索引 (0 到 DCIM_NUM_ROWS_PER_PAGE - 1)
 * @param row_buffer 包含预对齐行数据的缓冲区 (大小为 DCIM_NUM_BYTES_PER_ROW)
 * @return 如果写入成功返回true，否则返回false (例如，索引无效)
 */
bool DcimArray::writeRow(int page_idx, int row_idx, const uint8_t *row_buffer);

/* 从DCIM内部存储中读取特定页面的特定行
 * @param page_idx 页索引 (0 到 DCIM_NUM_PAGES - 1)
 * @param row_idx 行索引 (0 到 DCIM_NUM_ROWS_PER_PAGE - 1)
 * @param row_buffer 存储读取行数据的缓冲区 (大小为 DCIM_NUM_BYTES_PER_ROW)
 * @return 如果读取成功返回true，否则返回false (例如，索引无效)
 */
bool DcimArray::readRow(int page_idx, int row_idx, uint8_t *row_buffer);
```

### 5.2 计算函数

#### 5.2.1 C接口

```c
/* 使用DCIM存储中特定页面的数据执行向量-矩阵乘法，假设输入已对齐
 * @param aligned_input_vector 预对齐的输入向量 (大小 33*sizeof(uint16_t))
 * @param in_type 输入向量的数据类型 (使用 data_fmt 枚举)
 * @param page_idx 存储中包含对齐权重矩阵的页面索引 (0 到 DCIM_NUM_PAGES - 1)
 * @param wt_type 页面上存储的权重的数据类型 (使用 data_fmt 枚举)
 * @param result_vector 存储64个结果的输出缓冲区 (大小 64*sizeof(uint32_t))
 */
void dcim_compute_from_storage_aligned_input(
    const uint8_t *aligned_input_vector,
    uint8_t in_type,
    int page_idx,
    uint8_t wt_type,
    uint8_t *result_vector
);

/* 使用原始输入向量执行向量-矩阵乘法，内部会进行对齐
 * @param raw_input_vector 原始输入向量 (大小 32*sizeof(uint16_t))
 * @param in_type 输入向量的数据类型 (使用 data_fmt 枚举)
 * @param page_idx 存储中包含对齐权重矩阵的页面索引
 * @param wt_type 页面上存储的权重的数据类型
 * @param result_vector 存储64个结果的输出缓冲区 (大小 64*sizeof(uint32_t))
 */
void dcim_compute_from_storage_raw_input(
    const uint8_t *raw_input_vector,
    uint8_t in_type,
    int page_idx,
    uint8_t wt_type,
    uint8_t *result_vector
);
```

#### 5.2.2 C++接口

```cpp
/* 使用内部存储中特定页面的数据执行向量-矩阵乘法
 * @param aligned_input_vector 预对齐的输入向量 (大小 33*sizeof(uint16_t))
 * @param in_type 输入向量的数据类型
 * @param page_idx 内部存储中包含对齐权重矩阵的页面索引
 * @param wt_type 页面上存储的权重的数据类型
 * @param result_vector 存储64个结果的输出缓冲区 (大小 64*sizeof(uint32_t))
 * @return 如果计算成功返回true，否则返回false
 */
bool DcimArray::compute(
    const uint8_t *aligned_input_vector,
    uint8_t in_type,
    int page_idx,
    uint8_t wt_type,
    uint8_t *result_vector
);

/* 对原始输入向量进行对齐
 * @param raw_input_vector 原始输入向量 (大小 32*sizeof(uint16_t))
 * @param in_type 输入的数据类型
 * @param aligned_output 存储对齐向量的输出缓冲区 (大小 33*sizeof(uint16_t))
 */
void DcimArray::alignInputVector(
    const uint8_t *raw_input_vector,
    uint8_t in_type,
    uint8_t *aligned_output
);
```

### 5.3 数据转换/提取函数

```c
/* 将原始权重矩阵（列优先）转换为对齐的权重矩阵（列优先）
 * @param raw_matrix 指向原始权重矩阵数据的指针（列优先）
 *                  预期布局：uint16_t[num_columns][32]
 * @param wt_type 权重的数据类型（决定对齐和num_columns）
 * @param aligned_matrix_col_major 对齐权重矩阵的输出缓冲区（列优先）
 *                                预期布局：uint16_t[num_columns][33]
 * @return 如果转换成功返回true，否则返回false（例如，空指针，无效类型）
 */
bool convertRawToAlignedColumnMajor(
    const uint8_t *raw_matrix,
    uint8_t wt_type,
    uint8_t *aligned_matrix_col_major
);

/* 将对齐的权重矩阵（列优先）转换为页存储格式（行优先）
 * @param aligned_matrix_col_major 指向对齐权重矩阵数据的指针（列优先）
 *                                预期布局：uint16_t[num_columns][33]
 * @param wt_type 权重的数据类型（决定打包和num_columns）
 * @param page_buffer 行优先页存储格式的输出缓冲区
 *                   预期布局：uint8_t[DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW]
 * @return 如果转换成功返回true，否则返回false（例如，空指针，无效类型）
 */
bool convertAlignedColumnMajorToPageRowMajor(
    const uint8_t *aligned_matrix_col_major,
    uint8_t wt_type,
    uint8_t *page_buffer
);

/* 从物理存储行（包含预对齐数据）中提取单个逻辑权重列的对齐数据
 * @param aligned_weight_page_rows 指向包含对齐权重数据的页缓冲区前32行的指针
 *                               预期维度：[32][DCIM_NUM_BYTES_PER_ROW]
 * @param wt_type 权重的数据类型（决定每个元素的位数和打包）
 * @param column_idx 要提取的逻辑列索引（0到63）
 * @param extracted_aligned_col 存储33个提取的对齐权重元素的输出缓冲区
 *                            （32个数据 + 1个指数）
 */
void extract_aligned_weight_column(
    const uint8_t (*aligned_weight_page_rows)[DCIM_NUM_BYTES_PER_ROW],
    uint8_t wt_type,
    int column_idx,
    uint16_t *extracted_aligned_col
);

/* 根据权重数据类型确定逻辑列数
 * @param wt_type 权重的数据类型 (使用 data_fmt 枚举)。
 * @return 逻辑列数 (例如，INT4 为 64，INT8 为 32，INT16 为 16)。
 *         不支持的类型返回 0。
 */
int get_num_logical_columns(uint8_t wt_type);
```

### 5.4 核心计算函数

```c
/* 执行向量-矩阵乘法的核心操作
 * 此函数接受对齐的权重列和对齐的输入向量，执行乘法累加操作，
 * 并返回FP32格式的结果。
 * 
 * 函数处理特殊情况：
 * 1. 对INT4类型输入和权重进行符号扩展处理
 * 2. 根据数据类型执行不同的乘法累加操作
 * 3. 处理指数调整和结果规范化
 * 
 * @param wt_algn 对齐权重列，包括共享指数 (大小 33)
 * @param wt_dtpy 权重数据类型
 * @param in_algn 对齐输入向量，包括共享指数 (大小 33)
 * @param in_dtpy 输入数据类型
 * @return 以FP32格式表示的结果
 */
uint32_t dcim_macro_com(
    uint16_t* wt_algn,
    uint8_t wt_dtpy,
    uint16_t* in_algn,
    uint8_t in_dtpy
);
```

## 6. 实现细节

### 6.1 数据对齐机制

DCIM系统中，对齐是指将数据转换为内部计算格式的过程，主要有：

1. **浮点数对齐**：提取共享指数，将32个浮点数转换为32个尾数和1个共享指数
   - 实现函数：`float_data_align`、`data_align_f16b`、`data_align_bbf16b`、`data_align_f8b`

2. **整数类型处理**：对于整数类型，对齐主要处理符号扩展，第33个元素(指数)设为0
   - 特别地，对于INT4类型，在`dcim_macro_com`中有特殊处理逻辑

### 6.2 存储布局

DCIM存储采用页行优先(Page-Row-Major)格式，这种布局有助于提高访问效率：

- 每页33行，每行32字节，总共256位/行
- 第1-32行存储数据，第33行存储共享指数（对于浮点类型）
- 根据数据类型，一页中能存储的逻辑列数不同：
  - INT4: 64列（每字节2个元素）
  - INT8/FP8E4/FP8E5/BBF16: 32列（每字节1个元素）
  - INT12/INT16/FP16/BF16: 16列（每2字节1个元素）

### 6.3 向量-矩阵乘法实现

核心乘法实现在`dcim_macro_com`函数中：

1. 处理INT4类型的特殊情况（符号扩展）
2. 根据数据类型执行不同的乘法累加操作
3. 处理指数调整和结果规范化
4. 构建并返回FP32格式的结果

### 6.4 数据类型适配

系统设计了灵活的类型适配机制：

- 通过`get_num_logical_columns`和内部函数`get_bits_per_element`动态决定数据处理方式
- 通过`requires_alignment`判断是否需要浮点对齐
- 为不同位宽的数据类型实现了专门的打包/解包逻辑

### 6.5 错误处理

系统实现了一致的错误处理策略：

- C函数通过stderr报告错误，但通常不中断执行
- C++接口通过返回布尔值指示操作成功/失败
- 所有函数都包含对null指针和越界索引的检查
- 在发生错误时，输出缓冲区通常会被清零，以避免使用未初始化的数据

## 7. 存储格式

DCIM存储采用页行优先(Page-Row-Major)格式，这种布局有助于提高访问效率：

- 每页33行，每行32字节，总共256位/行
- 第1-32行存储数据，第33行存储共享指数（对于浮点类型）
- 根据数据类型，一页中能存储的逻辑列数不同：
  - INT4: 64列（每字节2个元素）
  - INT8/FP8E4/FP8E5/BBF16: 32列（每字节1个元素）
  - INT12/INT16/FP16/BF16: 16列（每2字节1个元素）

### 7.1 数据布局示例

对于INT4类型（64列/页）：
```
Page Layout:
Row 0:  [Col0-1][Col2-3]...[Col62-63] (32字节，每字节2个INT4值)
Row 1:  [Col0-1][Col2-3]...[Col62-63]
...
Row 31: [Col0-1][Col2-3]...[Col62-63]
Row 32: [共享指数] (对于INT类型为0)
```

对于INT8/FP8类型（32列/页）：
```
Page Layout:
Row 0:  [Col0][Col1]...[Col31] (32字节，每字节1个INT8/FP8值)
Row 1:  [Col0][Col1]...[Col31]
...
Row 31: [Col0][Col1]...[Col31]
Row 32: [共享指数] (对于INT8为0，对于FP8包含指数值)
```

对于INT16/FP16类型（16列/页）：
```
Page Layout:
Row 0:  [Col0_L][Col0_H][Col1_L][Col1_H]...[Col15_L][Col15_H] (32字节，每2字节1个值)
Row 1:  [Col0_L][Col0_H][Col1_L][Col1_H]...[Col15_L][Col15_H]
...
Row 31: [Col0_L][Col0_H][Col1_L][Col1_H]...[Col15_L][Col15_H]
Row 32: [共享指数] (对于INT16为0，对于FP16包含指数值)
```

### 7.2 数据对齐

1. **整数类型（INT4/8/12/16）**：
   - 不需要对齐操作
   - 第33行（共享指数）设置为0
   - 直接按位打包存储

2. **浮点类型（FP8/FP16/BF16/BBF16）**：
   - 需要对齐操作以提取共享指数
   - 第33行存储共享指数
   - 数据部分存储归一化的尾数



## 8. 错误处理

所有接口都包含错误检查：

1. **参数验证**：
   - 检查空指针
   - 验证页面和行索引范围
   - 验证数据类型的有效性

2. **错误报告**：
   - C接口通过stderr报告错误
   - C++接口通过返回值指示操作状态

3. **内存安全**：
   - 所有缓冲区访问都有边界检查
   - 写操作前验证目标空间的有效性



