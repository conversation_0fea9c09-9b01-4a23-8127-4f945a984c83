
# DCIM Cluster (CIMC) 架构文档

## 1. 说明

### 1.1 设计目标

`DcimCluster` (CIMC) 类在多个 `DcimEngine` 实例之上提供了一个更高级别的抽象。其目标是协调四个 `DcimEngine`（每个包含 `DCIM_ENGINE_N_MACROS` = 2 个 `DcimArray` 宏单元）以执行大规模向量-矩阵计算，遵循特定的操作模式，从而简化应用层的集成。

### 1.2 核心特性

-   **多引擎管理 (Multi-Engine Management)**: 管理一个包含 4 个 `DcimEngine` 实例的固定集群。
-   **可配置操作模式 (Configurable Operational Modes)**: 支持不同的输入分发和输出聚合模式：
    -   `PATTERN1_OUTPUT_FUSION` (模式1 - 输出融合): 所有引擎计算，结果累加。
    -   `PATTERN2_INPUT_DIST_OUTPUT_FUSION` (模式2 - 输入分发与输出融合): 成对输入分发，成对输出累加。
    -   `PATTERN3_INPUT_DIST` (模式3 - 输入分发): 广播输入，拼接输出。
-   **统一接口 (Unified Interface)**: 提供一致的存储访问和计算方法，隐藏多引擎协调的复杂性。
-   **标准化输入 (Standardized Input)**: 接受一个足以容纳所有引擎数据的固定大小输入块，简化调用者逻辑。内部处理根据所选模式进行调整。
-   **利用底层功能 (Leverages Lower Layers)**: 基于 `DcimEngine` 和 `DcimArray` 已验证的功能构建。

## 2. 系统架构

`DcimCluster` 位于 `DcimEngine` 层之上，负责协调多个引擎。

```mermaid
graph TD
    A[应用层] --> Cluster[DCIM C++接口 - DcimCluster 类];

    subgraph DcimCluster 内部
        Cluster -- 管理 --> E0["engines[0]: DcimEngine"];
        Cluster -- 管理 --> E1["engines[1]: DcimEngine"];
        Cluster -- 管理 --> E2["engines[2]: DcimEngine"];
        Cluster -- 管理 --> E3["engines[3]: DcimEngine"];
        Cluster -- 包含 --> Config["内部状态 (例如 current_mode)"];
        Cluster -- 包含 --> ComputeLogic["Cluster 计算协调逻辑"];
        Cluster -- 包含 --> StorageAccess["Cluster 存储访问接口"];
    end

    StorageAccess --> E0;
    StorageAccess --> E1;
    StorageAccess --> E2;
    StorageAccess --> E3;

    ComputeLogic -- 选择输入 & 调用 --> E0;
    ComputeLogic -- 选择输入 & 调用 --> E1;
    ComputeLogic -- 选择输入 & 调用 --> E2;
    ComputeLogic -- 选择输入 & 调用 --> E3;
    ComputeLogic -- 聚合/拼接 --> OutputBuffer[输出缓冲区];


    E0 --> EngCore["DcimEngine Core"];


    EngCore --> M0["macros[0]: DcimArray"];
    EngCore --> M1["macros[1]: DcimArray"];

    style Cluster fill:#ccf,stroke:#333,stroke-width:2px
    style E0 fill:#aae,stroke:#333,stroke-width:1px
    style E1 fill:#aae,stroke:#333,stroke-width:1px
    style E2 fill:#aae,stroke:#333,stroke-width:1px
    style E3 fill:#aae,stroke:#333,stroke-width:1px
```

-   **应用层 (Application Layer)**: 与 `DcimCluster` 交互，进行配置、数据加载和计算请求。
-   **DcimCluster**:
    -   持有 4 个 `DcimEngine` 实例。
    -   存储当前的 `OperationMode` (操作模式)。
    -   提供存储访问方法 (`writePage`, `readPage` 等)，这些方法将请求路由到指定的引擎和宏单元。
    -   提供计算方法 (`computeAlignedInput`, `computeRawInput`)，它们：
        -   接受一个完整的输入块（大小足以容纳 4 个引擎的数据）。
        -   根据当前模式内部确定使用输入块的哪些部分。
        -   将计算任务分派给相关的 `DcimEngine` 实例。
        -   根据模式聚合或拼接结果到输出缓冲区。
-   **DcimEngine**: 每个实例管理其 `DCIM_ENGINE_N_MACROS` (2) 个 `DcimArray`，并执行 `dcim_engine.md` 中描述的组合计算。

## 3. 操作模式与数据流

集群通过 `setOperationMode` 设置三种操作模式之一。`compute` 函数总是期望一个完整的输入缓冲区，但会根据活动模式以不同方式处理它。

**常量:**

-   `CIME_NUM = 4` (集群中的引擎数量)
-   `MACROS_PER_ENGINE = 2` (`DCIM_ENGINE_N_MACROS`)
-   `RAW_ELEMENTS_PER_MACRO = 32` (每个宏的原始输入元素数)
-   `ALIGNED_ELEMENTS_PER_MACRO = 33` (每个宏的对齐输入元素数)
-   `RESULTS_PER_ENGINE = 64` (每个引擎的结果数)

**输入缓冲区大小:**

-   `computeRawInput` 期望 `CIME_NUM * MACROS_PER_ENGINE * RAW_ELEMENTS_PER_MACRO` = `4 * 2 * 32 = 256` 个 `uint16_t` 元素。
-   `computeAlignedInput` 期望 `CIME_NUM * MACROS_PER_ENGINE * ALIGNED_ELEMENTS_PER_MACRO` = `4 * 2 * 33 = 264` 个 `uint16_t` 元素。

**输出缓冲区大小:**

-   传递给 `compute` 的输出缓冲区必须足够大以容纳最大可能的输出 (模式3): `CIME_NUM * RESULTS_PER_ENGINE` = `4 * 64 = 256` 个 `uint32_t` 元素。

### 3.1 模式 1: 输出融合 (Output Fusion)

-   **使用输入**: 输入块中对应于引擎 0, 1, 2, 3 的数据。
-   **计算**: 所有 4 个引擎使用各自的输入执行计算。
-   **输出**: 所有 4 个引擎的结果 (每个 64\*`uint32_t`) 被累加 (使用浮点转换进行元素求和) 到输出缓冲区的前 64\*`uint32_t` 部分。
-   **输出大小**: 64 \* `uint32_t`.

```mermaid
graph TD
    subgraph DcimCluster_Pattern1["DcimCluster (模式: PATTERN1)"]
        Input["输入块 "]
        Input --> SelectInput["选择 E0, E1, E2, E3 的输入"]

        SelectInput -- 输入 E0 --> E0[引擎 0 计算]
        SelectInput -- 输入 E1 --> E1[引擎 1 计算]
        SelectInput -- 输入 E2 --> E2[引擎 2 计算]
        SelectInput -- 输入 E3 --> E3[引擎 3 计算]

        E0 --> R0["结果 0 (64*uint32_t)"]
        E1 --> R1["结果 1 (64*uint32_t)"]
        E2 --> R2["结果 2 (64*uint32_t)"]
        E3 --> R3["结果 3 (64*uint32_t)"]

        R0 --> Accumulate["累加所有结果 (0+1+2+3)"]
        R1 --> Accumulate
        R2 --> Accumulate
        R3 --> Accumulate
        Accumulate --> Output["输出缓冲区 (写入前 64*uint32_t)"]
    end
    style Input fill:#ccffcc
    style Output fill:#ffddaa
```

### 3.2 模式 2: 输入分发与输出融合 (Input Distribution & Output Fusion)

-   **使用输入**: 输入块中对应于引擎 0 (用于 E0, E1) 和引擎 2 (用于 E2, E3) 的数据。块中引擎 1 和引擎 3 的数据被忽略。
-   **计算**: 引擎 0 和 1 使用输入 E0 进行计算。引擎 2 和 3 使用输入 E2 进行计算。
-   **输出**: E0 和 E1 的结果累加到输出缓冲区的前 64\*`uint32_t` 部分。E2 和 E3 的结果累加到输出缓冲区的接下来 64\*`uint32_t` 部分。
-   **输出大小**: 128 \* `uint32_t`.

```mermaid
graph TD
    subgraph DcimCluster_Pattern2["DcimCluster (模式: PATTERN2)"]
        Input["输入块 "]
        Input --> SelectInput["选择 E0, E2 的输入"]

        SelectInput -- 输入 E0 --> E0[引擎 0 计算]
        SelectInput -- 输入 E0 --> E1[引擎 1 计算]
        SelectInput -- 输入 E2 --> E2[引擎 2 计算]
        SelectInput -- 输入 E2 --> E3[引擎 3 计算]

        E0 --> R0["结果 0"]
        E1 --> R1["结果 1"]
        E2 --> R2["结果 2"]
        E3 --> R3["结果 3"]

        R0 --> Accumulate01["累加 0+1"]
        R1 --> Accumulate01
        R2 --> Accumulate23["累加 2+3"]
        R3 --> Accumulate23

        Accumulate01 --> Output1["输出缓冲区 (写入前 64*uint32_t)"]
        Accumulate23 --> Output2["输出缓冲区 (写入后 64*uint32_t)"]

    end
    style Input fill:#ccffcc
    style Output1 fill:#ffddaa
    style Output2 fill:#ffddaa
```

### 3.3 模式 3: 输入分发 (Input Distribution)

-   **使用输入**: 输入块中对应于引擎 0 的数据。块中引擎 1, 2, 3 的数据被忽略。
-   **计算**: 所有 4 个引擎使用广播的输入 E0 执行计算。
-   **输出**: E0, E1, E2, E3 的结果直接拼接到输出缓冲区中。
-   **输出大小**: 256 \* `uint32_t`.

```mermaid
graph TD
    subgraph DcimCluster_Pattern3["DcimCluster (模式: PATTERN3)"]
        Input["输入块 "]
        Input --> SelectInput["选择 E0 的输入"]

        SelectInput -- 输入 E0 --> E0[引擎 0 计算]
        SelectInput -- 输入 E0 --> E1[引擎 1 计算]
        SelectInput -- 输入 E0 --> E2[引擎 2 计算]
        SelectInput -- 输入 E0 --> E3[引擎 3 计算]

        E0 --> R0["结果 0 (64*uint32_t)"]
        E1 --> R1["结果 1 (64*uint32_t)"]
        E2 --> R2["结果 2 (64*uint32_t)"]
        E3 --> R3["结果 3 (64*uint32_t)"]

        R0 --> Concatenate["拼接结果 [R0, R1, R2, R3]"]
        R1 --> Concatenate
        R2 --> Concatenate
        R3 --> Concatenate
        Concatenate --> Output["输出缓冲区 (写入 256*uint32_t)"]
    end
    style Input fill:#ccffcc
    style Output fill:#ffddaa
```

## 4. 类接口 (概念)

```cpp
#include <vector>
#include <cstdint>
#include "dcim_engine.hpp" // 假设 DcimEngine 在此定义

class DcimCluster {
public:
    // 定义操作模式的枚举
    enum class OperationMode {
        PATTERN1_OUTPUT_FUSION,          // 模式1: 输出融合
        PATTERN2_INPUT_DIST_OUTPUT_FUSION, // 模式2: 输入分发 & 输出融合
        PATTERN3_INPUT_DIST              // 模式3: 输入分发
    };

    /**
     * @brief 构造函数。初始化 4 个 DcimEngine 实例。
     */
    DcimCluster();

    /**
     * @brief 析构函数。
     */
    ~DcimCluster();

    // 禁用拷贝构造和赋值操作
    DcimCluster(const DcimCluster&) = delete;
    DcimCluster& operator=(const DcimCluster&) = delete;

    /**
     * @brief 设置后续计算调用的操作模式。
     * @param mode 所需的 OperationMode。
     */
    void setOperationMode(OperationMode mode);

    /**
     * @brief 获取当前配置的操作模式。
     * @return 当前的 OperationMode。
     */
    OperationMode getOperationMode() const;

    // --- 存储管理 (传递给特定引擎和宏单元) ---

    /**
     * @brief 将预对齐数据写入引擎内特定宏单元的整个页面。
     * @param engine_idx 目标引擎索引 (0 到 3)。
     * @param macro_idx 引擎内目标宏单元索引 (0 到 MACROS_PER_ENGINE - 1)。
     * @param page_idx 宏单元内页面索引 (0 到 DCIM_NUM_PAGES - 1)。
     * @param page_buffer 包含对齐页面数据的缓冲区。
     * @return 如果写入成功返回 true，否则返回 false。
     */
    bool writePage(size_t engine_idx, size_t macro_idx, int page_idx, const uint8_t *page_buffer);

    /**
     * @brief 从引擎内特定宏单元的存储中读取整个页面。
     * @param engine_idx 目标引擎索引 (0 到 3)。
     * @param macro_idx 引擎内目标宏单元索引 (0 到 MACROS_PER_ENGINE - 1)。
     * @param page_idx 宏单元内页面索引 (0 到 DCIM_NUM_PAGES - 1)。
     * @param page_buffer 用于存储读取页面数据的缓冲区。
     * @return 如果读取成功返回 true，否则返回 false。
     */
    bool readPage(size_t engine_idx, size_t macro_idx, int page_idx, uint8_t *page_buffer);

    /**
     * @brief 将预对齐数据写入引擎内特定宏单元的特定页面的特定行。
     * @param engine_idx 目标引擎索引 (0 到 3)。
     * @param macro_idx 引擎内目标宏单元索引 (0 到 MACROS_PER_ENGINE - 1)。
     * @param page_idx 宏单元内页面索引 (0 到 DCIM_NUM_PAGES - 1)。
     * @param row_idx 页面内行索引 (0 到 DCIM_NUM_ROWS_PER_PAGE - 1)。
     * @param row_buffer 包含对齐行数据的缓冲区。
     * @return 如果写入成功返回 true，否则返回 false。
     */
    bool writeRow(size_t engine_idx, size_t macro_idx, int page_idx, int row_idx, const uint8_t *row_buffer);

    /**
     * @brief 从引擎内特定宏单元的特定页面的特定行读取数据。
     * @param engine_idx 目标引擎索引 (0 到 3)。
     * @param macro_idx 引擎内目标宏单元索引 (0 到 MACROS_PER_ENGINE - 1)。
     * @param page_idx 宏单元内页面索引 (0 到 DCIM_NUM_PAGES - 1)。
     * @param row_idx 页面内行索引 (0 到 DCIM_NUM_ROWS_PER_PAGE - 1)。
     * @param row_buffer 用于存储读取行数据的缓冲区。
     * @return 如果读取成功返回 true，否则返回 false。
     */
    bool readRow(size_t engine_idx, size_t macro_idx, int page_idx, int row_idx, uint8_t *row_buffer);

    // --- 计算 ---

    /**
     * @brief 使用预对齐输入执行集群计算 (基于配置的 OperationMode)。
     * @param aligned_input_block 包含 4 个引擎数据的连续块 (大小 4*MACROS_PER_ENGINE*ALIGNED_ELEMENTS_PER_MACRO*sizeof(uint16_t))。
     * @param in_type 原始输入向量的数据类型。
     * @param page_idx 每个相关引擎存储中使用的页面索引。
     * @param wt_type 每个相关引擎存储中使用的权重数据类型。
     * @param output_block 输出缓冲区 (必须是大小 CIME_NUM*RESULTS_PER_ENGINE*sizeof(uint32_t))。
     * @return 如果成功返回 true，否则返回 false。
     */
    bool computeAlignedInput(
        const uint8_t *aligned_input_block,
        uint8_t in_type,
        int page_idx,
        uint8_t wt_type,
        uint8_t *output_block
    );

   /**
     * @brief 使用原始输入执行集群计算 (内部对齐, 基于配置的 OperationMode)。
     * @param raw_input_block 包含 4 个引擎数据的连续块 (大小 4*MACROS_PER_ENGINE*RAW_ELEMENTS_PER_MACRO*sizeof(uint16_t))。
     * @param in_type 输入向量的数据类型。
     * @param page_idx 每个相关引擎存储中使用的页面索引。
     * @param wt_type 每个相关引擎存储中使用的权重数据类型。
     * @param output_block 输出缓冲区 (必须是大小 CIME_NUM*RESULTS_PER_ENGINE*sizeof(uint32_t))。
     * @return 如果成功返回 true，否则返回 false。
     */
    bool computeRawInput(
        const uint8_t *raw_input_block,
        uint8_t in_type,
        int page_idx,
        uint8_t wt_type,
        uint8_t *output_block
    );

    // --- 常量 ---
    // 公开以便外部使用/验证 (如果需要)
    static constexpr size_t CIME_NUM = 4;
    static constexpr size_t MACROS_PER_ENGINE = DCIM_ENGINE_N_MACROS; // 应为 2
    static constexpr size_t RESULTS_PER_ENGINE = 64;
    static constexpr size_t RAW_ELEMENTS_PER_MACRO = 32;
    static constexpr size_t ALIGNED_ELEMENTS_PER_MACRO = 33; // DCIM_NUM_ROWS_PER_PAGE
    static constexpr size_t BYTES_PER_ELEMENT = sizeof(uint16_t);
    static constexpr size_t BYTES_PER_RESULT = sizeof(uint32_t);

    // 计算得出的尺寸
    static constexpr size_t RAW_INPUT_BYTES_PER_ENGINE = MACROS_PER_ENGINE * RAW_ELEMENTS_PER_MACRO * BYTES_PER_ELEMENT;
    static constexpr size_t ALIGNED_INPUT_BYTES_PER_ENGINE = MACROS_PER_ENGINE * ALIGNED_ELEMENTS_PER_MACRO * BYTES_PER_ELEMENT;
    static constexpr size_t TOTAL_RAW_INPUT_BYTES = CIME_NUM * RAW_INPUT_BYTES_PER_ENGINE;
    static constexpr size_t TOTAL_ALIGNED_INPUT_BYTES = CIME_NUM * ALIGNED_INPUT_BYTES_PER_ENGINE;
    static constexpr size_t RESULTS_BYTES_PER_ENGINE = RESULTS_PER_ENGINE * BYTES_PER_RESULT;
    static constexpr size_t TOTAL_OUTPUT_BYTES = CIME_NUM * RESULTS_BYTES_PER_ENGINE;


private:
    std::vector<DcimEngine> engines;
    OperationMode current_mode;

    // 用于计算完整块内输入偏移量的辅助函数
    const uint8_t* getRawInputPtrForEngine(const uint8_t* block_start, size_t engine_idx) const;
    const uint8_t* getAlignedInputPtrForEngine(const uint8_t* block_start, size_t engine_idx) const;

    // 用于输出聚合的辅助函数
    // 接受包含所有引擎结果的扁平向量 [R0, R1, R2, R3]
    void accumulateResults(const std::vector<uint32_t>& all_engine_results, uint32_t* final_output, size_t start_engine, size_t num_engines_to_sum, size_t output_offset_elements);
    void concatenateResults(const std::vector<uint32_t>& all_engine_results, uint32_t* final_output);

    // 由 computeRawInput 和 computeAlignedInput 共享的内部计算逻辑
    bool computeInternal(
        const uint8_t *aligned_input_block, // 总是接收对齐的输入块
        uint8_t in_type,
        int page_idx,
        uint8_t wt_type,
        uint8_t *output_block // 最终输出缓冲区
    );

    // 用于检查引擎索引有效性的辅助函数
    bool isValidEngineIdx(size_t engine_idx) const;

};
```


