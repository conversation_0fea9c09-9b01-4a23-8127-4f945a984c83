#ifndef _DCIM_MATRIX_UTILS_H_
#define _DCIM_MATRIX_UTILS_H_

#include <stdbool.h>  // For bool type
#include <stdint.h>
#include "dcim_com.h"      // For data_fmt enum and float_data_align declaration
#include "dcim_storage.h"  // For dimension constants

#ifdef __cplusplus
extern "C"
{
#endif

    /**
     * @brief Determines the number of logical columns based on weight data type.
     *
     * @param wt_type The data type of the weights (using data_fmt enum).
     * @return The number of logical columns (e.g., 64 for INT4, 32 for INT8, 16 for INT16).
     *         Returns 0 for unsupported types.
     */
    int get_num_logical_columns(uint8_t wt_type);

    /**
     * @brief Converts a raw weight matrix (column-major) to an aligned weight matrix
     * (column-major).
     *
     * This function iterates through each column of the raw matrix, applies alignment
     * using float_data_align (if applicable for the data type), and stores the result
     * in the output aligned matrix. Fixed-point types are copied directly without alignment.
     *
     * @param raw_matrix Pointer to the raw weight matrix data (column-major).
     *                   Expected layout: uint16_t[num_columns][32].
     * @param wt_type The data type of the weights (determines alignment and num_columns).
     * @param aligned_matrix_col_major Output buffer for the aligned weight matrix (column-major).
     *                                 Expected layout: uint16_t[num_columns][33].
     * @return true if conversion was successful, false otherwise (e.g., null pointers, invalid
     * type).
     */
    bool convertRawToAlignedColumnMajor(
        const uint8_t*
            raw_matrix,  // Input: Flattened column-major raw data [cols][32*sizeof(uint16_t)]
        uint8_t wt_type,
        uint8_t* aligned_matrix_col_major  // Output: Flattened column-major aligned data
                                           // [cols][33*sizeof(uint16_t)]
    );

    /**
     * @brief Converts an aligned weight matrix (column-major) to the page storage format
     * (row-major).
     *
     * This function takes the column-major aligned data (output from convertRawToAlignedColumnMajor
     * or provided directly) and rearranges/packs it into the row-major format expected by
     * DcimArray::writePage or write_storage_page.
     *
     * @param aligned_matrix_col_major Pointer to the aligned weight matrix data (column-major).
     *                                 Expected layout: uint16_t[num_columns][33].
     * @param wt_type The data type of the weights (determines packing and num_columns).
     * @param page_buffer Output buffer in the row-major page storage format.
     *                    Expected layout: uint16_t[DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_WORDS_PER_ROW].
     * @return true if conversion was successful, false otherwise (e.g., null pointers, invalid
     * type).
     */
    bool convertAlignedColumnMajorToPageRowMajor(
        const uint8_t* aligned_matrix_col_major,  // Input: Flattened column-major aligned data
                                                  // [cols][33*sizeof(uint16_t)]
        uint8_t wt_type,
        uint8_t*
            page_buffer  // Output: Row-major page format pointer, size should be [33][32] bytes
    );

#ifdef __cplusplus
}
#endif

#endif  // _DCIM_MATRIX_UTILS_H_
