#ifndef _DCIM_ARRAY_HPP_
#define _DCIM_ARRAY_HPP_

#include <stdint.h>
#include <cstring>  // For memcpy, memset
#include <vector>   // Using vector for potential flexibility if needed later

// Include C headers within extern "C" block
extern "C"
{
#include "dcim_com.h"
// #include "dcim_compute.h" // We will replicate compute logic, not call directly
#include "dcim_extract.h"  // Provides extract_aligned_weight_column
#include "dcim_storage.h"  // Provides dimension constants
}

class DcimArray
{
  public:
    /**
     * @brief Constructor for the DcimArray class.
     *        Initializes the internal storage to zeros.
     */
    DcimArray();

    /**
     * @brief Destructor.
     */
    ~DcimArray();

    // --- Storage Management ---

    /**
     * @brief Writes pre-aligned data to an entire page in the internal DCIM storage.
     *
     * @param page_idx The index of the page to write to (0 to DCIM_NUM_PAGES - 1).
     * @param page_buffer A buffer containing the aligned page data to write
     *                    (must be size [DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW]).
     * @return true if write was successful, false otherwise (e.g., invalid index).
     */
    bool writePage(int page_idx, const uint8_t* page_buffer);

    /**
     * @brief Reads an entire page from the internal DCIM storage.
     *
     * @param page_idx The index of the page to read (0 to DCIM_NUM_PAGES - 1).
     * @param page_buffer A buffer to store the read page data
     *                    (must be size [DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW]).
     * @return true if read was successful, false otherwise (e.g., invalid index).
     */
    bool readPage(int page_idx, uint8_t* page_buffer);

    /**
     * @brief Writes pre-aligned data to a specific row on a specific page in the internal DCIM
     * storage.
     *
     * @param page_idx The index of the page to write to (0 to DCIM_NUM_PAGES - 1).
     * @param row_idx The index of the row within the page (0 to DCIM_NUM_ROWS_PER_PAGE - 1).
     * @param row_buffer A buffer containing the aligned row data to write (must be size
     * DCIM_NUM_BYTES_PER_ROW).
     * @return true if write was successful, false otherwise (e.g., invalid index).
     */
    bool writeRow(int page_idx, int row_idx, const uint8_t* row_buffer);

    /**
     * @brief Reads a specific row from a specific page in the internal DCIM storage.
     *
     * @param page_idx The index of the page to read from (0 to DCIM_NUM_PAGES - 1).
     * @param row_idx The index of the row within the page (0 to DCIM_NUM_ROWS_PER_PAGE - 1).
     * @param row_buffer A buffer to store the read row data (must be size DCIM_NUM_BYTES_PER_ROW).
     * @return true if read was successful, false otherwise (e.g., invalid index).
     */
    bool readRow(int page_idx, int row_idx, uint8_t* row_buffer);

    // --- Computation ---

    /**
     * @brief Performs the full vector-matrix multiplication using data from a specific
     *        page in the internal DCIM storage.
     *
     * Assumes the internal storage page contains pre-aligned weight data.
     * Assumes the input vector provided is also pre-aligned.
     *
     * @param aligned_input_vector The pre-aligned input vector (size 33, including shared
     * exponent).
     * @param in_type The data type of the input vector (using data_fmt enum).
     * @param page_idx The index of the page in internal storage containing the aligned weight
     * matrix.
     * @param wt_type The data type of the weights stored on the page.
     * @param result_vector Output buffer to store the 64 results (size 64).
     * @return true if computation was successful, false otherwise (e.g., invalid index or type).
     */
    bool compute(const uint8_t* aligned_input_vector,  // Size 33*sizeof(uint16_t)
                 uint8_t in_type,
                 int page_idx,
                 uint8_t wt_type,
                 uint8_t* result_vector  // Size 64*sizeof(uint32_t)
    );

    // --- Optional Alignment Functionality (if needed) ---

    /**
     * @brief Aligns a raw input vector. (Wrapper around float_data_align)
     *        Note: Assumes input alignment is independent of weight context.
     *
     * @param raw_input_vector Raw input vector (size 32).
     * @param in_type Data type of the input.
     * @param aligned_output Output buffer for the aligned vector (size 33).
     */
    void alignInputVector(const uint8_t* raw_input_vector,
                          uint8_t in_type,
                          uint8_t* aligned_output);

    // --- Optional Helper Function (If needed for external raw input) ---
    /**
     * @brief Performs alignment on a raw input vector and then calls
     *        dcim_compute_from_storage_aligned_input.
     *
     * @param raw_input_vector The raw input vector (size 32).
     * @param in_type The data type of the input vector (using data_fmt enum).
     * @param page_idx The index of the page in storage containing the aligned weight matrix.
     * @param wt_type The data type of the weights stored on the page.
     * @param result_vector Output buffer to store the 64 results.
     */
    void computeRawInput(const uint8_t* raw_input_vector,  // Size 32*sizeof(uint16_t)
                         uint8_t in_type,
                         int page_idx,
                         uint8_t wt_type,
                         uint8_t* result_vector  // Size 64*sizeof(uint32_t)
    );

  private:
    // Internal storage array for this instance
    uint8_t internal_storage[DCIM_NUM_PAGES][DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW];

    // Helpers for bounds checking
    bool isValidPage(int page_idx) const;
    bool isValidRow(int row_idx) const;  // Added row check

    // Determine Max Columns for Weight Type (Internal helper)
    int getMaxColumns(uint8_t wt_type) const;
};

#endif  // _DCIM_ARRAY_HPP_
