#pragma once
#include <stdint.h>
static uint16_t in_mhex_fp16[4][32] = {
    {0xc54c, 0x31c5, 0x5c81, 0x46c0, 0xb3ab, 0xe339, 0xdae8, 0xe750, 0x34e7, 0xdede, 0xc2a7,
     0x44cd, 0xdc2c, 0xd493, 0xc7ed, 0xbcba, 0xe0a9, 0xb956, 0xd201, 0xd060, 0x30b7, 0x2d79,
     0xd8d1, 0x3581, 0x4462, 0x505a, 0xdd55, 0xd159, 0xc4c1, 0x3b71, 0x575b, 0xc4d2},

    {0x4544, 0x5aa4, 0xa8c4, 0xbeae, 0xd20d, 0xc683, 0xb514, 0xe0b7, 0xccf1, 0x2cde, 0xcb9e,
     0x6631, 0xd737, 0xd635, 0x54bf, 0x4f10, 0xe10c, 0x33ce, 0x6023, 0x59ed, 0xbd89, 0x5d6a,
     0x306b, 0x5495, 0x38d3, 0xd1bf, 0x331a, 0x5fed, 0x6557, 0xe2dd, 0x38ff, 0x42f6},
    {0},
    {0}};

static uint16_t in_mhex_bbf16[4][32] = {
    {0xc1e1, 0xbc43, 0xc20d, 0x42d9, 0x3fa9, 0x4247, 0xbeb5, 0xbd1b, 0xc0e8, 0x43c5, 0xc1f1,
     0x4199, 0x3e20, 0xbca5, 0x3cb1, 0xc2f1, 0xbe27, 0xc130, 0xc1b2, 0xbf3d, 0xc379, 0xc060,
     0x4139, 0x4350, 0xbcd8, 0xc032, 0xbe3f, 0xc132, 0x3d1f, 0x4153, 0x42bf, 0xbc10},
    {0x3fb7, 0x42d2, 0x42fe, 0x3d1c, 0xc398, 0x403b, 0xbfdc, 0x42c5, 0xc08e, 0xc296, 0x3d1d,
     0xc21d, 0xbe0f, 0x42ab, 0xbc5e, 0xc374, 0x4343, 0xbe36, 0xbdc4, 0xbdfa, 0x3d60, 0xc0ab,
     0x4232, 0xbd76, 0xc3e7, 0x3dec, 0xc1dd, 0x415c, 0xbe5e, 0x41ae, 0xc034, 0x3c92},
    {0},
    {0}};

static uint16_t in_mhex_fp8e4[4][32] = {
    {0x00c0, 0x00ce, 0x00cb, 0x0088, 0x00df, 0x00c7, 0x0053, 0x0044, 0x0045, 0x00d5, 0x00d2,
     0x00dd, 0x0055, 0x000c, 0x0099, 0x0059, 0x00de, 0x001c, 0x0056, 0x00d7, 0x00dc, 0x0057,
     0x008b, 0x00ce, 0x0099, 0x0056, 0x00dd, 0x001f, 0x0054, 0x0059, 0x0099, 0x0093},

    {0x0017, 0x0017, 0x005a, 0x00cd, 0x0091, 0x004b, 0x00dd, 0x0001, 0x00d2, 0x00db, 0x0053,
     0x0042, 0x000d, 0x00d9, 0x001f, 0x0048, 0x008e, 0x0046, 0x00cb, 0x00c1, 0x00cc, 0x009b,
     0x00d2, 0x00ca, 0x0002, 0x00db, 0x0016, 0x009b, 0x00ca, 0x0052, 0x00c3, 0x00c0},
    {0},
    {0}};

static uint16_t in_mhex_fp8e5[4][32] = {
    {0x000f, 0x0021, 0x0001, 0x0017, 0x00b3, 0x0086, 0x0091, 0x0035, 0x0094, 0x0081, 0x00a7,
     0x0096, 0x00a6, 0x000c, 0x0099, 0x0017, 0x003b, 0x00a5, 0x0022, 0x0005, 0x00b3, 0x0039,
     0x00ad, 0x0037, 0x002d, 0x001d, 0x0089, 0x0093, 0x0022, 0x0007, 0x00a6, 0x00b8},

    {0x00a3, 0x003a, 0x00a9, 0x0093, 0x0084, 0x0001, 0x009e, 0x0001, 0x00a5, 0x003c, 0x002e,
     0x009a, 0x00a7, 0x00a9, 0x0029, 0x003d, 0x0028, 0x0021, 0x009c, 0x0034, 0x009f, 0x0085,
     0x0024, 0x002e, 0x0031, 0x00b4, 0x001b, 0x0007, 0x0099, 0x0030, 0x001c, 0x002c},
    {0},
    {0}};

//---------------------------
static uint16_t in_table_tr_fp16[9][32] = {
    /*INT4*/ {0x000e, 0x0004, 0x0001, 0x000d, 0x000a, 0x0004, 0x0009, 0x0003,
              0x0003, 0x0001, 0x0008, 0x000b, 0x0005, 0x0000, 0x0004, 0x000c,
              0x000f, 0x0002, 0x0009, 0x000c, 0x000d, 0x0005, 0x0008, 0x000e,
              0x000b, 0x000a, 0x0006, 0x0007, 0x0003, 0x0007, 0x0004, 0x0003},
    /*INT8*/ {0x0037, 0x00a2, 0x003d, 0x0092, 0x00d3, 0x0080, 0x002c, 0x007e,
              0x00ed, 0x00bf, 0x00c0, 0x000d, 0x00a9, 0x00e5, 0x0099, 0x0092,
              0x005e, 0x00e3, 0x00ee, 0x00a3, 0x0092, 0x008e, 0x0090, 0x0081,
              0x00e6, 0x008e, 0x003a, 0x00df, 0x00e8, 0x000a, 0x00d8, 0x0066},
    /*INT12*/ {0x64ab, 0x8a3c, 0x8739, 0x7acb, 0x9b11, 0xfcdc, 0xc802, 0xe57e,
               0xad38, 0x99cb, 0x3826, 0x1267, 0x1ea7, 0x4894, 0x9df0, 0xf576,
               0x979a, 0xf134, 0x2e47, 0xd82a, 0x804a, 0x1d5e, 0x5079, 0x4b70,
               0xd1bb, 0x9745, 0x6b10, 0x46e2, 0x8e71, 0x89f6, 0xcd4e, 0x91e6},
    /*INT16*/ {0x00bf, 0x0ab7, 0x03be, 0x0e20, 0x0e9e, 0x009c, 0x0399, 0x0ff5,
               0x0ec9, 0x0393, 0x0c54, 0x0e40, 0x04ff, 0x032f, 0x010a, 0x037a,
               0x06e7, 0x0f5f, 0x0248, 0x0756, 0x03db, 0x00d8, 0x0460, 0x0116,
               0x0f0f, 0x027f, 0x0e6a, 0x06aa, 0x0e88, 0x0439, 0x0a5d, 0x05ea},
    /*FP16*/ {0x40d1, 0x585e, 0xc259, 0x2dfe, 0x6210, 0xdaed, 0x6565, 0xd3fd,
              0xe0a5, 0x6220, 0xb84c, 0x3d7e, 0xe237, 0x412e, 0x614d, 0xadf0,
              0xd862, 0xb5e6, 0xbf74, 0x29a9, 0x39c5, 0xae30, 0x60bc, 0x4932,
              0x2cb2, 0x3dd2, 0xaea8, 0x3737, 0x667a, 0xdee0, 0x2fbd, 0xd024},
    /*BF16*/ {0x4022, 0xbf64, 0x4217, 0xbfb6, 0xbd6f, 0x41c1, 0xc39d, 0x42a5,
              0x3eb3, 0xc13e, 0xc3d2, 0x3db9, 0xc237, 0x3ede, 0x41dc, 0xbef6,
              0xc088, 0xc04c, 0x40e2, 0x41a0, 0xc140, 0xc2fa, 0xbc61, 0xbe6c,
              0x3ee5, 0xbe72, 0x43f0, 0x3d92, 0xbf8b, 0xc22b, 0xc021, 0xbf56},
    /*BBF16*/ {0xbc2e, 0x3ed3, 0xbef2, 0x3c84, 0x4288, 0xc025, 0x3ffe, 0x3d5d,
               0xbd47, 0x40a7, 0xc29e, 0xc38d, 0x42eb, 0x3e1f, 0x40ed, 0x3cd1,
               0x3f46, 0xbd35, 0xc15a, 0xbdf2, 0xbcc2, 0xc04d, 0xc2a6, 0xc090,
               0x43f6, 0x3e6c, 0x43c5, 0x4328, 0xbd22, 0xbd9c, 0x414a, 0xbfb2},
    /*FP8E4*/ {0x0008, 0x0017, 0x0091, 0x00c1, 0x00c4, 0x001c, 0x008f, 0x00ce,
               0x00c6, 0x00cc, 0x0047, 0x0007, 0x005a, 0x0011, 0x00d6, 0x0009,
               0x00d9, 0x00d3, 0x009e, 0x0010, 0x0091, 0x00c0, 0x005e, 0x00ce,
               0x000d, 0x00c2, 0x0088, 0x0007, 0x008f, 0x00ce, 0x0098, 0x0083},
    /*FP8E5*/ {0x00a9, 0x008b, 0x00a1, 0x001f, 0x00b8, 0x003c, 0x00ab, 0x0026,
               0x00b7, 0x0028, 0x00bf, 0x0025, 0x0015, 0x001e, 0x00ba, 0x0035,
               0x0094, 0x0017, 0x00a8, 0x0034, 0x003f, 0x002a, 0x002a, 0x0033,
               0x00b2, 0x00b5, 0x009e, 0x00b8, 0x0013, 0x00b6, 0x00a1, 0x0030}};

static uint16_t in_table_tr_bf16[9][32] = {
    /*INT4*/ {0x0002, 0x0000, 0x0007, 0x0003, 0x000c, 0x0003, 0x0005, 0x0009,
              0x0001, 0x0002, 0x0001, 0x0002, 0x0004, 0x0001, 0x0000, 0x000a,
              0x0007, 0x000d, 0x000d, 0x0008, 0x000e, 0x000a, 0x000e, 0x0001,
              0x0000, 0x0006, 0x0008, 0x000b, 0x0002, 0x0003, 0x0000, 0x0001},
    /*INT8*/ {0x00ce, 0x006e, 0x00f1, 0x0074, 0x005f, 0x00dd, 0x0058, 0x006e,
              0x007b, 0x00fa, 0x000a, 0x007a, 0x00cb, 0x004e, 0x0023, 0x00b8,
              0x0081, 0x004e, 0x008a, 0x000a, 0x00da, 0x0067, 0x00d5, 0x000d,
              0x0043, 0x000a, 0x0065, 0x00a3, 0x009a, 0x00ca, 0x0081, 0x0075},
    /*INT12*/ {0x1852, 0xd33d, 0x70dd, 0xe578, 0xe791, 0xf51c, 0xc125, 0xd4b1,
               0x5024, 0xc49b, 0xebf4, 0x88d7, 0x08a8, 0x7881, 0xb67f, 0x58a0,
               0x2631, 0x6c46, 0x0a50, 0xa6d5, 0xed76, 0x5ec8, 0x8d55, 0x03eb,
               0x37d1, 0xd20a, 0x9ee2, 0x04b9, 0xd3b2, 0xb948, 0x3dbd, 0x1026},
    /*INT16*/ {0x0ee6, 0x0a42, 0x0879, 0x0776, 0x0db7, 0x0dff, 0x0dc2, 0x00cc,
               0x0ebc, 0x07f4, 0x0882, 0x051b, 0x02ae, 0x0f6a, 0x05b5, 0x0ce9,
               0x0596, 0x006a, 0x0a23, 0x0c0f, 0x01c6, 0x05ca, 0x0d11, 0x0bf7,
               0x0134, 0x038c, 0x07df, 0x08d5, 0x0633, 0x0ac3, 0x06bd, 0x0df5},
    /*FP16*/ {0xb929, 0xb9ad, 0x3c51, 0x2dbd, 0x41b3, 0x32f0, 0x4086, 0xc494,
              0x4bb6, 0x2b01, 0x3fa7, 0xb0e8, 0x412b, 0xb55b, 0xe710, 0xbc98,
              0xdd04, 0x5c60, 0x573a, 0x58fe, 0x630d, 0xc8fb, 0xb6ee, 0xcb49,
              0xd30b, 0xe00c, 0x4519, 0xd9d9, 0x4812, 0xe539, 0xb6a9, 0x501e},
    /*BF16*/ {0xc1d0, 0xbdfd, 0x3fa4, 0xbdb0, 0x3da7, 0x3c4b, 0xbe24, 0x4304,
              0x405d, 0x3f67, 0x3d81, 0x4353, 0xc1ad, 0xc105, 0xc13e, 0xbe2c,
              0x3e9c, 0xbcfd, 0x41fa, 0x3cef, 0xbcdd, 0xbf4e, 0x4002, 0x400f,
              0x42a8, 0xbf1a, 0x415d, 0x42f9, 0x411a, 0x433d, 0xc3f1, 0x43f0},
    /*BBF16*/ {0xc1d9, 0xc163, 0xc27e, 0xbfc4, 0x4385, 0x3f8d, 0x3f93, 0x3f73,
               0xc352, 0xbdd2, 0xc3d8, 0x3fc6, 0x4190, 0xc2f7, 0xc1c4, 0xbc34,
               0xbd54, 0xc2f7, 0xbe6c, 0xbd16, 0xbcbd, 0xbc8d, 0xbe18, 0x4041,
               0xc104, 0x4235, 0xc3d3, 0xc016, 0xbc15, 0x402e, 0xbe34, 0xbe05},
    /*FP8E4*/ {0x001a, 0x000f, 0x004a, 0x00ce, 0x0002, 0x0005, 0x00ce, 0x005e,
               0x0050, 0x0047, 0x000e, 0x00cd, 0x0054, 0x00dc, 0x0010, 0x0047,
               0x004d, 0x0018, 0x0054, 0x0011, 0x0043, 0x009e, 0x0017, 0x00d6,
               0x00d5, 0x001b, 0x0084, 0x0047, 0x00c4, 0x00cd, 0x00d8, 0x0017},
    /*FP8E5*/ {0x000e, 0x00b5, 0x003d, 0x00ba, 0x000e, 0x00a4, 0x0089, 0x0036,
               0x001d, 0x0007, 0x0006, 0x0087, 0x0035, 0x00ae, 0x0095, 0x00af,
               0x0039, 0x0007, 0x00a4, 0x0016, 0x008b, 0x0023, 0x000b, 0x0088,
               0x00a4, 0x000b, 0x0082, 0x0010, 0x0094, 0x002b, 0x0009, 0x0016}};

static uint16_t in_table_tr_bbf16[9][32] = {
    /*INT4*/ {0x0000, 0x000a, 0x0005, 0x0006, 0x0002, 0x0009, 0x0009, 0x000a,
              0x0008, 0x0006, 0x0003, 0x000a, 0x0008, 0x000b, 0x000c, 0x000b,
              0x0006, 0x000f, 0x000d, 0x000c, 0x0004, 0x0004, 0x0006, 0x000e,
              0x000c, 0x0002, 0x000e, 0x000a, 0x000a, 0x000b, 0x000d, 0x0006},
    /*INT8*/ {0x0082, 0x00c1, 0x003b, 0x00ee, 0x0028, 0x003c, 0x00ed, 0x0014,
              0x00ff, 0x0028, 0x0086, 0x00cc, 0x001e, 0x0011, 0x007e, 0x0096,
              0x003e, 0x00a0, 0x0048, 0x00c1, 0x009c, 0x00c5, 0x0074, 0x0034,
              0x0069, 0x00d4, 0x00ac, 0x003c, 0x0044, 0x0051, 0x0047, 0x0013},
    /*INT12*/ {0xdb5c, 0xd09b, 0x9251, 0xb9f7, 0x02f6, 0x0f33, 0x5a18, 0xcf55,
               0x58ac, 0x2cda, 0x6736, 0x235c, 0x7530, 0x19c4, 0xec77, 0x1d4b,
               0x6df8, 0x0b63, 0xb4e2, 0x42d3, 0xbbdd, 0x07cd, 0x3071, 0x00cf,
               0xe75b, 0xde96, 0x6f48, 0x5f85, 0x03d5, 0x06df, 0x7ebd, 0xc1b7},
    /*INT16*/ {0x0349, 0x0b53, 0x05d0, 0x063c, 0x08af, 0x0ab4, 0x06c4, 0x0d38,
               0x071f, 0x011b, 0x0096, 0x07f8, 0x0c2b, 0x013b, 0x0996, 0x0fb5,
               0x013a, 0x05ed, 0x03c6, 0x09e0, 0x0e3b, 0x0cf9, 0x0b5f, 0x0cc0,
               0x04e1, 0x0b63, 0x0632, 0x0d20, 0x04a5, 0x08a4, 0x0487, 0x0d50},
    /*FP16*/ {0x4d4d, 0xe61e, 0xcfe4, 0xbf54, 0x4d7c, 0x62cd, 0xc6fb, 0x2ae7,
              0x4957, 0x5b2b, 0x583b, 0x5b36, 0xa880, 0xc1a1, 0xc9f7, 0xdaef,
              0x319d, 0xaa00, 0x5f0f, 0xb2da, 0xd002, 0xdf49, 0x5cd8, 0xe3c0,
              0x5ae4, 0x4ab0, 0x4c1b, 0x3eff, 0x3a45, 0xd930, 0xdf27, 0x650d},
    /*BF16*/ {0x3fcc, 0xc1d6, 0xbfb2, 0xc2f2, 0x411d, 0xbfdc, 0xbfb5, 0xbff9,
              0x3c3f, 0x415b, 0x41b7, 0x41e1, 0x3f6c, 0x3cb9, 0xc251, 0x4314,
              0x3d5d, 0x3fc3, 0x3fa6, 0x3c09, 0xc38d, 0xc294, 0xbc29, 0x42b6,
              0x4138, 0x3ccf, 0xc057, 0xbdd0, 0xbeb0, 0xbf3f, 0xbf0f, 0xbdba},
    /*BBF16*/ {0xc382, 0xc041, 0x413a, 0x4171, 0xc3f5, 0x40a3, 0x4280, 0x41ff,
               0xbffb, 0x3f27, 0xc350, 0x4276, 0xc1c9, 0x42cb, 0xc2fc, 0x4049,
               0x4187, 0xc3d7, 0xbc50, 0x3f1f, 0xc139, 0xc1b8, 0xbde7, 0x3d9d,
               0xc23b, 0xc3a0, 0xc375, 0x3f79, 0xc0f2, 0x3f2e, 0x3d37, 0xc2df},
    /*FP8E4*/ {0x0048, 0x008f, 0x00c6, 0x009b, 0x009d, 0x00ce, 0x0083, 0x00d9,
               0x000e, 0x0002, 0x0041, 0x0044, 0x001c, 0x0092, 0x0017, 0x00cf,
               0x00cd, 0x0044, 0x0053, 0x00da, 0x00ca, 0x00dd, 0x0001, 0x0049,
               0x0084, 0x00d0, 0x009b, 0x00c6, 0x0080, 0x00d8, 0x009c, 0x0045},
    /*FP8E5*/ {0x003d, 0x0026, 0x0003, 0x0092, 0x00b9, 0x0096, 0x00b0, 0x001e,
               0x002f, 0x0038, 0x008d, 0x0013, 0x00ac, 0x0002, 0x0083, 0x0031,
               0x0036, 0x00b4, 0x003d, 0x008d, 0x00bc, 0x003f, 0x0082, 0x009b,
               0x0022, 0x0031, 0x00b0, 0x0015, 0x0029, 0x0027, 0x00b9, 0x00b9}};

static uint16_t in_table_tr_fp8e4[9][32] = {
    /*INT4*/ {0x0007, 0x000e, 0x0000, 0x0001, 0x000d, 0x0005, 0x0007, 0x0002,
              0x0000, 0x0009, 0x000f, 0x0003, 0x000b, 0x000c, 0x0006, 0x000c,
              0x0000, 0x000b, 0x0009, 0x0001, 0x0000, 0x0005, 0x0007, 0x000f,
              0x0008, 0x000c, 0x0002, 0x000c, 0x0003, 0x0005, 0x0003, 0x0001},
    /*INT8*/ {0x0093, 0x00eb, 0x0017, 0x00f7, 0x005a, 0x0001, 0x00f3, 0x00e5,
              0x000e, 0x0023, 0x00c2, 0x00bb, 0x000f, 0x0054, 0x00e2, 0x00a9,
              0x00cb, 0x00ab, 0x00a2, 0x003d, 0x0041, 0x00ca, 0x0036, 0x0032,
              0x006d, 0x0062, 0x008d, 0x00cc, 0x006e, 0x0074, 0x00e1, 0x0066},
    /*INT12*/ {0xe52d, 0x5248, 0x193e, 0xf4e3, 0xa365, 0xa2b5, 0x5c38, 0x43d3,
               0x5bba, 0x988a, 0x6f77, 0x7e1a, 0x2fd1, 0x1de7, 0x511c, 0x2539,
               0x1fd1, 0xb1b2, 0xba2f, 0x5897, 0xcae8, 0x3da1, 0xf2bf, 0xc224,
               0xa85e, 0x3b41, 0xcf75, 0xf372, 0xbf3f, 0xcdc1, 0x259a, 0x882d},
    /*INT16*/ {0x0307, 0x017c, 0x079e, 0x09e9, 0x0793, 0x0b01, 0x0723, 0x0d1a,
               0x0fbd, 0x0aa6, 0x0d87, 0x0a38, 0x0a41, 0x040e, 0x022e, 0x03d0,
               0x0d42, 0x0385, 0x0b4a, 0x0526, 0x02e5, 0x0949, 0x0513, 0x0287,
               0x0295, 0x03f8, 0x0caf, 0x0d09, 0x0324, 0x0828, 0x0570, 0x0ddb},
    /*FP16*/ {0xd1a2, 0x6473, 0xe71b, 0x4a56, 0x307b, 0x4f40, 0xc6be, 0xac39,
              0x5ebd, 0x60b8, 0xc0f2, 0xd781, 0xb019, 0x2e69, 0xbe36, 0x566a,
              0x51a8, 0xcbae, 0x5f4b, 0xab3e, 0x5138, 0xcad1, 0xaf29, 0xb00f,
              0xb82a, 0xc2c1, 0xb6db, 0xba1d, 0x4bb8, 0x57f6, 0xc751, 0xb5f3},
    /*BF16*/ {0xbde9, 0x3d84, 0xbdea, 0x4278, 0xc0fb, 0xbedb, 0x4016, 0xc188,
              0xbf64, 0xbd0f, 0x4399, 0x3e0a, 0x42da, 0xc09d, 0x4230, 0x405d,
              0x4395, 0x3e59, 0xbc94, 0x3eaf, 0x3de7, 0xbe8e, 0xbe50, 0xbea1,
              0xbd98, 0xbce4, 0x43b8, 0x43ff, 0xc29f, 0xbf37, 0x419b, 0xbc38},
    /*BBF16*/ {0xc1ce, 0x3dda, 0x40ed, 0x40d3, 0xc105, 0xc069, 0x407d, 0x4393,
               0xbe28, 0x3c8a, 0xc2e6, 0x3fa0, 0x3cb3, 0x40cc, 0xbe62, 0xc11f,
               0x4097, 0x3e1d, 0x42b9, 0x3f66, 0xc16b, 0xc0ef, 0xbeab, 0xbf55,
               0x3f57, 0xbcc8, 0xbd5b, 0xbccd, 0x4391, 0xc043, 0xc20f, 0xc3ac},
    /*FP8E4*/ {0x000a, 0x00ca, 0x00d7, 0x00db, 0x00cc, 0x0007, 0x0096, 0x000b,
               0x0059, 0x000c, 0x00df, 0x004d, 0x00c8, 0x00c1, 0x0045, 0x0017,
               0x0045, 0x00c8, 0x009a, 0x0099, 0x0000, 0x00d3, 0x0009, 0x0055,
               0x001c, 0x008c, 0x008e, 0x0091, 0x0007, 0x00d1, 0x0085, 0x00cd},
    /*FP8E5*/ {0x00a5, 0x002e, 0x003d, 0x0000, 0x0088, 0x00bf, 0x00bd, 0x001b,
               0x009c, 0x001f, 0x0026, 0x000e, 0x00b9, 0x0088, 0x0014, 0x00a6,
               0x003e, 0x0025, 0x0097, 0x0092, 0x0023, 0x00bc, 0x0083, 0x00ae,
               0x002e, 0x009e, 0x00a0, 0x0083, 0x001a, 0x00be, 0x002b, 0x0022}};

static uint16_t in_table_tr_fp8e5[9][32] = {
    /*INT4*/ {0x000d, 0x000b, 0x000c, 0x0009, 0x0000, 0x0001, 0x0001, 0x0005,
              0x0001, 0x000d, 0x0002, 0x0003, 0x0006, 0x000c, 0x0004, 0x0005,
              0x000b, 0x000f, 0x000a, 0x0002, 0x000e, 0x0005, 0x0005, 0x0006,
              0x000b, 0x0007, 0x0001, 0x000a, 0x000b, 0x0003, 0x000b, 0x000f},
    /*INT8*/ {0x0057, 0x00a4, 0x0050, 0x001b, 0x00c2, 0x0026, 0x001d, 0x009d,
              0x00b4, 0x001e, 0x00f2, 0x0060, 0x0003, 0x009a, 0x00d9, 0x0037,
              0x009c, 0x00b6, 0x0010, 0x0037, 0x00bd, 0x00c2, 0x0065, 0x0065,
              0x006f, 0x0002, 0x00e3, 0x00ac, 0x00d0, 0x0056, 0x0040, 0x0054},
    /*INT12*/ {0xb196, 0xb8d8, 0x5981, 0xfe40, 0x5444, 0xe6cb, 0x6d2d, 0x1cf3,
               0x4063, 0xf2ca, 0x60d3, 0x34a2, 0x0565, 0x6e74, 0xbcc2, 0x7934,
               0x510b, 0x5c53, 0x571c, 0x29e0, 0x39da, 0x509e, 0x08dc, 0x4272,
               0xd796, 0xddbb, 0xa86c, 0xf13a, 0x26e1, 0xfe6f, 0x8498, 0x9d9d},
    /*INT16*/ {0x0c18, 0x0a14, 0x026c, 0x08f0, 0x02ee, 0x02f5, 0x08d9, 0x07ad,
               0x0a13, 0x069d, 0x0960, 0x0ac5, 0x07f7, 0x06b1, 0x0762, 0x0a62,
               0x0081, 0x0272, 0x0a24, 0x087a, 0x05a0, 0x0dae, 0x0f3f, 0x0c44,
               0x0438, 0x0b4f, 0x0622, 0x08cf, 0x0eec, 0x09eb, 0x0fc1, 0x0f3f},
    /*FP16*/ {0xda6e, 0x39f8, 0x4996, 0x55a4, 0xd960, 0xb47f, 0x59c0, 0xc457,
              0xb47a, 0x411d, 0xc634, 0xc03a, 0xc952, 0xbd6b, 0xe043, 0xadfd,
              0xcd4d, 0xd3f5, 0x3458, 0x46a0, 0xd642, 0x5f80, 0xe60b, 0x376e,
              0x4afc, 0x31a2, 0x4b8e, 0x6571, 0x67c5, 0xc944, 0xcd01, 0x5841},
    /*BF16*/ {0x4320, 0x42fc, 0xc207, 0x3c6c, 0xbf3a, 0xc115, 0x3f4d, 0x3e73,
              0xc1cc, 0x432a, 0x42fa, 0xbc31, 0xbf11, 0x3e8a, 0xc32e, 0xc087,
              0xc3a4, 0x4088, 0x3e4d, 0xc32b, 0xbdf9, 0x3d7c, 0xbeac, 0xc374,
              0xc185, 0xbfc6, 0xbcb8, 0x3f32, 0xc1b9, 0xbd69, 0x3c1e, 0x3cc8},
    /*BBF16*/ {0x3fa4, 0xc35d, 0x3d7c, 0xbcf9, 0xbead, 0x3ced, 0xc20d, 0x4328,
               0x4264, 0xbec3, 0xbf39, 0xc3a6, 0x41ad, 0xc1e1, 0xbfdc, 0xc271,
               0xbf26, 0xc145, 0x3f46, 0xbc41, 0xc11a, 0xc165, 0xc12a, 0x4287,
               0xbc8a, 0x41f2, 0xc2fc, 0x3dc6, 0xc37d, 0x4176, 0x426e, 0xbf38},
    /*FP8E4*/ {0x004d, 0x009f, 0x00c8, 0x0018, 0x0043, 0x001a, 0x0001, 0x0012,
               0x001f, 0x00c2, 0x0052, 0x0093, 0x0097, 0x0085, 0x0048, 0x0008,
               0x0010, 0x0050, 0x0093, 0x009b, 0x00df, 0x0089, 0x0084, 0x0013,
               0x00c5, 0x00d6, 0x000a, 0x00d9, 0x0047, 0x00d6, 0x0056, 0x004c},
    /*FP8E5*/ {0x002d, 0x001e, 0x00a8, 0x0038, 0x0033, 0x002a, 0x0034, 0x0022,
               0x000a, 0x0082, 0x0019, 0x0039, 0x0083, 0x0026, 0x0034, 0x0021,
               0x0035, 0x008b, 0x0027, 0x00a6, 0x00bb, 0x0080, 0x0039, 0x0084,
               0x0039, 0x0014, 0x001f, 0x0083, 0x0089, 0x003f, 0x0011, 0x008f}};

static uint16_t in_table_tr_int16[9][32] = {
    /*INT4*/ {0x0004, 0x0004, 0x000f, 0x0004, 0x0000, 0x000a, 0x0008, 0x0007,
              0x000b, 0x0008, 0x0004, 0x0001, 0x000e, 0x000e, 0x000d, 0x0003,
              0x000d, 0x0003, 0x000e, 0x0001, 0x0000, 0x0007, 0x0009, 0x0009,
              0x0007, 0x000c, 0x000d, 0x0000, 0x000e, 0x0002, 0x000b, 0x0004},
    /*INT8*/ {0x00aa, 0x00f4, 0x00e1, 0x006e, 0x0076, 0x001b, 0x0078, 0x00c6,
              0x0038, 0x00a1, 0x00ce, 0x0059, 0x005e, 0x00d1, 0x0017, 0x0064,
              0x00b7, 0x001f, 0x00f9, 0x0077, 0x00c1, 0x009d, 0x00cb, 0x008b,
              0x0015, 0x0053, 0x0087, 0x003d, 0x0040, 0x0087, 0x0067, 0x00cc},
    /*INT12*/ {0x789c, 0x4c70, 0x8626, 0x7345, 0xe400, 0xffc4, 0x26d2, 0x257f,
               0xa417, 0xd7ec, 0xc6eb, 0x2e28, 0x7689, 0x99be, 0xf6cc, 0xb428,
               0x0136, 0xd2ca, 0xa3e3, 0x58e6, 0x52dc, 0xbf76, 0xe471, 0xde72,
               0xb2b8, 0x8eb9, 0xd896, 0x1e82, 0x2192, 0xb06f, 0x0089, 0x676d},
    /*INT16*/ {0x078e, 0x0f52, 0x05bc, 0x0f16, 0x0549, 0x0ad1, 0x02eb, 0x0010,
               0x0fad, 0x03c1, 0x0d5f, 0x0e63, 0x0394, 0x0518, 0x0667, 0x0aba,
               0x0936, 0x0270, 0x074e, 0x0640, 0x0162, 0x0920, 0x0fd6, 0x0258,
               0x02c7, 0x01de, 0x093c, 0x0550, 0x0f09, 0x00a1, 0x09c0, 0x00dd},
    /*FP16*/ {0xe4bb, 0x414c, 0xe103, 0xc820, 0x58a6, 0xd99d, 0x502b, 0xbfc2,
              0x555c, 0x5164, 0x40c3, 0xd85f, 0xc540, 0xa85d, 0xa938, 0xc081,
              0x57da, 0x5a8e, 0x572d, 0x4a96, 0xd9b6, 0x37ab, 0xda0b, 0xd954,
              0xaac8, 0x2aca, 0xcbaf, 0x4491, 0x4cd5, 0x3eb8, 0xe7e2, 0x50a1},
    /*BF16*/ {0x422e, 0xbdee, 0xc0ce, 0x4254, 0xbef5, 0xc386, 0xc3d4, 0x3f92,
              0xc396, 0xbe0d, 0xbe9c, 0xc00c, 0x4336, 0xbf57, 0x4111, 0x4366,
              0x3f68, 0x42e8, 0x41d7, 0x41a0, 0xbc54, 0xc3c9, 0xbda1, 0x3cfa,
              0xbe4a, 0x404e, 0x40f9, 0x43d8, 0xbeff, 0xbcfa, 0xbe02, 0xc0f4},
    /*BBF16*/ {0xbcf7, 0x3d95, 0x3d83, 0xbc60, 0x4239, 0xbde7, 0xbec0, 0xbfcc,
               0x3d84, 0x4391, 0x3ffb, 0xc07f, 0xbd94, 0x4041, 0xc161, 0xc1ad,
               0xc016, 0x3cb7, 0xc2b5, 0xc1dd, 0xc0f1, 0xc0f4, 0xc217, 0xbf8d,
               0x42e9, 0xbee9, 0xc0bf, 0xc1a1, 0x3c27, 0x3cdb, 0x3cd4, 0xbf72},
    /*FP8E4*/ {0x004d, 0x0053, 0x0089, 0x0059, 0x005f, 0x001e, 0x004b, 0x0047,
               0x0000, 0x00cf, 0x008e, 0x0093, 0x0098, 0x009e, 0x000a, 0x0042,
               0x0055, 0x0056, 0x00c4, 0x0095, 0x00d6, 0x0083, 0x0001, 0x001e,
               0x0093, 0x008b, 0x00c1, 0x0018, 0x001e, 0x0052, 0x000e, 0x009b},
    /*FP8E5*/ {0x0009, 0x00b2, 0x0028, 0x00b5, 0x00bd, 0x002a, 0x009c, 0x0093,
               0x0011, 0x009f, 0x0099, 0x0002, 0x002a, 0x0084, 0x0039, 0x0094,
               0x0019, 0x0023, 0x0020, 0x0029, 0x00aa, 0x003b, 0x009a, 0x003c,
               0x003c, 0x0021, 0x0004, 0x00a2, 0x0036, 0x0085, 0x0014, 0x0039}};

static uint16_t in_table_tr_int8[9][32] = {
    /*INT4*/ {0x0009, 0x000a, 0x000d, 0x0003, 0x000e, 0x000e, 0x0002, 0x0009,
              0x000e, 0x000a, 0x0000, 0x0004, 0x000d, 0x000d, 0x000e, 0x0007,
              0x000f, 0x0009, 0x0001, 0x000f, 0x0007, 0x0008, 0x000a, 0x0003,
              0x0004, 0x0000, 0x0008, 0x000c, 0x000b, 0x0003, 0x0006, 0x0007},
    /*INT8*/ {0x0032, 0x00d0, 0x0049, 0x00f6, 0x0002, 0x003d, 0x00a0, 0x0075,
              0x008a, 0x0011, 0x0095, 0x00f6, 0x0067, 0x0086, 0x001c, 0x0055,
              0x00e3, 0x002f, 0x0013, 0x00f1, 0x00dc, 0x0093, 0x00b6, 0x00da,
              0x00af, 0x00eb, 0x0051, 0x0091, 0x0001, 0x00de, 0x003e, 0x009a},
    /*INT12*/ {0xb0ef, 0xa0d4, 0xe0f9, 0x06b9, 0x5893, 0xd998, 0xa60f, 0xee17,
               0xf412, 0x1e24, 0x6d54, 0x57e0, 0x28c1, 0xb6b0, 0xce8f, 0x690f,
               0x2e53, 0x6721, 0x70ed, 0xaa07, 0xda55, 0xc835, 0xd7c6, 0x3376,
               0xb549, 0x034c, 0x1d1d, 0x0d3b, 0x2349, 0x98c1, 0xdb41, 0x153a},
    /*INT16*/ {0x0a6b, 0x087e, 0x0839, 0x0e4f, 0x06cd, 0x0d0f, 0x02cb, 0x02c1,
               0x0dc3, 0x0b1b, 0x07fd, 0x0d72, 0x00ff, 0x0f25, 0x0f13, 0x08d4,
               0x0271, 0x0df2, 0x0ed4, 0x02cd, 0x06c0, 0x0566, 0x0f1c, 0x0e53,
               0x02f0, 0x0275, 0x04f3, 0x0ca2, 0x0449, 0x0389, 0x0077, 0x024c},
    /*FP16*/ {0xdd8a, 0xb9e1, 0x5915, 0xcca0, 0x3d3b, 0xafe4, 0xa95d, 0x2815,
              0x5294, 0xd4db, 0xb2af, 0xb275, 0xc682, 0x2cef, 0x3e08, 0xcbc9,
              0x46bd, 0xbea5, 0x3d77, 0xc50a, 0x346a, 0x5c7c, 0xe54a, 0xa9ea,
              0x3e88, 0xd8b0, 0x3c59, 0x2a64, 0x29da, 0x658c, 0xd3f4, 0xb518},
    /*BF16*/ {0x4050, 0x4111, 0x3eee, 0xbe40, 0x3cd0, 0x4235, 0x3e4b, 0xbe1b,
              0x4093, 0x4109, 0x41d2, 0xc099, 0xc007, 0x416d, 0xc106, 0xbf28,
              0xc34f, 0x3d6c, 0x430c, 0x43f3, 0xc2e8, 0xbf2f, 0xbe02, 0xc04d,
              0x3f5b, 0xc17a, 0x408d, 0xbeae, 0x3db8, 0x3c53, 0x3f52, 0x40ba},
    /*BBF16*/ {0x3c02, 0xbeed, 0xc359, 0xc1c8, 0xc06a, 0x4086, 0x3dc8, 0xbc42,
               0xc3dd, 0xbdd8, 0xbdb4, 0xc281, 0x3eca, 0x4011, 0x41d6, 0xbc2f,
               0x420a, 0xbec2, 0x3ce0, 0x3cbe, 0x3f12, 0xc282, 0xc3d7, 0xc350,
               0xc1b3, 0x42be, 0xc2c7, 0x3cdf, 0x3eca, 0xbde2, 0x41b5, 0xbc45},
    /*FP8E4*/ {0x009a, 0x0081, 0x0096, 0x00dd, 0x0006, 0x000e, 0x005c, 0x001b,
               0x0055, 0x000d, 0x00c5, 0x009c, 0x004d, 0x00d2, 0x009d, 0x004d,
               0x0004, 0x0018, 0x0011, 0x0082, 0x001b, 0x0054, 0x00cc, 0x00d7,
               0x0085, 0x008d, 0x00c8, 0x0085, 0x0050, 0x00d6, 0x0010, 0x0013},
    /*FP8E5*/ {0x0024, 0x00be, 0x0012, 0x0032, 0x00a4, 0x00a5, 0x00ba, 0x008a,
               0x0007, 0x00a5, 0x00a0, 0x0010, 0x008c, 0x00a1, 0x0086, 0x0028,
               0x0082, 0x00ae, 0x003e, 0x002d, 0x0011, 0x0095, 0x009a, 0x0036,
               0x000c, 0x001e, 0x0096, 0x00bd, 0x00ac, 0x0094, 0x00b3, 0x003a}};

static uint16_t in_table_tr_int4[9][32] = {
    /*INT4*/ {0x0001, 0x0002, 0x000a, 0x0006, 0x0005, 0x000c, 0x0005, 0x000f,
              0x0001, 0x000a, 0x0004, 0x000d, 0x000a, 0x0002, 0x0007, 0x0008,
              0x0003, 0x0004, 0x0003, 0x000e, 0x0007, 0x000f, 0x0006, 0x000d,
              0x000b, 0x000f, 0x0002, 0x000a, 0x000a, 0x0002, 0x0001, 0x000e},
    /*INT8*/ {0x0020, 0x0085, 0x00c4, 0x0015, 0x0040, 0x00ec, 0x003b, 0x0016,
              0x009e, 0x00f9, 0x003b, 0x001a, 0x00c8, 0x0009, 0x00ec, 0x0099,
              0x005c, 0x006f, 0x0003, 0x0032, 0x00f1, 0x0084, 0x002b, 0x00a5,
              0x0096, 0x0040, 0x00a2, 0x00a2, 0x00c3, 0x00dd, 0x004e, 0x000c},
    /*INT12*/ {0x0d17, 0x0851, 0x09c7, 0x0258, 0x01b0, 0x0a17, 0x0506, 0x057e,
               0x04b1, 0x0448, 0x0629, 0x0fc6, 0x0e5c, 0x0c3f, 0x068f, 0x0e39,
               0x008d, 0x0bdd, 0x0520, 0x0850, 0x0829, 0x0e80, 0x00ce, 0x0ef1,
               0x03a6, 0x0218, 0x0a1c, 0x0040, 0x0262, 0x0e6a, 0x08cd, 0x08c7},
    /*INT16*/ {0x00af, 0x0257, 0x0cbe, 0x0d26, 0x02b8, 0x0a17, 0x0c1a, 0x0558,
               0x0cd4, 0x0fa8, 0x0a2e, 0x0219, 0x08e6, 0x0fc9, 0x0f0c, 0x0452,
               0x0fbd, 0x0bed, 0x0976, 0x0d88, 0x059a, 0x0565, 0x0f66, 0x02fd,
               0x016f, 0x0cfd, 0x02e5, 0x0963, 0x099c, 0x07f7, 0x0971, 0x0df4},
    /*FP16*/ {0x43e2, 0x4d52, 0xc0d2, 0x6675, 0xb5b4, 0xb2d5, 0x4a04, 0x514a,
              0x2c5f, 0x387b, 0x3dfc, 0x4695, 0xd2c0, 0x5107, 0x4dbf, 0x622f,
              0x671c, 0xc3c6, 0xd205, 0xccba, 0x2de0, 0x35ef, 0x395c, 0xd9e9,
              0x547e, 0x5b37, 0xb01f, 0xca85, 0xb77a, 0x3f16, 0x468f, 0xd21c},
    /*BF16*/ {0xbc40, 0xbf5a, 0x4050, 0x4327, 0xc303, 0x428c, 0x3e51, 0xbc32,
              0xbf2d, 0xc168, 0x4035, 0xbf82, 0xc390, 0x3f3d, 0x41de, 0xc362,
              0xc15c, 0x3fae, 0xc086, 0x42d6, 0xc132, 0x3dfb, 0xc21d, 0xbdc9,
              0xc2b8, 0xc273, 0x4020, 0x40ac, 0x3d50, 0x3e22, 0xc253, 0x3c56},
    /*BBF16*/ {0x3e15, 0xc1c0, 0xc25c, 0x408b, 0xbdd5, 0x411f, 0x3ccd, 0xbcad,
               0xc2ef, 0x3ca1, 0x41c5, 0x4028, 0xbc86, 0xc00f, 0x4026, 0xc3df,
               0x3f39, 0x4033, 0xbf88, 0x3c19, 0xc189, 0xc09e, 0xc1b2, 0x4306,
               0x3ebd, 0x4081, 0x3dcb, 0xc235, 0xc1bb, 0xc1bc, 0x4244, 0x42b6},
    /*FP8E4*/ {0x008a, 0x00c7, 0x00c7, 0x00da, 0x0082, 0x00ce, 0x0003, 0x0042,
               0x0058, 0x00cf, 0x001b, 0x0017, 0x008c, 0x00de, 0x00d2, 0x0009,
               0x0097, 0x0098, 0x0096, 0x000f, 0x0006, 0x00d1, 0x0098, 0x00c1,
               0x0097, 0x00c1, 0x009c, 0x0092, 0x0017, 0x0046, 0x0009, 0x004f},
    /*FP8E5*/ {0x0099, 0x0006, 0x0002, 0x0080, 0x0090, 0x002d, 0x00a5, 0x00ba,
               0x00a2, 0x009b, 0x00ab, 0x0003, 0x00a2, 0x00a1, 0x0023, 0x001a,
               0x0034, 0x00b0, 0x00b7, 0x0005, 0x0013, 0x0023, 0x0093, 0x00b1,
               0x0005, 0x0013, 0x0088, 0x0032, 0x00ab, 0x001d, 0x00a5, 0x0012}};
