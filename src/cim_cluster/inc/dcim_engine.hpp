#ifndef _DCIM_ENGINE_HPP_
#define _DCIM_ENGINE_HPP_

#include <cstddef>  // For size_t
#include <cstdint>
#include <vector>
#include "dcim_array.hpp"  // Include the base macro class
#define DCIM_ENGINE_N_MACROS 2
// Forward declaration of C types if needed, though dcim_array.hpp includes them
// extern "C" {
//     #include "dcim_com.h"
// }

class DcimEngine
{
  public:
    /**
     * @brief Constructor for the DcimEngine class.
     *
     * @param num_macros The number of DcimArray instances (macros) this engine will manage.
     */
    DcimEngine(size_t num_macros);

    /**
     * @brief Destructor.
     */
    ~DcimEngine();

    // --- Storage Management (Wrappers around DcimArray methods) ---

    /**
     * @brief Writes pre-aligned data to an entire page in a specific macro's storage.
     *
     * @param macro_idx The index of the target macro (0 to N-1).
     * @param page_idx The index of the page within the macro (0 to DCIM_NUM_PAGES - 1).
     * @param page_buffer Buffer containing the aligned page data.
     * @return true if write was successful, false otherwise.
     */
    bool writePage(size_t macro_idx, int page_idx, const uint8_t* page_buffer);

    /**
     * @brief Reads an entire page from a specific macro's storage.
     *
     * @param macro_idx The index of the target macro (0 to N-1).
     * @param page_idx The index of the page within the macro (0 to DCIM_NUM_PAGES - 1).
     * @param page_buffer Buffer to store the read page data.
     * @return true if read was successful, false otherwise.
     */
    bool readPage(size_t macro_idx, int page_idx, uint8_t* page_buffer);

    /**
     * @brief Writes pre-aligned data to a specific row on a specific page in a specific macro's
     * storage.
     *
     * @param macro_idx The index of the target macro (0 to N-1).
     * @param page_idx The index of the page within the macro (0 to DCIM_NUM_PAGES - 1).
     * @param row_idx The index of the row within the page (0 to DCIM_NUM_ROWS_PER_PAGE - 1).
     * @param row_buffer Buffer containing the aligned row data.
     * @return true if write was successful, false otherwise.
     */
    bool writeRow(size_t macro_idx, int page_idx, int row_idx, const uint8_t* row_buffer);

    /**
     * @brief Reads a specific row from a specific page in a specific macro's storage.
     *
     * @param macro_idx The index of the target macro (0 to N-1).
     * @param page_idx The index of the page within the macro (0 to DCIM_NUM_PAGES - 1).
     * @param row_idx The index of the row within the page (0 to DCIM_NUM_ROWS_PER_PAGE - 1).
     * @param row_buffer Buffer to store the read row data.
     * @return true if read was successful, false otherwise.
     */
    bool readRow(size_t macro_idx, int page_idx, int row_idx, uint8_t* row_buffer);

    // --- Computation ---

    /**
     * @brief Performs the combined vector-matrix multiplication using pre-aligned input vectors.
     *        The input is expected as a contiguous block containing N aligned vectors.
     *
     * @param aligned_input_block A contiguous block of memory containing N pre-aligned input
     * vectors. Total size: N * DCIM_NUM_ROWS_PER_PAGE * sizeof(uint16_t) bytes. (N * 33 *
     * sizeof(uint16_t))
     * @param in_type The data type of the original input vector (used by DcimArray::compute).
     * @param page_idx The index of the page to use in *each* macro's storage.
     * @param wt_type The data type of the weights stored on the pages.
     * @param result_vector Output buffer to store the 64 summed results (size 64 *
     * sizeof(uint32_t)).
     * @return true if computation was successful, false otherwise.
     */
    bool computeAlignedInput(const uint8_t* aligned_input_block,  // Size N * 33 * sizeof(uint16_t)
                             uint8_t in_type,
                             int page_idx,
                             uint8_t wt_type,
                             uint8_t* result_vector  // Size 64 * sizeof(uint32_t)
    );

    /**
     * @brief Performs alignment on a raw input vector and then calls computeAlignedInput.
     *        The raw input is expected as a contiguous block containing N raw vectors.
     *
     * @param raw_input_vector The raw input vector as a contiguous block.
     *                         Total size: N * 32 * sizeof(uint16_t) bytes.
     * @param in_type The data type of the input vector (using data_fmt enum).
     * @param page_idx The index of the page to use in *each* macro's storage.
     * @param wt_type The data type of the weights stored on the pages.
     * @param result_vector Output buffer to store the 64 summed results (size 64 *
     * sizeof(uint32_t)).
     * @return true if computation was successful, false otherwise.
     */
    bool computeRawInput(const uint8_t* raw_input_vector,  // Size N * 32 * sizeof(uint16_t)
                         uint8_t in_type,
                         int page_idx,
                         uint8_t wt_type,
                         uint8_t* result_vector  // Size 64 * sizeof(uint32_t)
    );

    /**
     * @brief Gets the number of macros managed by this engine.
     * @return The number of macros (N).
     */
    size_t getNumMacros() const;

  private:
    std::vector<DcimArray> macros;  // Container for the DcimArray instances

    // Helper to check if macro index is valid
    bool isValidMacroIdx(size_t macro_idx) const;

    // Constants for input sizes per macro
    static constexpr size_t RAW_INPUT_ELEMENTS_PER_MACRO = 32;
    static constexpr size_t ALIGNED_INPUT_ELEMENTS_PER_MACRO =
        DCIM_NUM_ROWS_PER_PAGE;  // Should be 33
    static constexpr size_t BYTES_PER_ELEMENT = sizeof(uint16_t);
};

#endif  // _DCIM_ENGINE_HPP_