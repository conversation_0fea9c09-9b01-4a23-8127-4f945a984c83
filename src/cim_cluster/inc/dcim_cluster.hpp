#ifndef _DCIM_CLUSTER_HPP_
#define _DCIM_CLUSTER_HPP_

#include <cstddef>  // For size_t
#include <cstdint>
#include <vector>
#include "dcim_engine.hpp"  // Includes DcimArray and DCIM_ENGINE_N_MACROS

// Include necessary C headers for types and potentially conversion functions
extern "C"
{
#include "dcim_com.h"
}

class DcimCluster
{
  public:
    // Enum to define operational modes
    enum class OperationMode
    {
        PATTERN1_OUTPUT_FUSION,             // All engines compute, results accumulated
        PATTERN2_INPUT_DIST_OUTPUT_FUSION,  // Paired input dist, paired output accumulation
        PATTERN3_INPUT_DIST                 // Broadcast input, concatenated output
    };

    /**
     * @brief Constructor. Initializes the cluster with 4 DcimEngine instances.
     *        Defaults to PATTERN1_OUTPUT_FUSION mode.
     */
    DcimCluster();

    /**
     * @brief Destructor.
     */
    ~DcimCluster();

    // Disable copy constructor and assignment operator
    Dcim<PERSON><PERSON>(const DcimCluster&) = delete;
    DcimCluster& operator=(const DcimCluster&) = delete;

    /**
     * @brief Sets the operational mode for subsequent compute calls.
     * @param mode The desired OperationMode.
     */
    void setOperationMode(OperationMode mode);

    /**
     * @brief Gets the currently configured operational mode.
     * @return The current OperationMode.
     */
    OperationMode getOperationMode() const;

    // --- Storage Management (Pass-through to specific engine) ---

    /**
     * @brief Writes pre-aligned data to an entire page in a specific macro's storage within an
     * engine.
     * @param engine_idx The index of the target engine (0 to 3).
     * @param macro_idx The index of the target macro within the engine (0 to MACROS_PER_ENGINE -
     * 1).
     * @param page_idx The index of the page within the macro (0 to DCIM_NUM_PAGES - 1).
     * @param page_buffer Buffer containing the aligned page data.
     * @return true if write was successful, false otherwise.
     */
    bool writePage(size_t engine_idx, size_t macro_idx, int page_idx, const uint8_t* page_buffer);

    /**
     * @brief Reads an entire page from a specific macro's storage within an engine.
     * @param engine_idx The index of the target engine (0 to 3).
     * @param macro_idx The index of the target macro within the engine (0 to MACROS_PER_ENGINE -
     * 1).
     * @param page_idx The index of the page within the macro (0 to DCIM_NUM_PAGES - 1).
     * @param page_buffer Buffer to store the read page data.
     * @return true if read was successful, false otherwise.
     */
    bool readPage(size_t engine_idx, size_t macro_idx, int page_idx, uint8_t* page_buffer);

    /**
     * @brief Writes pre-aligned data to a specific row on a specific page in a specific macro's
     * storage within an engine.
     * @param engine_idx The index of the target engine (0 to 3).
     * @param macro_idx The index of the target macro within the engine (0 to MACROS_PER_ENGINE -
     * 1).
     * @param page_idx The index of the page within the macro (0 to DCIM_NUM_PAGES - 1).
     * @param row_idx The index of the row within the page (0 to DCIM_NUM_ROWS_PER_PAGE - 1).
     * @param row_buffer Buffer containing the aligned row data.
     * @return true if write was successful, false otherwise.
     */
    bool writeRow(size_t engine_idx,
                  size_t macro_idx,
                  int page_idx,
                  int row_idx,
                  const uint8_t* row_buffer);
    /**
     * @brief Reads a specific row from a specific page in a specific macro's storage within an
     * engine.
     * @param engine_idx The index of the target engine (0 to 3).
     * @param macro_idx The index of the target macro within the engine (0 to MACROS_PER_ENGINE -
     * 1).
     * @param page_idx The index of the page within the macro (0 to DCIM_NUM_PAGES - 1).
     * @param row_idx The index of the row within the page (0 to DCIM_NUM_ROWS_PER_PAGE - 1).
     * @param row_buffer Buffer to store the read row data.
     * @return true if read was successful, false otherwise.
     */
    bool readRow(size_t engine_idx,
                 size_t macro_idx,
                 int page_idx,
                 int row_idx,
                 uint8_t* row_buffer);

    // --- Computation ---

    /**
     * @brief Performs cluster computation using pre-aligned input based on the configured
     * OperationMode.
     * @param aligned_input_block Contiguous block sized for 4 engines (4 * MACROS_PER_ENGINE *
     * ALIGNED_ELEMENTS_PER_MACRO * sizeof(uint16_t)).
     * @param in_type Data type of the original input vectors.
     * @param page_idx Page index used in each relevant engine's storage.
     * @param wt_type Weight data type used in each relevant engine's storage.
     * @param output_block Output buffer (must be size CIME_NUM * RESULTS_PER_ENGINE *
     * sizeof(uint32_t)).
     * @return true if successful, false otherwise.
     */
    bool computeAlignedInput(const uint8_t* aligned_input_block,
                             uint8_t in_type,
                             int page_idx,
                             uint8_t wt_type,
                             uint8_t* output_block);

    /**
     * @brief Performs cluster computation using raw input (aligns internally) based on the
     * configured OperationMode.
     * @param raw_input_block Contiguous block sized for 4 engines (4 * MACROS_PER_ENGINE *
     * RAW_ELEMENTS_PER_MACRO * sizeof(uint16_t)).
     * @param in_type Data type of the input vectors.
     * @param page_idx Page index used in each relevant engine's storage.
     * @param wt_type Weight data type used in each relevant engine's storage.
     * @param output_block Output buffer (must be size CIME_NUM * RESULTS_PER_ENGINE *
     * sizeof(uint32_t)).
     * @return true if successful, false otherwise.
     */
    bool computeRawInput(const uint8_t* raw_input_block,
                         uint8_t in_type,
                         int page_idx,
                         uint8_t wt_type,
                         uint8_t* output_block);

    // --- Linear Word Access (local_mem compatible layout) ---

    /**
     * @brief Reads a single 256-bit word using a linear offset based on local_mem layout.
     *        (Page-major within Row, Row-major within Macro, Macro-major within Engine,
     * Engine-major)
     * @param cluster_word_offset The linear word offset (0 to TOTAL_CLUSTER_WORDS - 1).
     * @param word_buffer Buffer to store the read word (size DCIM_NUM_BYTES_PER_ROW).
     * @return true if read was successful, false otherwise.
     */
    bool readWordLinear(uint64_t cluster_word_offset, uint8_t* word_buffer);

    /**
     * @brief Writes a single 256-bit word using a linear offset based on local_mem layout.
     *        (Page-major within Row, Row-major within Macro, Macro-major within Engine,
     * Engine-major)
     * @param cluster_word_offset The linear word offset (0 to TOTAL_CLUSTER_WORDS - 1).
     * @param word_buffer Buffer containing the word to write (size DCIM_NUM_BYTES_PER_ROW).
     * @return true if write was successful, false otherwise.
     */
    bool writeWordLinear(uint64_t cluster_word_offset, const uint8_t* word_buffer);
    // --- Constants ---
    // Made public for potential external use/validation if needed
    static constexpr size_t CIME_NUM = 4;
    static constexpr size_t MACROS_PER_ENGINE = DCIM_ENGINE_N_MACROS;  // Should be 2
    static constexpr size_t RESULTS_PER_ENGINE = 64;
    static constexpr size_t RAW_ELEMENTS_PER_MACRO = 32;
    static constexpr size_t ALIGNED_ELEMENTS_PER_MACRO = DCIM_NUM_ROWS_PER_PAGE;  // Should be 33
    static constexpr size_t BYTES_PER_ELEMENT = sizeof(uint16_t);
    static constexpr size_t BYTES_PER_RESULT = sizeof(uint32_t);

    // Calculated sizes
    static constexpr size_t RAW_INPUT_BYTES_PER_ENGINE =
        MACROS_PER_ENGINE * RAW_ELEMENTS_PER_MACRO * BYTES_PER_ELEMENT;
    static constexpr size_t ALIGNED_INPUT_BYTES_PER_ENGINE =
        MACROS_PER_ENGINE * ALIGNED_ELEMENTS_PER_MACRO * BYTES_PER_ELEMENT;
    static constexpr size_t TOTAL_RAW_INPUT_BYTES = CIME_NUM * RAW_INPUT_BYTES_PER_ENGINE;
    static constexpr size_t TOTAL_ALIGNED_INPUT_BYTES = CIME_NUM * ALIGNED_INPUT_BYTES_PER_ENGINE;
    static constexpr size_t RESULTS_BYTES_PER_ENGINE = RESULTS_PER_ENGINE * BYTES_PER_RESULT;
    static constexpr size_t TOTAL_OUTPUT_BYTES = CIME_NUM * RESULTS_BYTES_PER_ENGINE;

  private:
    std::vector<DcimEngine> engines;
    OperationMode current_mode;

    // Helper for calculating input offsets within the full block
    const uint8_t* getRawInputPtrForEngine(const uint8_t* block_start, size_t engine_idx) const;
    const uint8_t* getAlignedInputPtrForEngine(const uint8_t* block_start, size_t engine_idx) const;

    // Constants defining the linear layout compatible with local_mem
    // Assuming DCIM_NUM_ROWS_PER_PAGE includes the exponent row (i.e., 33)
    // Assuming DCIM_NUM_PAGES is 16
    static constexpr uint32_t CLUSTER_TOTAL_MACROS = CIME_NUM * MACROS_PER_ENGINE;  // 4 * 2 = 8
    static constexpr uint32_t CLUSTER_ROWS_PER_MACRO = DCIM_NUM_ROWS_PER_PAGE;      // 33
    static constexpr uint32_t CLUSTER_PAGES_PER_MACRO_ROW = DCIM_NUM_PAGES;         // 16
    static constexpr uint32_t CLUSTER_WORDS_PER_MACRO =
        CLUSTER_ROWS_PER_MACRO * CLUSTER_PAGES_PER_MACRO_ROW;  // 33 * 16 = 528
    static constexpr uint32_t CLUSTER_WORDS_PER_ENGINE =
        CLUSTER_WORDS_PER_MACRO * MACROS_PER_ENGINE;  // 528 * 2 = 1056
    static constexpr uint64_t TOTAL_CLUSTER_WORDS =
        CLUSTER_TOTAL_MACROS * CLUSTER_WORDS_PER_MACRO;  // 8 * 528 = 4224
    // Helpers for output aggregation
    // Takes a flat vector containing results from all engines [R0, R1, R2, R3]
    void accumulateResults(const std::vector<uint32_t>& all_engine_results,
                           uint32_t* final_output,
                           size_t start_engine,
                           size_t num_engines_to_sum,
                           size_t output_offset_elements);
    void concatenateResults(const std::vector<uint32_t>& all_engine_results,
                            uint32_t* final_output);

    // Internal compute logic shared by computeRawInput and computeAlignedInput
    bool computeInternal(const uint8_t* aligned_input_block,  // Always receives aligned input block
                         uint8_t in_type,
                         int page_idx,
                         uint8_t wt_type,
                         uint8_t* output_block  // Final output buffer
    );

    // Helper to check engine index validity
    // Helper to parse linear offset (local_mem layout) into physical indices
    bool parseClusterWordOffset(uint64_t cluster_word_offset,
                                uint32_t& engine_idx,
                                uint32_t& macro_idx_in_engine,
                                uint32_t& row_idx,
                                uint32_t& page_idx) const;
    bool isValidEngineIdx(size_t engine_idx) const;
};

#endif  // _DCIM_CLUSTER_HPP_