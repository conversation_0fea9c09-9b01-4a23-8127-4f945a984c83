#ifndef _DCIM_COMPUTE_H_
#define _DCIM_COMPUTE_H_

#include <stdint.h>
#include "dcim_com.h"      // For data_fmt enum
#include "dcim_storage.h"  // For dimension constants

#ifdef __cplusplus
extern "C"
{
#endif

    /**
     * @brief Performs the full vector-matrix multiplication using data from a specific
     *        page in the DCIM storage.
     *
     * Assumes the storage page contains pre-aligned weight data and the shared exponent.
     * Assumes the input vector provided is also pre-aligned.
     *
     * Calculation: result[j] = dot_product(aligned_input_vector[0..31],
     * aligned_weight_column[j][0..31]) where alignment uses shared exponents from
     * aligned_input_vector[32] and storage[page_idx][32].
     *
     * @param aligned_input_vector The pre-aligned input vector (size 33, including shared
     * exponent).
     * @param in_type The data type of the input vector (using data_fmt enum).
     * @param page_idx The index of the page in storage containing the aligned weight matrix (0 to
     * DCIM_NUM_PAGES - 1).
     * @param wt_type The data type of the weights stored on the page (using data_fmt enum).
     * @param result_vector Output buffer to store the 64 results of the multiplication (size 64).
     *                      Each result is uint32_t (typically FP32 format from dcim_macro_com).
     */
    void dcim_compute_from_storage_aligned_input(
        const uint8_t*
            aligned_input_vector,  // Input vector is pre-aligned (size 33*sizeof(uint16_t))
        uint8_t in_type,
        int page_idx,
        uint8_t wt_type,
        uint8_t* result_vector  // Output vector (size 64*sizeof(uint32_t))
    );

    // --- Optional Helper Function (If needed for external raw input) ---
    /**
     * @brief Performs alignment on a raw input vector and then calls
     *        dcim_compute_from_storage_aligned_input.
     *
     * @param raw_input_vector The raw input vector (size 32).
     * @param in_type The data type of the input vector (using data_fmt enum).
     * @param page_idx The index of the page in storage containing the aligned weight matrix.
     * @param wt_type The data type of the weights stored on the page.
     * @param result_vector Output buffer to store the 64 results.
     */
    void dcim_compute_from_storage_raw_input(
        const uint8_t* raw_input_vector,  // Size 32*sizeof(uint16_t)
        uint8_t in_type,
        int page_idx,
        uint8_t wt_type,
        uint8_t* result_vector  // Size 64*sizeof(uint32_t)
    );

#ifdef __cplusplus
}
#endif

#endif  // _DCIM_COMPUTE_H_
