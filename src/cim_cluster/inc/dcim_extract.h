#ifndef _DCIM_EXTRACT_H_
#define _DCIM_EXTRACT_H_

#include <stdint.h>
#include "dcim_com.h"      // For data_fmt enum
#include "dcim_storage.h"  // For dimension constants

#ifdef __cplusplus
extern "C"
{
#endif

    /**
     * @brief Extracts the aligned data for a single logical weight column from the
     *        physical storage rows (which hold pre-aligned data).
     *
     * @param aligned_weight_page_rows A pointer to the first 32 rows of the page buffer
     *                                 containing the aligned weight data. Expected dimensions:
     *                                 [32][DCIM_NUM_BYTES_PER_ROW].
     * @param wt_type The data type of the weights (determines bits per element and packing).
     *                Uses the data_fmt enum values (e.g., INT4, INT8, FP16, BBF16).
     * @param column_idx The logical column index to extract (0 to 63).
     * @param extracted_aligned_col Output buffer to store the 33 extracted aligned
     *                              weight elements (32 data + 1 exponent) for the
     *                              specified column. Expected size: [33].
     */
    void extract_aligned_weight_column(
        const uint8_t (*aligned_weight_page_rows)[DCIM_NUM_BYTES_PER_ROW],  // Pass full page data
        uint8_t wt_type,
        int column_idx,
        uint16_t* extracted_aligned_col  // Output size 33
    );

#ifdef __cplusplus
}
#endif

#endif  // _DCIM_EXTRACT_H_
