#ifndef _DCIM_STORAGE_H_
#define _DCIM_STORAGE_H_

#include <stdint.h>

// Define storage dimensions
#define DCIM_NUM_PAGES 16
#define DCIM_NUM_ROWS_PER_PAGE 33  // 32 data rows + 1 exponent row
#define DCIM_NUM_BYTES_PER_ROW 32  // 32 bytes * 8 bits/byte = 256 bits

// Declare the main storage array (definition will be in dcim_storage.c)
// This array holds pre-aligned data.
extern uint8_t dcim_storage[DCIM_NUM_PAGES][DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW];

#ifdef __cplusplus
extern "C"
{
#endif

    /**
     * @brief Reads a specific row from a specific page in the DCIM storage.
     *
     * @param page_idx The index of the page to read from (0 to DCIM_NUM_PAGES - 1).
     * @param row_idx The index of the row within the page (0 to DCIM_NUM_ROWS_PER_PAGE - 1).
     * @param buffer A buffer to store the read row data (must be size DCIM_NUM_BYTES_PER_ROW).
     */
    void read_storage_row(int page_idx, int row_idx, uint8_t* buffer);

    /**
     * @brief Writes data to a specific row on a specific page in the DCIM storage.
     *
     * @param page_idx The index of the page to write to (0 to DCIM_NUM_PAGES - 1).
     * @param row_idx The index of the row within the page (0 to DCIM_NUM_ROWS_PER_PAGE - 1).
     * @param buffer A buffer containing the data to write (must be size DCIM_NUM_BYTES_PER_ROW).
     */
    void write_storage_row(int page_idx, int row_idx, const uint8_t* buffer);

    /**
     * @brief Reads an entire page from the DCIM storage.
     *
     * @param page_idx The index of the page to read (0 to DCIM_NUM_PAGES - 1).
     * @param page_buffer A buffer to store the read page data (must be size
     * [DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW]).
     */
    void read_storage_page(int page_idx, uint8_t* page_buffer);

    /**
     * @brief Writes data to an entire page in the DCIM storage.
     *
     * @param page_idx The index of the page to write to (0 to DCIM_NUM_PAGES - 1).
     * @param page_buffer A buffer containing the page data to write (must be size
     * [DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW]).
     */
    void write_storage_page(int page_idx, const uint8_t* page_buffer);

#ifdef __cplusplus
}
#endif

#endif  // _DCIM_STORAGE_H_
