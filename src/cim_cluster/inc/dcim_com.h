#ifndef _DCIM_COM_H_
#define _DCIM_COM_H_

#include "stdint.h"
#include "stdio.h"
#include "stdlib.h"

#ifdef __cplusplus
extern "C"
{
#endif

    //      0   1     2     3     4   5     6   7     8     9
    static enum { INT4, INT8, INT12, INT16, FP32, FP16, BF16, BBF16, FP8E4, FP8E5 } data_fmt;

    extern uint32_t f2mh_conv(float fdata, uint8_t data_typ);
    extern void mh2f_conv(uint32_t mhex, uint8_t data_typ, float* flt_out);
    extern uint32_t bit_acc(uint8_t sta_bit, uint8_t stp_bit, uint32_t dst_data);

    extern void data_align_f16b(uint16_t* data_hex, uint8_t data_type, uint16_t* data_algn);
    extern void data_align_bbf16b(uint16_t* data_hex, uint8_t data_type, uint16_t* data_algn);
    extern void data_align_f8b(uint16_t* data_hex, uint8_t data_type, uint16_t* data_algn);
    extern void float_data_align(uint16_t* data_hex, uint8_t data_type, uint16_t* data_algn);

    extern uint32_t dcim_macro(uint16_t* wt_algn,
                               uint8_t wt_dtpy,
                               uint16_t* in_algn,
                               uint8_t in_dtpy);
    extern uint32_t dcim_macro_com(uint16_t* wt_algn,
                                   uint8_t wt_dtpy,
                                   uint16_t* in_algn,
                                   uint8_t in_dtpy);

#ifdef __cplusplus
}
#endif

#endif  // _DCIM_COM_H_
