# DCIM Engine 架构文档

## 1. 说明

### 1.1 设计目标

-   **扩展计算能力**: 在 `DcimArray` (单个计算宏单元) 的基础上，提供管理和协调 **N** 个宏单元 (`DCIM_ENGINE_N_MACROS`) 的能力，以处理更大规模的向量-矩阵运算。
-   **接口一致性**: 提供与 `DcimArray` 类似的上层 C++ 接口，便于应用层集成和使用。
-   **重用与封装**: 最大程度地重用 `DcimArray` 的功能，将多宏单元的协调逻辑封装在 `DcimEngine` 内部。

### 1.2 核心特性

-   **多宏单元管理**: 内部包含并管理 `N` 个 `DcimArray` 实例。
-   **统一存储访问**: 提供按宏单元索引访问其内部页/行存储的接口。
-   **组合计算**: 实现将输入向量分发给各宏单元，并行（逻辑上）执行计算，并将结果汇总（求和）的逻辑。
-   **灵活配置**: 通过构造函数参数确定管理的宏单元数量 `N`。
-   **继承数据格式支持**: 继承 `DcimArray` 对多种数据格式的支持。

## 2. 系统架构

`DcimEngine` 作为 `DcimArray` 的上一层封装，其在系统中的位置如下：

```mermaid
graph TD
    A[应用层] --> Eng[DCIM C++接口 - DcimEngine 类];

    subgraph DcimEngine 内部
        Eng --> M0["macros[0]: DcimArray"];
        Eng --> M1["macros[1]: DcimArray"];
        Eng --> MN["... macros[N-1]: DcimArray"];
        Eng --> ComputeLogic[Engine 计算协调逻辑];
        Eng --> StorageAccess[Engine 存储访问接口];
    end

    StorageAccess --> M0;
    StorageAccess --> M1;
    StorageAccess --> MN;

    ComputeLogic --> M0;
    ComputeLogic --> M1;
    ComputeLogic --> MN;

    subgraph "DcimArray (宏单元)"
        M0 --> Core["(DcimArray 核心功能)"];
        M1 --> Core;
        MN --> Core;
        Core --> D[计算子系统];
        Core --> E[存储子系统];
        Core --> F[数据操作子系统];
    end

    style Eng fill:#aae,stroke:#333,stroke-width:2px
    style M0 fill:#dfd,stroke:#333,stroke-width:1px
    style M1 fill:#dfd,stroke:#333,stroke-width:1px
    style MN fill:#dfd,stroke:#333,stroke-width:1px
```

-   **应用层**: 与 `DcimEngine` 交互，进行数据写入和发起计算请求。
-   **DcimEngine**:
    -   包含一个 `std::vector<DcimArray>` 成员 (`macros`) 来存储 `N` 个宏单元实例。
    -   提供存储访问接口 (`writePage`, `readPage`, `writeRow`, `readRow`)，这些接口需要指定目标宏单元的索引 (`macro_idx`)。
    -   提供计算接口 (`computeRawInput`, `computeAlignedInput`)，负责处理输入分发、调用底层宏单元计算、以及结果汇总。
-   **DcimArray (宏单元)**: 每个实例负责其独立的存储空间和基础的向量-矩阵计算逻辑（如 `DcimArray::compute`）。

## 3. 数据流

`DcimEngine` 的计算数据流如下：

```mermaid
flowchart TD
    subgraph "输入处理阶段 (Engine)"
        RawInput[["原始输入向量块 N * 32 * uint16_t"]]
        AlignStage[["输入对齐"]]
        AlignedInputBlock[["对齐输入向量块 N * 33 * uint16_t"]]
        Aligner[["对齐N个子向量"]]
        
        RawInput -->|computeRawInput| AlignStage
        AlignStage --> AlignedInputBlock
        RawInput --> computeRawInput
        AlignedInputBlock --> computeAlignedInput
        computeRawInput --> Aligner
        Aligner --> AlignedInputBlock
    end

    subgraph "计算分发与执行阶段 (Engine & Macros)"
        computeAlignedInput --> Splitter
        Splitter[["按宏单元拆分对齐输入块"]]
        AI0[["aligned_input_0"]]
        AI1[["aligned_input_1"]]
        AIN[["aligned_input_N-1"]]
        
        Splitter --> AI0
        Splitter --> AI1
        Splitter --> AIN

        C0[["macros[0].compute"]]
        C1[["macros[1].compute"]]
        CN[["macros[N-1].compute"]]
        
        P0[["macros[0].storage"]]
        P1[["macros[1].storage"]]
        PN[["macros[N-1].storage"]]
        
        AI0 --> C0
        P0 --> C0
        AI1 --> C1
        P1 --> C1
        AIN --> CN
        PN --> CN

        R0[["temp_result_0 64*uint32_t"]]
        R1[["temp_result_1 64*uint32_t"]]
        RN[["temp_result_N-1 64*uint32_t"]]
        
        C0 --> R0
        C1 --> R1
        CN --> RN
    end

    subgraph "结果汇总阶段 (Engine)"
        Summer[["结果累加器"]]
        FinalResult[["最终结果向量 64*uint32_t"]]
        
        R0 --> Summer
        R1 --> Summer
        RN --> Summer
        Summer --> FinalResult
    end

    style RawInput fill:#ffcccc,stroke:#333
    style AlignedInputBlock fill:#ccffcc,stroke:#333
    style FinalResult fill:#ffddaa,stroke:#333
    style P0 fill:#ccccff,stroke:#333
    style P1 fill:#ccccff,stroke:#333
    style PN fill:#ccccff,stroke:#333
```

1.  **输入处理**:
    -   `computeRawInput` 接收包含 `N` 个原始输入向量（每个32\*`uint16_t`）的连续内存块。
    -   内部调用 `N` 次 `DcimArray::alignInputVector`，将每个原始向量对齐，生成包含 `N` 个对齐向量（每个33\*`uint16_t`）的连续内存块。
    -   `computeAlignedInput` 直接接收包含 `N` 个对齐向量的连续内存块。
2.  **计算分发**:
    -   `computeAlignedInput` 将对齐输入块按宏单元拆分。
    -   将对应的对齐输入向量传递给每个 `DcimArray` 实例的 `compute` 方法，并指定相同的 `page_idx` 和 `wt_type`。
    -   每个 `DcimArray` 实例使用其内部存储页的数据和对应的输入向量独立计算，生成一个临时的 64\*`uint32_t` 结果向量。
3.  **结果汇总**:
    -   `computeAlignedInput` 将所有 `N` 个宏单元返回的临时结果向量按元素进行累加。
    -   最终生成一个 64\*`uint32_t` 的结果向量，作为 `DcimEngine` 的计算输出。

## 4. 数据格式支持

`DcimEngine` 本身不直接处理数据格式细节，它依赖于其包含的 `DcimArray` 实例。因此，`DcimEngine` 支持 `DcimArray` 支持的所有输入和权重数据格式（`INT4/8/12/16`, `FP8E4/E5`, `FP16`, `BF16`, `BBF16` 等）。数据类型 (`in_type`, `wt_type`) 会被透传给底层的 `DcimArray::compute` 函数。

## 5. 核心函数文档 (C++ 接口)

```cpp
class DcimEngine {
public:
    /**
     * @brief 构造函数
     * @param num_macros 要管理的 DcimArray 实例数量 (N)
     */
    DcimEngine(size_t num_macros);

    /**
     * @brief 析构函数
     */
    ~DcimEngine();

    // --- 存储管理 ---

    /**
     * @brief 向指定宏单元的指定页面写入预对齐数据
     * @param macro_idx 目标宏单元索引 (0 到 N-1)
     * @param page_idx 宏单元内的页面索引 (0 到 DCIM_NUM_PAGES - 1)
     * @param page_buffer 包含对齐页面数据的缓冲区
     * @return 操作是否成功
     */
    bool writePage(size_t macro_idx, int page_idx, const uint8_t *page_buffer);

    /**
     * @brief 从指定宏单元的指定页面读取数据
     * @param macro_idx 目标宏单元索引 (0 到 N-1)
     * @param page_idx 宏单元内的页面索引 (0 到 DCIM_NUM_PAGES - 1)
     * @param page_buffer 存储读取页面数据的缓冲区
     * @return 操作是否成功
     */
    bool readPage(size_t macro_idx, int page_idx, uint8_t *page_buffer);

    /**
     * @brief 向指定宏单元的指定页面的指定行写入预对齐数据
     * @param macro_idx 目标宏单元索引 (0 到 N-1)
     * @param page_idx 宏单元内的页面索引
     * @param row_idx 页面内的行索引 (0 到 DCIM_NUM_ROWS_PER_PAGE - 1)
     * @param row_buffer 包含对齐行数据的缓冲区
     * @return 操作是否成功
     */
    bool writeRow(size_t macro_idx, int page_idx, int row_idx, const uint8_t *row_buffer);

    /**
     * @brief 从指定宏单元的指定页面的指定行读取数据
     * @param macro_idx 目标宏单元索引 (0 到 N-1)
     * @param page_idx 宏单元内的页面索引
     * @param row_idx 页面内的行索引
     * @param row_buffer 存储读取行数据的缓冲区
     * @return 操作是否成功
     */
    bool readRow(size_t macro_idx, int page_idx, int row_idx, uint8_t *row_buffer);

    // --- 计算 ---

    /**
     * @brief 使用预对齐的输入向量块执行组合计算
     * @param aligned_input_block 包含 N 个对齐输入向量的连续内存块 (大小 N * 33 * sizeof(uint16_t))
     * @param in_type 原始输入向量的数据类型
     * @param page_idx 所有宏单元使用的页面索引
     * @param wt_type 所有宏单元使用的权重数据类型
     * @param result_vector 存储 64 个累加结果的输出缓冲区 (大小 64 * sizeof(uint32_t))
     * @return 计算是否成功
     */
    bool computeAlignedInput(
        const uint8_t *aligned_input_block,
        uint8_t in_type,
        int page_idx,
        uint8_t wt_type,
        uint8_t *result_vector
    );

   /**
     * @brief 使用原始输入向量块执行组合计算（内部进行对齐）
     * @param raw_input_vector 包含 N 个原始输入向量的连续内存块 (大小 N * 32 * sizeof(uint16_t))
     * @param in_type 输入向量的数据类型
     * @param page_idx 所有宏单元使用的页面索引
     * @param wt_type 所有宏单元使用的权重数据类型
     * @param result_vector 存储 64 个累加结果的输出缓冲区 (大小 64 * sizeof(uint32_t))
     * @return 计算是否成功
     */
    bool computeRawInput(
        const uint8_t *raw_input_vector,
        uint8_t in_type,
        int page_idx,
        uint8_t wt_type,
        uint8_t *result_vector
    );

    /**
     * @brief 获取引擎管理的宏单元数量 N
     * @return 宏单元数量
     */
    size_t getNumMacros() const;

private:
    std::vector<DcimArray> macros; // 存储 DcimArray 实例
    bool isValidMacroIdx(size_t macro_idx) const; // 内部辅助函数
    // ... 其他私有成员或常量 ...
};
```

## 6. 实现细节

### 6.1 宏单元管理

-   `DcimEngine` 在构造时根据传入的 `num_macros` 参数，创建并持有一个 `std::vector<DcimArray>`。
-   所有对特定宏单元存储的访问都需要提供 `macro_idx` 参数。

### 6.2 输入处理

-   `computeRawInput` 负责将输入的 `N * 32` 原始向量块，通过循环调用 `macros[i].alignInputVector`，转换为 `N * 33` 的对齐向量块。
-   `computeAlignedInput` 接收 `N * 33` 的对齐向量块。

### 6.3 计算与结果汇总

-   `computeAlignedInput` 遍历所有 `N` 个宏单元。
-   对于每个宏单元 `i`，它提取对应的对齐输入向量 `aligned_input_i`。
-   调用 `macros[i].compute(aligned_input_i, ...)` 来获取该宏单元的计算结果 `temp_result_i`。
-   将所有 `temp_result_i` (i 从 0 到 N-1) 按元素累加到最终的 `result_vector` 中。

## 7. 存储格式

`DcimEngine` 本身不定义新的存储格式。它管理的存储是 `N` 个独立的 `DcimArray` 存储区域。每个 `DcimArray` 实例内部遵循 `dcim.md` 中描述的页行优先 (Page-Row-Major) 格式。应用层需要通过 `macro_idx` 来区分访问的是哪个宏单元的存储。

## 8. 错误处理

-   `DcimEngine` 的存储访问函数和计算函数会进行基本的输入验证（如空指针检查、`macro_idx` 范围检查）。
-   大部分错误处理逻辑依赖于底层的 `DcimArray` 实例。如果 `macros[i].writePage/readPage/compute` 等函数返回 `false`，`DcimEngine` 的对应函数也会返回 `false`，并将错误信息（通常通过 `fprintf(stderr, ...)`）传递出来。
-   在计算过程中，如果任何一个宏单元的 `compute` 失败，`computeAlignedInput` 会将最终的 `result_vector` 清零并返回 `false`。
