<!DOCTYPE html><html><head>
      <title>dcim_engine</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:////home/<USER>/.cursor-server/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/katex/katex.min.css">
      
      
      <script type="text/javascript" src="file:////home/<USER>/.cursor-server/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/mermaid/mermaid.min.js" charset="UTF-8"></script>
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:Consolas,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<h1 id="dcim-engine-架构文档">DCIM Engine 架构文档 </h1>
<h2 id="1-说明">1. 说明 </h2>
<h3 id="11-设计目标">1.1 设计目标 </h3>
<ul>
<li><strong>扩展计算能力</strong>: 在 <code>DcimArray</code> (单个计算宏单元) 的基础上，提供管理和协调 <strong>N</strong> 个宏单元 (<code>DCIM_ENGINE_N_MACROS</code>) 的能力，以处理更大规模的向量-矩阵运算。</li>
<li><strong>接口一致性</strong>: 提供与 <code>DcimArray</code> 类似的上层 C++ 接口，便于应用层集成和使用。</li>
<li><strong>重用与封装</strong>: 最大程度地重用 <code>DcimArray</code> 的功能，将多宏单元的协调逻辑封装在 <code>DcimEngine</code> 内部。</li>
</ul>
<h3 id="12-核心特性">1.2 核心特性 </h3>
<ul>
<li><strong>多宏单元管理</strong>: 内部包含并管理 <code>N</code> 个 <code>DcimArray</code> 实例。</li>
<li><strong>统一存储访问</strong>: 提供按宏单元索引访问其内部页/行存储的接口。</li>
<li><strong>组合计算</strong>: 实现将输入向量分发给各宏单元，并行（逻辑上）执行计算，并将结果汇总（求和）的逻辑。</li>
<li><strong>灵活配置</strong>: 通过构造函数参数确定管理的宏单元数量 <code>N</code>。</li>
<li><strong>继承数据格式支持</strong>: 继承 <code>DcimArray</code> 对多种数据格式的支持。</li>
</ul>
<h2 id="2-系统架构">2. 系统架构 </h2>
<p><code>DcimEngine</code> 作为 <code>DcimArray</code> 的上一层封装，其在系统中的位置如下：</p>
<div class="mermaid">graph TD
    A[应用层] --&gt; Eng[DCIM C++接口 - DcimEngine 类];

    subgraph DcimEngine 内部
        Eng --&gt; M0["macros[0]: DcimArray"];
        Eng --&gt; M1["macros[1]: DcimArray"];
        Eng --&gt; MN["... macros[N-1]: DcimArray"];
        Eng --&gt; ComputeLogic[Engine 计算协调逻辑];
        Eng --&gt; StorageAccess[Engine 存储访问接口];
    end

    StorageAccess --&gt; M0;
    StorageAccess --&gt; M1;
    StorageAccess --&gt; MN;

    ComputeLogic --&gt; M0;
    ComputeLogic --&gt; M1;
    ComputeLogic --&gt; MN;

    subgraph "DcimArray (宏单元)"
        M0 --&gt; Core["(DcimArray 核心功能)"];
        M1 --&gt; Core;
        MN --&gt; Core;
        Core --&gt; D[计算子系统];
        Core --&gt; E[存储子系统];
        Core --&gt; F[数据操作子系统];
    end

    style Eng fill:#aae,stroke:#333,stroke-width:2px
    style M0 fill:#dfd,stroke:#333,stroke-width:1px
    style M1 fill:#dfd,stroke:#333,stroke-width:1px
    style MN fill:#dfd,stroke:#333,stroke-width:1px
</div><ul>
<li><strong>应用层</strong>: 与 <code>DcimEngine</code> 交互，进行数据写入和发起计算请求。</li>
<li><strong>DcimEngine</strong>:
<ul>
<li>包含一个 <code>std::vector&lt;DcimArray&gt;</code> 成员 (<code>macros</code>) 来存储 <code>N</code> 个宏单元实例。</li>
<li>提供存储访问接口 (<code>writePage</code>, <code>readPage</code>, <code>writeRow</code>, <code>readRow</code>)，这些接口需要指定目标宏单元的索引 (<code>macro_idx</code>)。</li>
<li>提供计算接口 (<code>computeRawInput</code>, <code>computeAlignedInput</code>)，负责处理输入分发、调用底层宏单元计算、以及结果汇总。</li>
</ul>
</li>
<li><strong>DcimArray (宏单元)</strong>: 每个实例负责其独立的存储空间和基础的向量-矩阵计算逻辑（如 <code>DcimArray::compute</code>）。</li>
</ul>
<h2 id="3-数据流">3. 数据流 </h2>
<p><code>DcimEngine</code> 的计算数据流如下：</p>
<div class="mermaid">flowchart TD
    subgraph "输入处理阶段 (Engine)"
        RawInput[["原始输入向量块 N * 32 * uint16_t"]]
        AlignStage[["输入对齐"]]
        AlignedInputBlock[["对齐输入向量块 N * 33 * uint16_t"]]
        Aligner[["对齐N个子向量"]]
        
        RawInput --&gt;|computeRawInput| AlignStage
        AlignStage --&gt; AlignedInputBlock
        RawInput --&gt; computeRawInput
        AlignedInputBlock --&gt; computeAlignedInput
        computeRawInput --&gt; Aligner
        Aligner --&gt; AlignedInputBlock
    end

    subgraph "计算分发与执行阶段 (Engine &amp; Macros)"
        computeAlignedInput --&gt; Splitter
        Splitter[["按宏单元拆分对齐输入块"]]
        AI0[["aligned_input_0"]]
        AI1[["aligned_input_1"]]
        AIN[["aligned_input_N-1"]]
        
        Splitter --&gt; AI0
        Splitter --&gt; AI1
        Splitter --&gt; AIN

        C0[["macros[0].compute"]]
        C1[["macros[1].compute"]]
        CN[["macros[N-1].compute"]]
        
        P0[["macros[0].storage"]]
        P1[["macros[1].storage"]]
        PN[["macros[N-1].storage"]]
        
        AI0 --&gt; C0
        P0 --&gt; C0
        AI1 --&gt; C1
        P1 --&gt; C1
        AIN --&gt; CN
        PN --&gt; CN

        R0[["temp_result_0 64*uint32_t"]]
        R1[["temp_result_1 64*uint32_t"]]
        RN[["temp_result_N-1 64*uint32_t"]]
        
        C0 --&gt; R0
        C1 --&gt; R1
        CN --&gt; RN
    end

    subgraph "结果汇总阶段 (Engine)"
        Summer[["结果累加器"]]
        FinalResult[["最终结果向量 64*uint32_t"]]
        
        R0 --&gt; Summer
        R1 --&gt; Summer
        RN --&gt; Summer
        Summer --&gt; FinalResult
    end

    style RawInput fill:#ffcccc,stroke:#333
    style AlignedInputBlock fill:#ccffcc,stroke:#333
    style FinalResult fill:#ffddaa,stroke:#333
    style P0 fill:#ccccff,stroke:#333
    style P1 fill:#ccccff,stroke:#333
    style PN fill:#ccccff,stroke:#333
</div><ol>
<li><strong>输入处理</strong>:
<ul>
<li><code>computeRawInput</code> 接收包含 <code>N</code> 个原始输入向量（每个32*<code>uint16_t</code>）的连续内存块。</li>
<li>内部调用 <code>N</code> 次 <code>DcimArray::alignInputVector</code>，将每个原始向量对齐，生成包含 <code>N</code> 个对齐向量（每个33*<code>uint16_t</code>）的连续内存块。</li>
<li><code>computeAlignedInput</code> 直接接收包含 <code>N</code> 个对齐向量的连续内存块。</li>
</ul>
</li>
<li><strong>计算分发</strong>:
<ul>
<li><code>computeAlignedInput</code> 将对齐输入块按宏单元拆分。</li>
<li>将对应的对齐输入向量传递给每个 <code>DcimArray</code> 实例的 <code>compute</code> 方法，并指定相同的 <code>page_idx</code> 和 <code>wt_type</code>。</li>
<li>每个 <code>DcimArray</code> 实例使用其内部存储页的数据和对应的输入向量独立计算，生成一个临时的 64*<code>uint32_t</code> 结果向量。</li>
</ul>
</li>
<li><strong>结果汇总</strong>:
<ul>
<li><code>computeAlignedInput</code> 将所有 <code>N</code> 个宏单元返回的临时结果向量按元素进行累加。</li>
<li>最终生成一个 64*<code>uint32_t</code> 的结果向量，作为 <code>DcimEngine</code> 的计算输出。</li>
</ul>
</li>
</ol>
<h2 id="4-数据格式支持">4. 数据格式支持 </h2>
<p><code>DcimEngine</code> 本身不直接处理数据格式细节，它依赖于其包含的 <code>DcimArray</code> 实例。因此，<code>DcimEngine</code> 支持 <code>DcimArray</code> 支持的所有输入和权重数据格式（<code>INT4/8/12/16</code>, <code>FP8E4/E5</code>, <code>FP16</code>, <code>BF16</code>, <code>BBF16</code> 等）。数据类型 (<code>in_type</code>, <code>wt_type</code>) 会被透传给底层的 <code>DcimArray::compute</code> 函数。</p>
<h2 id="5-核心函数文档-c-接口">5. 核心函数文档 (C++ 接口) </h2>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-class">class</span> <span class="token class-name">DcimEngine</span> <span class="token punctuation">{</span>
<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token comment">/**
     * @brief 构造函数
     * @param num_macros 要管理的 DcimArray 实例数量 (N)
     */</span>
    <span class="token function">DcimEngine</span><span class="token punctuation">(</span>size_t num_macros<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">/**
     * @brief 析构函数
     */</span>
    <span class="token operator">~</span><span class="token function">DcimEngine</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// --- 存储管理 ---</span>

    <span class="token comment">/**
     * @brief 向指定宏单元的指定页面写入预对齐数据
     * @param macro_idx 目标宏单元索引 (0 到 N-1)
     * @param page_idx 宏单元内的页面索引 (0 到 DCIM_NUM_PAGES - 1)
     * @param page_buffer 包含对齐页面数据的缓冲区
     * @return 操作是否成功
     */</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">writePage</span><span class="token punctuation">(</span>size_t macro_idx<span class="token punctuation">,</span> <span class="token keyword keyword-int">int</span> page_idx<span class="token punctuation">,</span> <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token operator">*</span>page_buffer<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">/**
     * @brief 从指定宏单元的指定页面读取数据
     * @param macro_idx 目标宏单元索引 (0 到 N-1)
     * @param page_idx 宏单元内的页面索引 (0 到 DCIM_NUM_PAGES - 1)
     * @param page_buffer 存储读取页面数据的缓冲区
     * @return 操作是否成功
     */</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">readPage</span><span class="token punctuation">(</span>size_t macro_idx<span class="token punctuation">,</span> <span class="token keyword keyword-int">int</span> page_idx<span class="token punctuation">,</span> <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token operator">*</span>page_buffer<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">/**
     * @brief 向指定宏单元的指定页面的指定行写入预对齐数据
     * @param macro_idx 目标宏单元索引 (0 到 N-1)
     * @param page_idx 宏单元内的页面索引
     * @param row_idx 页面内的行索引 (0 到 DCIM_NUM_ROWS_PER_PAGE - 1)
     * @param row_buffer 包含对齐行数据的缓冲区
     * @return 操作是否成功
     */</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">writeRow</span><span class="token punctuation">(</span>size_t macro_idx<span class="token punctuation">,</span> <span class="token keyword keyword-int">int</span> page_idx<span class="token punctuation">,</span> <span class="token keyword keyword-int">int</span> row_idx<span class="token punctuation">,</span> <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token operator">*</span>row_buffer<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">/**
     * @brief 从指定宏单元的指定页面的指定行读取数据
     * @param macro_idx 目标宏单元索引 (0 到 N-1)
     * @param page_idx 宏单元内的页面索引
     * @param row_idx 页面内的行索引
     * @param row_buffer 存储读取行数据的缓冲区
     * @return 操作是否成功
     */</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">readRow</span><span class="token punctuation">(</span>size_t macro_idx<span class="token punctuation">,</span> <span class="token keyword keyword-int">int</span> page_idx<span class="token punctuation">,</span> <span class="token keyword keyword-int">int</span> row_idx<span class="token punctuation">,</span> <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token operator">*</span>row_buffer<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// --- 计算 ---</span>

    <span class="token comment">/**
     * @brief 使用预对齐的输入向量块执行组合计算
     * @param aligned_input_block 包含 N 个对齐输入向量的连续内存块 (大小 N * 33 * sizeof(uint16_t))
     * @param in_type 原始输入向量的数据类型
     * @param page_idx 所有宏单元使用的页面索引
     * @param wt_type 所有宏单元使用的权重数据类型
     * @param result_vector 存储 64 个累加结果的输出缓冲区 (大小 64 * sizeof(uint32_t))
     * @return 计算是否成功
     */</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">computeAlignedInput</span><span class="token punctuation">(</span>
        <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token operator">*</span>aligned_input_block<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> in_type<span class="token punctuation">,</span>
        <span class="token keyword keyword-int">int</span> page_idx<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> wt_type<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token operator">*</span>result_vector
    <span class="token punctuation">)</span><span class="token punctuation">;</span>

   <span class="token comment">/**
     * @brief 使用原始输入向量块执行组合计算（内部进行对齐）
     * @param raw_input_vector 包含 N 个原始输入向量的连续内存块 (大小 N * 32 * sizeof(uint16_t))
     * @param in_type 输入向量的数据类型
     * @param page_idx 所有宏单元使用的页面索引
     * @param wt_type 所有宏单元使用的权重数据类型
     * @param result_vector 存储 64 个累加结果的输出缓冲区 (大小 64 * sizeof(uint32_t))
     * @return 计算是否成功
     */</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">computeRawInput</span><span class="token punctuation">(</span>
        <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token operator">*</span>raw_input_vector<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> in_type<span class="token punctuation">,</span>
        <span class="token keyword keyword-int">int</span> page_idx<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> wt_type<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token operator">*</span>result_vector
    <span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">/**
     * @brief 获取引擎管理的宏单元数量 N
     * @return 宏单元数量
     */</span>
    size_t <span class="token function">getNumMacros</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span><span class="token punctuation">;</span>

<span class="token keyword keyword-private">private</span><span class="token operator">:</span>
    std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span>DcimArray<span class="token operator">&gt;</span> macros<span class="token punctuation">;</span> <span class="token comment">// 存储 DcimArray 实例</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">isValidMacroIdx</span><span class="token punctuation">(</span>size_t macro_idx<span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span><span class="token punctuation">;</span> <span class="token comment">// 内部辅助函数</span>
    <span class="token comment">// ... 其他私有成员或常量 ...</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre><h2 id="6-实现细节">6. 实现细节 </h2>
<h3 id="61-宏单元管理">6.1 宏单元管理 </h3>
<ul>
<li><code>DcimEngine</code> 在构造时根据传入的 <code>num_macros</code> 参数，创建并持有一个 <code>std::vector&lt;DcimArray&gt;</code>。</li>
<li>所有对特定宏单元存储的访问都需要提供 <code>macro_idx</code> 参数。</li>
</ul>
<h3 id="62-输入处理">6.2 输入处理 </h3>
<ul>
<li><code>computeRawInput</code> 负责将输入的 <code>N * 32</code> 原始向量块，通过循环调用 <code>macros[i].alignInputVector</code>，转换为 <code>N * 33</code> 的对齐向量块。</li>
<li><code>computeAlignedInput</code> 接收 <code>N * 33</code> 的对齐向量块。</li>
</ul>
<h3 id="63-计算与结果汇总">6.3 计算与结果汇总 </h3>
<ul>
<li><code>computeAlignedInput</code> 遍历所有 <code>N</code> 个宏单元。</li>
<li>对于每个宏单元 <code>i</code>，它提取对应的对齐输入向量 <code>aligned_input_i</code>。</li>
<li>调用 <code>macros[i].compute(aligned_input_i, ...)</code> 来获取该宏单元的计算结果 <code>temp_result_i</code>。</li>
<li>将所有 <code>temp_result_i</code> (i 从 0 到 N-1) 按元素累加到最终的 <code>result_vector</code> 中。</li>
</ul>
<h2 id="7-存储格式">7. 存储格式 </h2>
<p><code>DcimEngine</code> 本身不定义新的存储格式。它管理的存储是 <code>N</code> 个独立的 <code>DcimArray</code> 存储区域。每个 <code>DcimArray</code> 实例内部遵循 <code>dcim.md</code> 中描述的页行优先 (Page-Row-Major) 格式。应用层需要通过 <code>macro_idx</code> 来区分访问的是哪个宏单元的存储。</p>
<h2 id="8-错误处理">8. 错误处理 </h2>
<ul>
<li><code>DcimEngine</code> 的存储访问函数和计算函数会进行基本的输入验证（如空指针检查、<code>macro_idx</code> 范围检查）。</li>
<li>大部分错误处理逻辑依赖于底层的 <code>DcimArray</code> 实例。如果 <code>macros[i].writePage/readPage/compute</code> 等函数返回 <code>false</code>，<code>DcimEngine</code> 的对应函数也会返回 <code>false</code>，并将错误信息（通常通过 <code>fprintf(stderr, ...)</code>）传递出来。</li>
<li>在计算过程中，如果任何一个宏单元的 <code>compute</code> 失败，<code>computeAlignedInput</code> 会将最终的 <code>result_vector</code> 清零并返回 <code>false</code>。</li>
</ul>

      </div>
      
      
    
    
    <script type="module">
// TODO: If ZenUML gets integrated into mermaid in the future,
//      we can remove the following lines.


var MERMAID_CONFIG = ({"startOnLoad":false});
if (typeof MERMAID_CONFIG !== 'undefined') {
  MERMAID_CONFIG.startOnLoad = false
  MERMAID_CONFIG.cloneCssStyles = false
  MERMAID_CONFIG.theme = "default"
}

mermaid.initialize(MERMAID_CONFIG || {})
if (typeof(window['Reveal']) !== 'undefined') {
  function mermaidRevealHelper(event) {
    var currentSlide = event.currentSlide
    var diagrams = currentSlide.querySelectorAll('.mermaid')
    for (var i = 0; i < diagrams.length; i++) {
      var diagram = diagrams[i]
      if (!diagram.hasAttribute('data-processed')) {
        mermaid.init(null, diagram, ()=> {
          Reveal.slide(event.indexh, event.indexv)
        })
      }
    }
  }
  Reveal.addEventListener('slidetransitionend', mermaidRevealHelper)
  Reveal.addEventListener('ready', mermaidRevealHelper)
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
} else {
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
}
</script>
    
    
    
  
    </body></html>