-- 定义测试类型列表
local test_types = {
    {name = "wt_fp16", define = "TEST_WT_FP16"},
    {name = "wt_bf16", define = "TEST_WT_BF16"},
    {name = "wt_fp8e4", define = "TEST_WT_FP8E4"},
    {name = "wt_fp8e5", define = "TEST_WT_FP8E5"},
    {name = "wt_int16", define = "TEST_WT_INT16"},
    {name = "wt_int8", define = "TEST_WT_INT8"},
    {name = "wt_int4", define = "TEST_WT_INT4"}
}

-- 添加测试的辅助函数
function add_cim_tests(test_file, enable_build_should_pass)
    enable_build_should_pass = enable_build_should_pass or false
    
    for _, test in ipairs(test_types) do
        local test_config = {
            files = test_file,
            defines = test.define,
            group = "cim_cluster"
        }
        
        if enable_build_should_pass then
            test_config.build_should_pass = true
        end
        
        add_tests(test.name, test_config)
    end
end

target("dcim_macro")
    set_kind("static")
    set_languages("cxx17")
    add_includedirs("./inc",{public = true})
    add_files("./src/*.c|main.c")
    add_deps("common")


target("test_float")
    set_kind("binary")
    add_files("./test/test_float.cpp")
    add_includedirs("./test",{public = true})
    add_deps("ac_types")
    add_deps("dcim_macro")
    add_ldflags("-Wl,--undefined=sc_main", {force = true})

    add_cim_tests("./test/test_float.cpp", false)


target("test_storage_compute")
    set_kind("binary")
    add_files("./test/test_storage_compute.cpp")
    add_includedirs("./test",{public = true})
    add_deps("dcim_macro")
    add_ldflags("-Wl,--undefined=sc_main", {force = true})

    add_cim_tests("./test/test_storage_compute.cpp", false)



target("dcim_macro_cpp")
    set_kind("static")
    add_deps("dcim_macro")
    add_files("./src/dcim_array.cpp")


target("test_dcim_array")
    set_kind("binary")
    add_files("./test/test_dcim_array.cpp")
    add_includedirs("./test",{public = true})
    add_deps("dcim_macro_cpp")
    add_ldflags("-Wl,--undefined=sc_main", {force = true})
    
    add_cim_tests("./test/test_dcim_array.cpp", false)


target("dcim_engine_cpp")
    set_kind("static")
    add_files("./src/dcim_engine.cpp")
    add_deps("dcim_macro_cpp")


target("test_dcim_engine")
    set_kind("binary")
    add_files("./test/test_dcim_engine.cpp")
    add_includedirs("./test",{public = true})
    add_deps("dcim_engine_cpp")
    add_ldflags("-Wl,--undefined=sc_main", {force = true})
    
    add_cim_tests("./test/test_dcim_engine.cpp", false)



target("dcim_cluster")
    set_kind("static")
    add_files("./src/dcim_cluster.cpp")
    add_deps("dcim_engine_cpp")


target("test_dcim_cluster")
    set_kind("binary")
    add_files("./test/test_dcim_cluster.cpp")
    add_includedirs("./test",{public = true})
    add_deps("dcim_cluster")
    add_ldflags("-Wl,--undefined=sc_main", {force = true})
    
    add_cim_tests("./test/test_dcim_cluster.cpp", false)



