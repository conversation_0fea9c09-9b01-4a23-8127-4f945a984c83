#include "dcim_com.h"

uint32_t f2mh_conv(float fdata, uint8_t data_typ)
{
    uint8_t En = 8;
    uint8_t Mn = 23;
    uint8_t Bl = 32;

    // 增加数组大小以确保安全
    uint8_t int_bstr[64] = {0};  // 增加大小以防止潜在的溢出
    uint8_t flt_bstr[64] = {0};
    uint8_t mval_bstr[32] = {0};  // 增加大小以适应不同格式

    uint8_t sign = 0;
    float fdata_fix = 0.0;
    uint32_t int_part = 0;
    float flt_part = 0.0;

    uint32_t rem = 0;
    uint32_t mbit_int = 0;
    uint32_t mbit_flt = 0;

    uint8_t Eval = 0;
    int16_t bias = 0;  // 改为有符号类型以防止下溢
    uint8_t idx = 0;
    uint32_t fp_wd = 0;

    uint32_t mhex_val = 0;

    // #define DEBUG_EN

    fdata_fix = fdata;
    if (fdata < 0)
    {
        sign = 1;
        fdata_fix = 0 - fdata;
    }
    int_part = (uint32_t)fdata_fix;
    flt_part = fdata_fix - int_part;

    // printf("int: %0d flt: %f \n", int_part, flt_part);

    switch (data_typ)
    {
        case FP32:
            En = 8;
            Mn = 23;
            Bl = 32;
            break;
        case FP16:
            En = 5;
            Mn = 10;
            Bl = 16;
            break;
        case BF16:
        case BBF16:
            En = 8;
            Mn = 7;
            Bl = 16;
            break;
        case FP8E4:
            En = 4;
            Mn = 3;
            Bl = 8;
            break;
        case FP8E5:
            En = 5;
            Mn = 2;
            Bl = 8;
            break;
    }

    // float to bin

    // integer
    if (int_part != 0)
    {
        while (int_part != 1)
        {
            rem = int_part % 2;
            int_part = int_part / 2;
            int_bstr[mbit_int] = rem;
            mbit_int++;
        }
        int_bstr[mbit_int] = 1;
    }

#ifdef DEBUG_EN
    printf("int_bstr: ");
    for (int i = 0; i < 32; i++)
    {
        printf("%0d ", int_bstr[31 - i]);
    }
    printf("\n");
#endif

    // float
    while (mbit_flt < 32)
    {
        rem = flt_part * 2;
        flt_part = flt_part * 2 - rem;
        flt_bstr[mbit_flt] = rem;
        // printf("rem: %0d flt_part: %f mbit_flt: %0d \n", rem, flt_part, mbit_flt);
        if (flt_part == 0.0)
            break;
        mbit_flt++;
    }

#ifdef DEBUG_EN
    printf("flt_bstr: ");
    for (int i = 0; i < 32; i++)
    {
        printf("%0d ", flt_bstr[i]);
    }
    printf("\n");
#endif

    // confix

    // if(mbit_int>=0) {
    if (fdata_fix >= 1)
    {
        // E part
        bias = mbit_int;
        Eval = ((1 << (En - 1)) - 1) + bias;

        // M part
        // for(int i=0;i<Mn;i++) {
        for (int i = 0; i < Mn && i < mbit_int; i++)
        {
            if ((mbit_int - 1 - i) >= 0)
            {
                mval_bstr[Mn - 1 - i] = int_bstr[mbit_int - 1 - i];
            }
        }

        if (mbit_flt > 0)
        {
            fp_wd = (Mn > bias) ? (Mn - bias) : 0;
            for (int i = 0; i < fp_wd && i < mbit_flt; i++)
            {
                mval_bstr[fp_wd - 1 - i] = flt_bstr[i];
            }
        }
    }
    else
    {
        // E part
        for (idx = 0; idx < 32; idx++)
        {
            if (flt_bstr[idx] == 1)
                break;
        }
        bias = idx + 1;
        Eval = ((1 << (En - 1)) - 1) - bias;

        // M part
        // 安全地复制尾数位
        for (int i = 0; i < Mn && (bias + i) < sizeof(flt_bstr); i++)
        {
            mval_bstr[Mn - 1 - i] = flt_bstr[bias + i];
        }

        // printf("M_bstr: ");
        // for(int i=0;i<Mn;i++) {
        //   printf("%0d ", mval_bstr[Mn-1-i]);
        // }
        // printf("\n");
    }

    for (int i = 0; i < Mn; i++)
    {
        mhex_val += (mval_bstr[i] * (1 << i));
    }

    if (fdata == 0)
    {
        Eval = 0;
        mhex_val = 0;
    }

    // printf("%f ==> S: %0d E: %0d M: %0d ",fdata, sign, Eval, mhex_val);
    mhex_val += ((sign << (En + Mn)) + (Eval << (Mn)));
    // printf("==> %08x \n", mhex_val);

    return mhex_val;
}

void mh2f_conv(uint32_t mhex, uint8_t data_typ, float* flt_out)
{
    uint8_t mhex_str[32] = {0};
    uint8_t En = 8;
    uint8_t Mn = 23;
    uint8_t Bl = 32;

    uint16_t Eval = 0;  // 增加大小以防止溢出
    int16_t bias = 0;   // 使用更大的类型以防止溢出
    uint8_t idx = 0;
    int64_t vec_int = 0;   // 使用64位以处理大数
    double vec_flt = 0.0;  // 使用double以提高精度

    switch (data_typ)
    {
        case FP32:
            En = 8;
            Mn = 23;
            Bl = 32;
            break;
        case FP16:
            En = 5;
            Mn = 10;
            Bl = 16;
            break;
        case BF16:
            En = 8;
            Mn = 7;
            Bl = 16;
            break;
        case FP8E5:
            En = 4;
            Mn = 3;
            Bl = 8;
            break;
        case FP8E4:
            En = 5;
            Mn = 2;
            Bl = 8;
            break;
    }

    // printf("lsb ");
    //  提取二进制位
    for (int i = 0; i < Bl && i < 32; i++)
    {
        mhex_str[i] = (mhex >> i) & 1;
    }

    // 计算指数值
    for (int i = 0; i < En && i < 32; i++)
    {
        idx = Mn + i;
        if (idx < 32)
        {
            Eval += mhex_str[idx] * (1U << i);
        }
    }

    bias = Eval - ((1 << (En - 1)) - 1);

    if (bias >= 0)
    {
        // int sec
        for (int i = 0; i < bias; i++)
        {
            idx = Mn - bias + i;
            vec_int += mhex_str[idx] * (1 << i);
        }
        vec_int += (int64_t)1 << bias;

        // float sec
        for (int i = 0; i < (Mn - bias); i++)
        {
            idx = Mn - bias - 1 - i;
            // printf("%d ", mhex_str[idx]);
            vec_flt += (float)mhex_str[idx] / (1 << (i + 1));
            // printf("vec_flt: %f\n", vec_flt);
        }
        // printf("\n");
    }
    else
    {
        vec_int = 0;
        // float sec
        idx = -bias;
        vec_flt = (float)1 / (1 << idx);
        for (int i = 0; i < Mn; i++)
        {
            idx = -(bias) + i + 1;
            vec_flt += (float)mhex_str[Mn - 1 - i] / (1 << (idx));
            // printf("mhex_str: %0d\n", mhex_str[Mn-1-i]);
            // printf("vec_flt: %f\n", vec_flt);
        }
        // printf("\n");
    }

    vec_flt += vec_int;
    if (mhex_str[Bl - 1])
    {
        vec_flt = -vec_flt;
    }

    // printf("Eval: %d\n", Eval);
    // printf("bias: %d\n", bias);
    // printf("vec_int: %d\n", vec_int);
    // printf("vec_flt: %f\n", vec_flt);

    if (mhex == 0x80000000)
    {
        vec_flt = -0.0;
    }
    else if (mhex == 0)
    {
        vec_flt = 0.0;
    }

    *flt_out = (float)vec_flt;
    // printf("  flt_out: %f\n", flt_out);
}
