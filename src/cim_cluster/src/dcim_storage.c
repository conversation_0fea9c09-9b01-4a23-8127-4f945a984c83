#include "../inc/dcim_storage.h"
#include <stdio.h>   // For error messages
#include <string.h>  // For memcpy

// Define the global storage array
// This array holds pre-aligned data.
uint8_t dcim_storage[DCIM_NUM_PAGES][DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW];

// Helper function for bounds checking (optional but recommended)
static int is_valid_location(int page_idx, int row_idx)
{
    if (page_idx < 0 || page_idx >= DCIM_NUM_PAGES)
    {
        fprintf(
            stderr, "Error: Page index %d out of bounds (0-%d).\n", page_idx, DCIM_NUM_PAGES - 1);
        return 0;
    }
    if (row_idx < 0 || row_idx >= DCIM_NUM_ROWS_PER_PAGE)
    {
        fprintf(stderr,
                "Error: Row index %d out of bounds (0-%d).\n",
                row_idx,
                DCIM_NUM_ROWS_PER_PAGE - 1);
        return 0;
    }
    return 1;
}

/**
 * @brief Reads a specific row from a specific page in the DCIM storage.
 */
void read_storage_row(int page_idx, int row_idx, uint8_t* buffer)
{
    if (!is_valid_location(page_idx, row_idx) || buffer == NULL)
    {
        // Optionally clear buffer on error
        if (buffer != NULL)
        {
            memset(buffer, 0, DCIM_NUM_BYTES_PER_ROW);
        }
        return;
    }
    memcpy(buffer, dcim_storage[page_idx][row_idx], DCIM_NUM_BYTES_PER_ROW);
}

/**
 * @brief Writes data to a specific row on a specific page in the DCIM storage.
 */
void write_storage_row(int page_idx, int row_idx, const uint8_t* buffer)
{
    if (!is_valid_location(page_idx, row_idx) || buffer == NULL)
    {
        return;  // Do nothing on error
    }
    memcpy(dcim_storage[page_idx][row_idx], buffer, DCIM_NUM_BYTES_PER_ROW);
}

/**
 * @brief Reads an entire page from the DCIM storage.
 */
void read_storage_page(int page_idx, uint8_t* page_buffer)
{
    if (page_idx < 0 || page_idx >= DCIM_NUM_PAGES || page_buffer == NULL)
    {
        fprintf(stderr,
                "Error: Page index %d out of bounds (0-%d) or null buffer.\n",
                page_idx,
                DCIM_NUM_PAGES - 1);
        // Optionally clear buffer on error
        if (page_buffer != NULL)
        {
            memset(page_buffer, 0, DCIM_NUM_ROWS_PER_PAGE * DCIM_NUM_BYTES_PER_ROW);
        }
        return;
    }
    memcpy(page_buffer, dcim_storage[page_idx], DCIM_NUM_ROWS_PER_PAGE * DCIM_NUM_BYTES_PER_ROW);
}

/**
 * @brief Writes data to an entire page in the DCIM storage.
 */
void write_storage_page(int page_idx, const uint8_t* page_buffer)
{
    if (page_idx < 0 || page_idx >= DCIM_NUM_PAGES || page_buffer == NULL)
    {
        fprintf(stderr,
                "Error: Page index %d out of bounds (0-%d) or null buffer.\n",
                page_idx,
                DCIM_NUM_PAGES - 1);
        return;  // Do nothing on error
    }
    memcpy(dcim_storage[page_idx], page_buffer, DCIM_NUM_ROWS_PER_PAGE * DCIM_NUM_BYTES_PER_ROW);
}
