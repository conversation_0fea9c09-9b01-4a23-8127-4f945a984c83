#include "../inc/dcim_array.hpp"
#include <cstdio>   // For fprintf, stderr
#include <cstring>  // For memcpy, memset

// Constructor
DcimArray::DcimArray()
{
    // Initialize internal storage to zero
    memset(internal_storage, 0, sizeof(internal_storage));
}

// Destructor
DcimArray::~DcimArray()
{
    // Nothing dynamically allocated to clean up
}

// --- Private Helpers ---

bool DcimArray::isValidPage(int page_idx) const
{
    return (page_idx >= 0 && page_idx < DCIM_NUM_PAGES);
}

bool DcimArray::isValidRow(int row_idx) const
{
    // Check if row index is within the valid range (0 to 32)
    return (row_idx >= 0 && row_idx < DCIM_NUM_ROWS_PER_PAGE);
}

int DcimArray::getMaxColumns(uint8_t wt_type) const
{
    // Reusing the logic from C implementation for consistency
    switch (wt_type)
    {
        case INT4:
            return 64;
        case INT8:
        case FP8E4:
        case FP8E5:
        case BBF16:
            return 32;
        case INT12:  // Fallthrough - treat as 16 for now
        case INT16:
        case FP16:
        case BF16:
            return 16;
        default:
            return 0;  // Indicate unsupported type
    }
}

// --- Public Methods ---

bool DcimArray::writePage(int page_idx, const uint8_t* page_buffer)
{
    if (!isValidPage(page_idx) || page_buffer == nullptr)
    {
        fprintf(stderr,
                "Error: Invalid page index %d or null buffer in DcimArray::writePage.\n",
                page_idx);
        return false;
    }
    // Direct memory copy is the most efficient way here.
    memcpy(
        internal_storage[page_idx], page_buffer, DCIM_NUM_ROWS_PER_PAGE * DCIM_NUM_BYTES_PER_ROW);
    return true;
}

bool DcimArray::readPage(int page_idx, uint8_t* page_buffer)
{
    if (!isValidPage(page_idx) || page_buffer == nullptr)
    {
        fprintf(stderr,
                "Error: Invalid page index %d or null buffer in DcimArray::readPage.\n",
                page_idx);
        // Optionally clear buffer on error
        if (page_buffer != nullptr)
        {
            memset(page_buffer, 0, DCIM_NUM_ROWS_PER_PAGE * DCIM_NUM_BYTES_PER_ROW);
        }
        return false;
    }
    // Direct memory copy is the most efficient way here.
    memcpy(
        page_buffer, internal_storage[page_idx], DCIM_NUM_ROWS_PER_PAGE * DCIM_NUM_BYTES_PER_ROW);
    return true;
}

bool DcimArray::writeRow(int page_idx, int row_idx, const uint8_t* row_buffer)
{
    if (!isValidPage(page_idx) || !isValidRow(row_idx) || row_buffer == nullptr)
    {
        fprintf(stderr,
                "Error: Invalid page/row index (%d/%d) or null buffer in DcimArray::writeRow.\n",
                page_idx,
                row_idx);
        return false;
    }
    // Copy data to the specific row within the specified page
    memcpy(internal_storage[page_idx][row_idx], row_buffer, DCIM_NUM_BYTES_PER_ROW);
    return true;
}

bool DcimArray::readRow(int page_idx, int row_idx, uint8_t* row_buffer)
{
    if (!isValidPage(page_idx) || !isValidRow(row_idx) || row_buffer == nullptr)
    {
        fprintf(stderr,
                "Error: Invalid page/row index (%d/%d) or null buffer in DcimArray::readRow.\n",
                page_idx,
                row_idx);
        // Optionally clear buffer on error
        if (row_buffer != nullptr)
        {
            memset(row_buffer, 0, DCIM_NUM_BYTES_PER_ROW);
        }
        return false;
    }
    // Copy data from the specific row within the specified page
    memcpy(row_buffer, internal_storage[page_idx][row_idx], DCIM_NUM_BYTES_PER_ROW);
    return true;
}

bool DcimArray::compute(const uint8_t* aligned_input_vector,  // Size 33*sizeof(uint16_t)
                        uint8_t in_type,
                        int page_idx,
                        uint8_t wt_type,
                        uint8_t* result_vector  // Size 64*sizeof(uint32_t)
)
{
    // --- Input Validation ---
    if (aligned_input_vector == nullptr || result_vector == nullptr)
    {
        fprintf(stderr, "Error: Null pointer passed to DcimArray::compute.\n");
        if (result_vector != nullptr)
            memset(result_vector, 0, 64 * sizeof(uint32_t));
        return false;
    }
    if (!isValidPage(page_idx))
    {
        fprintf(stderr, "Error: Invalid page index %d in DcimArray::compute.\n", page_idx);
        memset(result_vector, 0, 64 * sizeof(uint32_t));
        return false;
    }

    int max_columns = getMaxColumns(wt_type);
    if (max_columns == 0)
    {
        fprintf(stderr, "Error: Unsupported weight type (%d) in DcimArray::compute.\n", wt_type);
        memset(result_vector, 0, 64 * sizeof(uint32_t));
        return false;
    }
    if (wt_type == INT12)
    {
        // fprintf(stderr, "Warning: INT12 computation assumes simple packing within uint16_t.\n");
    }

    // --- Allocate Buffer for Weight Argument ---
    // Use const reference to the page data in internal storage
    const uint8_t(*page_data)[DCIM_NUM_BYTES_PER_ROW] = internal_storage[page_idx];
    uint16_t weight_arg_for_macro[DCIM_NUM_ROWS_PER_PAGE];  // Argument for dcim_macro_com (size 33)

    // --- Main Computation Loop (Reuses C functions) ---
    for (int j = 0; j < 64; ++j)
    {
        // Check if the current column index is valid for the weight type
        if (j >= max_columns)
        {
            ((uint32_t*)result_vector)[j] = 0;  // Pad with zero
            continue;
        }

        // Reuse C function to extract the full aligned weight column (33 elements)
        extract_aligned_weight_column(page_data, wt_type, j, weight_arg_for_macro);

        // Reuse C core computation function
        ((uint32_t*)result_vector)[j] = dcim_macro_com(
            weight_arg_for_macro,             // Aligned weight column + exponent (size 33)
            wt_type,                          // Weight data type
            (uint16_t*)aligned_input_vector,  // Cast uint8_t* to uint16_t* for aligned input vector
            in_type                           // Input data type
        );
    }

    return true;  // Computation completed
}

// --- Optional Alignment Functionality Implementation ---

void DcimArray::alignInputVector(const uint8_t* raw_input_vector,
                                 uint8_t in_type,
                                 uint8_t* aligned_output)
{
    if (raw_input_vector == nullptr || aligned_output == nullptr)
    {
        fprintf(stderr, "Error: Null pointer passed to DcimArray::alignInputVector.\n");
        if (aligned_output != nullptr)
            memset(aligned_output, 0, DCIM_NUM_ROWS_PER_PAGE * sizeof(uint16_t));
        return;
    }
    // Cast uint8_t pointers to uint16_t pointers for float_data_align
    float_data_align((uint16_t*)raw_input_vector, in_type, (uint16_t*)aligned_output);
}

void DcimArray::computeRawInput(const uint8_t* raw_input_vector,  // Size 32*sizeof(uint16_t)
                                uint8_t in_type,
                                int page_idx,
                                uint8_t wt_type,
                                uint8_t* result_vector  // Size 64*sizeof(uint32_t)
)
{
    if (raw_input_vector == NULL)
    {
        fprintf(stderr, "Error: Null raw_input_vector passed.\n");
        if (result_vector != NULL)
            memset(result_vector, 0, 64 * sizeof(uint32_t));
        return;
    }

    uint16_t aligned_input_vector[DCIM_NUM_ROWS_PER_PAGE];  // Size 33

    // Align the raw input vector
    if (in_type < 4)
    {
        memcpy(aligned_input_vector, raw_input_vector, 32 * sizeof(uint16_t));
        aligned_input_vector[32] = 0;
    }
    else
    {
        float_data_align((uint16_t*)raw_input_vector, in_type, aligned_input_vector);
    }

    // Call the main computation function with the aligned input
    compute((uint8_t*)aligned_input_vector, in_type, page_idx, wt_type, result_vector);
}