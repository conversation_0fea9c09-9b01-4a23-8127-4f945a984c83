#include "../inc/dcim_extract.h"
#include <stdio.h>   // For error messages
#include <string.h>  // For memset

/**
 * @brief Extracts the aligned data for a single logical weight column from the
 *        physical storage rows (which hold pre-aligned data), including the exponent.
 */
void extract_aligned_weight_column(
    const uint8_t (*aligned_weight_page_rows)[DCIM_NUM_BYTES_PER_ROW],  // Pass full page data
    uint8_t wt_type,
    int column_idx,
    uint16_t* extracted_aligned_col  // Output size 33
)
{
    if (aligned_weight_page_rows == NULL || extracted_aligned_col == NULL)
    {
        fprintf(stderr, "Error: Null pointer passed to extract_aligned_weight_column.\n");
        if (extracted_aligned_col != NULL)
            memset(extracted_aligned_col, 0, DCIM_NUM_ROWS_PER_PAGE * sizeof(uint16_t));
        return;
    }

    // Determine bits per element and max columns based on type
    int bits_per_element = 0;
    int max_columns = 0;

    switch (wt_type)
    {
        case INT4:
            bits_per_element = 4;
            max_columns = 64;
            break;
        case INT8:
        case FP8E4:
        case FP8E5:
        case BBF16:
            bits_per_element = 8;
            max_columns = 32;
            break;
        case INT12:                 // Assuming INT12 might be packed less efficiently for now
            bits_per_element = 16;  // Treat as 16 for simple extraction from uint16_t array
            max_columns = 16;       // Needs clarification if INT12 packing is different
            fprintf(stderr, "Warning: INT12 extraction assumes simple packing within uint16_t.\n");
            break;
        case INT16:
        case FP16:
        case BF16:
            bits_per_element = 16;
            max_columns = 16;
            break;
        case FP32:  // FP32 weights are not directly supported in the 256-bit row structure
        default:
            fprintf(stderr,
                    "Error: Unsupported or invalid weight type (%d) for extraction.\n",
                    wt_type);
            memset(extracted_aligned_col, 0, DCIM_NUM_ROWS_PER_PAGE * sizeof(uint16_t));
            return;
    }

    // Check if column index is valid for the given type
    if (column_idx < 0 || column_idx >= max_columns)
    {
        fprintf(stderr,
                "Error: Column index %d out of bounds for weight type %d (max %d).\n",
                column_idx,
                wt_type,
                max_columns - 1);
        memset(extracted_aligned_col, 0, DCIM_NUM_ROWS_PER_PAGE * sizeof(uint16_t));
        return;
    }

    // Loop through all 33 rows (0-31 data, 32 exponent)
    for (int i = 0; i < DCIM_NUM_ROWS_PER_PAGE; i++)
    {
        int source_byte_idx = 0;
        uint16_t extracted_value = 0;

        if (bits_per_element == 16)
        {
            source_byte_idx = column_idx * 2;
            if (source_byte_idx + 1 >= DCIM_NUM_BYTES_PER_ROW)
            {
                fprintf(stderr,
                        "Error: Calculated byte index %d exceeds row width %d.\n",
                        source_byte_idx,
                        DCIM_NUM_BYTES_PER_ROW);
                extracted_aligned_col[i] = 0;
                continue;
            }
            // For 16-bit values, combine two bytes
            extracted_value = (aligned_weight_page_rows[i][source_byte_idx + 1] << 8) |
                              aligned_weight_page_rows[i][source_byte_idx];
        }
        else if (bits_per_element == 8)
        {
            source_byte_idx = column_idx;
            if (source_byte_idx >= DCIM_NUM_BYTES_PER_ROW)
            {
                fprintf(stderr,
                        "Error: Calculated byte index %d exceeds row width %d.\n",
                        source_byte_idx,
                        DCIM_NUM_BYTES_PER_ROW);
                extracted_aligned_col[i] = 0;
                continue;
            }
            extracted_value = aligned_weight_page_rows[i][source_byte_idx];
        }
        else if (bits_per_element == 4)
        {
            source_byte_idx = column_idx / 2;
            int nibble_offset = column_idx % 2;  // 0=lower nibble, 1=upper nibble
            if (source_byte_idx >= DCIM_NUM_BYTES_PER_ROW)
            {
                fprintf(stderr,
                        "Error: Calculated byte index %d exceeds row width %d.\n",
                        source_byte_idx,
                        DCIM_NUM_BYTES_PER_ROW);
                extracted_aligned_col[i] = 0;
                continue;
            }
            uint8_t byte_value = aligned_weight_page_rows[i][source_byte_idx];
            if (nibble_offset == 0)
            {
                extracted_value = byte_value & 0x0F;
            }
            else
            {
                extracted_value = (byte_value >> 4) & 0x0F;
            }
        }

        extracted_aligned_col[i] = extracted_value;
    }
}
