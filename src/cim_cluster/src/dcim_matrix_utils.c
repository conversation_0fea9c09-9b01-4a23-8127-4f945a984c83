#include "../inc/dcim_matrix_utils.h"
#include <stdbool.h>
#include <stdio.h>   // For fprintf, stderr
#include <string.h>  // For memcpy, memset

// --- Helper Function Implementations ---

int get_num_logical_columns(uint8_t wt_type)
{
    switch (wt_type)
    {
        case INT4:
            return 64;
        case INT8:
        case FP8E4:
        case FP8E5:
        case BBF16:
            return 32;
        case INT12:  // Treat as 16 for column count, packing handled separately
        case INT16:
        case FP16:
        case BF16:
            return 16;
        default:
            fprintf(stderr,
                    "Error: Unsupported weight type (%d) in get_num_logical_columns.\n",
                    wt_type);
            return 0;  // Indicate error
    }
}

static int get_bits_per_element(uint8_t data_type)
{
    switch (data_type)
    {
        case INT4:
            return 4;
        case INT8:
        case FP8E4:
        case FP8E5:
        case BBF16:
            return 8;
        case INT12:
            return 12;  // Specific handling needed during packing/unpacking
        case INT16:
        case FP16:
        case BF16:
            return 16;
        default:
            fprintf(
                stderr, "Error: Unsupported data type (%d) in get_bits_per_element.\n", data_type);
            return 0;  // Indicate error
    }
}

// Function to check if a data type requires alignment (is floating point)
static bool requires_alignment(uint8_t data_type)
{
    switch (data_type)
    {
        case FP8E4:
        case FP8E5:
        case FP16:
        case BF16:
        case BBF16:
            return true;
        case INT4:
        case INT8:
        case INT12:
        case INT16:
            return false;  // Fixed-point types do not require alignment
        default:
            fprintf(stderr,
                    "Warning: Unknown data type (%d) in requires_alignment, assuming no alignment "
                    "needed.\n",
                    data_type);
            return false;
    }
}

bool convertRawToAlignedColumnMajor(
    const uint8_t*
        raw_matrix,  // Input: Flattened column-major raw data [cols][32*sizeof(uint16_t)]
    uint8_t wt_type,
    uint8_t* aligned_matrix_col_major  // Output: Flattened column-major aligned data
                                       // [cols][33*sizeof(uint16_t)]
)
{
    if (raw_matrix == NULL || aligned_matrix_col_major == NULL)
    {
        fprintf(stderr, "Error: Null pointer passed to convertRawToAlignedColumnMajor.\n");
        return false;
    }

    int num_columns = get_num_logical_columns(wt_type);
    if (num_columns == 0)
    {
        fprintf(
            stderr, "Error: Cannot convert matrix with unsupported weight type (%d).\n", wt_type);
        return false;
    }

    bool align_needed = requires_alignment(wt_type);
    for (int c = 0; c < num_columns; ++c)
    {
        // Pointer to the start of the current raw column (32 elements)
        const uint16_t* raw_col_ptr = (const uint16_t*)raw_matrix + c * 32;
        // Pointer to the start of the current aligned output column (33 elements)
        uint16_t* aligned_col_ptr = (uint16_t*)aligned_matrix_col_major + c * 33;

        if (align_needed)
        {
            // Call the alignment function provided by dcim_com.h
            float_data_align((uint16_t*)raw_col_ptr, wt_type, aligned_col_ptr);
        }
        else
        {
            // For fixed-point types, directly copy the 32 data elements
            memcpy(aligned_col_ptr, raw_col_ptr, 32 * sizeof(uint16_t));
            // Set the 33rd element (exponent) to 0 for fixed-point types
            aligned_col_ptr[32] = 0;
        }
    }

    return true;
}

bool convertAlignedColumnMajorToPageRowMajor(
    const uint8_t* aligned_matrix_col_major,  // Input: Flattened column-major aligned data
                                              // [cols][33*sizeof(uint16_t)]
    uint8_t wt_type,
    uint8_t* page_buffer  // Output: Row-major page format pointer
)
{
    if (aligned_matrix_col_major == NULL || page_buffer == NULL)
    {
        fprintf(stderr, "Error: Null pointer passed to convertAlignedColumnMajorToPageRowMajor.\n");
        return false;
    }

    int num_columns = get_num_logical_columns(wt_type);
    int bits_per_element = get_bits_per_element(wt_type);

    if (num_columns == 0 || bits_per_element == 0)
    {
        fprintf(
            stderr, "Error: Cannot convert matrix with unsupported weight type (%d).\n", wt_type);
        return false;
    }
    if (bits_per_element == 12)
    {
        fprintf(stderr,
                "Error: INT12 packing not yet implemented in "
                "convertAlignedColumnMajorToPageRowMajor.\n");
        return false;
    }

    // Clear the output buffer first
    memset(page_buffer, 0, DCIM_NUM_ROWS_PER_PAGE * DCIM_NUM_BYTES_PER_ROW);

    // Cast input to uint16_t pointer for easier access
    const uint16_t* aligned_matrix_col_major_16 = (const uint16_t*)aligned_matrix_col_major;

    // Process each row
    for (int r = 0; r < DCIM_NUM_ROWS_PER_PAGE; ++r)
    {
        uint8_t* row_ptr = page_buffer + (r * DCIM_NUM_BYTES_PER_ROW);
        uint8_t current_byte = 0;
        int bits_filled = 0;

        // Process all columns for this row
        for (int c = 0; c < num_columns; ++c)
        {
            // Get the value from the column-major input
            uint16_t aligned_value = aligned_matrix_col_major_16[c * DCIM_NUM_ROWS_PER_PAGE + r];

            switch (bits_per_element)
            {
                case 16:
                {
                    int byte_idx = c * 2;
                    if (byte_idx + 1 >= DCIM_NUM_BYTES_PER_ROW)
                    {
                        fprintf(stderr,
                                "Error: Column index %d out of bounds for 16-bit type in row %d.\n",
                                c,
                                r);
                        continue;
                    }
                    row_ptr[byte_idx] = aligned_value & 0xFF;
                    row_ptr[byte_idx + 1] = (aligned_value >> 8) & 0xFF;
                    break;
                }
                case 8:
                {
                    if (c >= DCIM_NUM_BYTES_PER_ROW)
                    {
                        fprintf(stderr,
                                "Error: Column index %d out of bounds for 8-bit type in row %d.\n",
                                c,
                                r);
                        continue;
                    }
                    row_ptr[c] = aligned_value & 0xFF;
                    break;
                }
                case 4:
                {
                    // Pack two 4-bit values into one byte
                    uint8_t nibble = aligned_value & 0xF;
                    int byte_idx = c / 2;

                    if (byte_idx >= DCIM_NUM_BYTES_PER_ROW)
                    {
                        fprintf(stderr,
                                "Error: Column index %d out of bounds for 4-bit type in row %d.\n",
                                c,
                                r);
                        continue;
                    }

                    if (c % 2 == 0)
                    {
                        // First nibble of the byte
                        row_ptr[byte_idx] = nibble;
                    }
                    else
                    {
                        // Second nibble of the byte
                        row_ptr[byte_idx] |= (nibble << 4);
                    }
                    break;
                }
                default:
                    fprintf(stderr,
                            "Internal Error: Unexpected bits_per_element (%d).\n",
                            bits_per_element);
                    return false;
            }
        }
    }

    return true;
}
