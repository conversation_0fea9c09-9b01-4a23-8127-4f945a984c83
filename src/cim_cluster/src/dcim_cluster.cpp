#include "../inc/dcim_cluster.hpp"
#include <cstdio>   // For fprintf, stderr
#include <cstring>  // For memset, memcpy
#include <limits>   // For numeric_limits if needed
#include <numeric>  // For std::accumulate (optional, manual sum likely needed for float conversion)
#include <stdexcept>  // For std::invalid_argument (though constructor doesn't throw)
#include <vector>     // For std::vector

// --- Constructor ---
DcimCluster::DcimCluster() : current_mode(OperationMode::PATTERN1_OUTPUT_FUSION)
{
    // The DcimEngine constructor requires the number of macros.
    // We assume DCIM_ENGINE_N_MACROS is defined correctly in dcim_engine.hpp
    // Resize the vector and rely on DcimEngine's constructor.
    try
    {
        engines.resize(CIME_NUM, DcimEngine(MACROS_PER_ENGINE));
    }
    catch (const std::exception& e)
    {
        // Handle potential exceptions from DcimEngine constructor if it throws
        fprintf(stderr, "FATAL: Exception during DcimCluster construction: %s\n", e.what());
        // Depending on desired behavior, might re-throw or exit
        throw;  // Re-throw to indicate construction failure
    }
    // Verify the size just in case resize didn't work as expected (unlikely)
    if (engines.size() != CIME_NUM)
    {
        fprintf(stderr, "FATAL: DcimCluster failed to initialize correct number of engines.\n");
        // Throw an exception or handle error appropriately
        throw std::runtime_error("DcimCluster engine initialization failed.");
    }
}

// --- Destructor ---
DcimCluster::~DcimCluster()
{
    // std::vector handles the destruction of DcimEngine objects automatically.
}

// --- Mode Configuration ---
void DcimCluster::setOperationMode(OperationMode mode)
{
    current_mode = mode;
}

DcimCluster::OperationMode DcimCluster::getOperationMode() const
{
    return current_mode;
}

// --- Private Helpers ---
bool DcimCluster::isValidEngineIdx(size_t engine_idx) const
{
    return (engine_idx < CIME_NUM);
}

const uint8_t* DcimCluster::getRawInputPtrForEngine(const uint8_t* block_start,
                                                    size_t engine_idx) const
{
    if (!block_start || !isValidEngineIdx(engine_idx))
    {
        return nullptr;  // Or handle error appropriately
    }
    return block_start + (engine_idx * RAW_INPUT_BYTES_PER_ENGINE);
}

const uint8_t* DcimCluster::getAlignedInputPtrForEngine(const uint8_t* block_start,
                                                        size_t engine_idx) const
{
    if (!block_start || !isValidEngineIdx(engine_idx))
    {
        return nullptr;  // Or handle error appropriately
    }
    return block_start + (engine_idx * ALIGNED_INPUT_BYTES_PER_ENGINE);
}

// --- Storage Management (Pass-through) ---

bool DcimCluster::writePage(size_t engine_idx,
                            size_t macro_idx,
                            int page_idx,
                            const uint8_t* page_buffer)
{
    if (!isValidEngineIdx(engine_idx))
    {
        fprintf(stderr, "Error: Invalid engine index %zu in DcimCluster::writePage.\n", engine_idx);
        return false;
    }
    // Delegate to the specific engine's method, passing macro_idx
    return engines[engine_idx].writePage(macro_idx, page_idx, page_buffer);
}

bool DcimCluster::readPage(size_t engine_idx, size_t macro_idx, int page_idx, uint8_t* page_buffer)
{
    if (!isValidEngineIdx(engine_idx))
    {
        fprintf(stderr, "Error: Invalid engine index %zu in DcimCluster::readPage.\n", engine_idx);
        if (page_buffer != nullptr)
        {
            // Ensure buffer is cleared on error according to DcimEngine's contract
            memset(page_buffer, 0, DCIM_NUM_ROWS_PER_PAGE * DCIM_NUM_BYTES_PER_ROW);
        }
        return false;
    }
    // Delegate to the specific engine's method, passing macro_idx
    return engines[engine_idx].readPage(macro_idx, page_idx, page_buffer);
}

bool DcimCluster::writeRow(size_t engine_idx,
                           size_t macro_idx,
                           int page_idx,
                           int row_idx,
                           const uint8_t* row_buffer)
{
    if (!isValidEngineIdx(engine_idx))
    {
        fprintf(stderr, "Error: Invalid engine index %zu in DcimCluster::writeRow.\n", engine_idx);
        return false;
    }
    // Delegate to the specific engine's method, passing macro_idx
    return engines[engine_idx].writeRow(macro_idx, page_idx, row_idx, row_buffer);
}

bool DcimCluster::readRow(size_t engine_idx,
                          size_t macro_idx,
                          int page_idx,
                          int row_idx,
                          uint8_t* row_buffer)
{
    if (!isValidEngineIdx(engine_idx))
    {
        fprintf(stderr, "Error: Invalid engine index %zu in DcimCluster::readRow.\n", engine_idx);
        if (row_buffer != nullptr)
        {
            // Ensure buffer is cleared on error according to DcimEngine's contract
            memset(row_buffer, 0, DCIM_NUM_BYTES_PER_ROW);
        }
        return false;
    }
    // Delegate to the specific engine's method, passing macro_idx
    return engines[engine_idx].readRow(macro_idx, page_idx, row_idx, row_buffer);
}

// --- Computation ---

bool DcimCluster::computeRawInput(const uint8_t* raw_input_block,
                                  uint8_t in_type,
                                  int page_idx,
                                  uint8_t wt_type,
                                  uint8_t* output_block)
{
    // --- Basic Input Validation ---
    if (raw_input_block == nullptr || output_block == nullptr)
    {
        fprintf(stderr, "Error: Null pointer passed to DcimCluster::computeRawInput.\n");
        if (output_block != nullptr)
            memset(output_block, 0, TOTAL_OUTPUT_BYTES);
        return false;
    }

    // --- Alignment ---
    // Allocate a single block for all aligned inputs.
    std::vector<uint8_t> aligned_input_block_vec(TOTAL_ALIGNED_INPUT_BYTES);
    uint8_t* aligned_input_block_ptr = aligned_input_block_vec.data();

    // Align input for each engine individually
    // Note: DcimEngine::alignInputVector aligns *one* macro's worth (32->33).
    // We need to call the DcimArray aligner or replicate logic for the engine's full input (2*32 ->
    // 2*33) Let's assume DcimEngine should have an alignInputBlock method, or we do it here.
    // Replicating DcimEngine's alignment logic here for simplicity:
    for (size_t i = 0; i < CIME_NUM; ++i)
    {
        const uint8_t* current_raw_engine_input = getRawInputPtrForEngine(raw_input_block, i);
        uint8_t* current_aligned_engine_output = const_cast<uint8_t*>(getAlignedInputPtrForEngine(
            aligned_input_block_ptr, i));  // Cast needed for output buffer

        if (!current_raw_engine_input || !current_aligned_engine_output)
        {
            fprintf(stderr,
                    "Error: Failed to get input/output pointers during alignment in "
                    "DcimCluster::computeRawInput for engine %zu.\n",
                    i);
            memset(output_block, 0, TOTAL_OUTPUT_BYTES);
            return false;
        }

        // Align the two macros within the engine
        for (size_t m = 0; m < MACROS_PER_ENGINE; ++m)
        {
            const uint8_t* raw_macro_input =
                current_raw_engine_input + (m * RAW_ELEMENTS_PER_MACRO * BYTES_PER_ELEMENT);
            uint8_t* aligned_macro_output = current_aligned_engine_output +
                                            (m * ALIGNED_ELEMENTS_PER_MACRO * BYTES_PER_ELEMENT);

            // Use DcimArray's alignInputVector (assuming engines[i] is valid)
            // Need a temporary DcimArray instance or access to the underlying ones if possible,
            // otherwise, call the C function directly. Let's call C function for simplicity.
            if (in_type < 4)
            {  // Integer types
                memcpy(aligned_macro_output,
                       raw_macro_input,
                       RAW_ELEMENTS_PER_MACRO * BYTES_PER_ELEMENT);
                // Set exponent part (last element) to 0
                memset(aligned_macro_output + (RAW_ELEMENTS_PER_MACRO * BYTES_PER_ELEMENT),
                       0,
                       BYTES_PER_ELEMENT);
            }
            else
            {  // Floating point types
                // Use const_cast for the first argument as float_data_align expects uint16_t*
                float_data_align(
                    const_cast<uint16_t*>(reinterpret_cast<const uint16_t*>(raw_macro_input)),
                    in_type,
                    reinterpret_cast<uint16_t*>(aligned_macro_output));
            }
        }
    }

    // --- Call Internal Compute ---
    return computeInternal(aligned_input_block_ptr, in_type, page_idx, wt_type, output_block);
}

bool DcimCluster::computeAlignedInput(const uint8_t* aligned_input_block,
                                      uint8_t in_type,
                                      int page_idx,
                                      uint8_t wt_type,
                                      uint8_t* output_block)
{
    // --- Basic Input Validation ---
    if (aligned_input_block == nullptr || output_block == nullptr)
    {
        fprintf(stderr, "Error: Null pointer passed to DcimCluster::computeAlignedInput.\n");
        if (output_block != nullptr)
            memset(output_block, 0, TOTAL_OUTPUT_BYTES);
        return false;
    }

    // --- Call Internal Compute ---
    // Directly use the provided aligned input block
    return computeInternal(aligned_input_block, in_type, page_idx, wt_type, output_block);
}

// --- Internal Compute Logic ---
bool DcimCluster::computeInternal(
    const uint8_t* aligned_input_block,  // Full block (4*2*33*uint16_t)
    uint8_t in_type,
    int page_idx,
    uint8_t wt_type,
    uint8_t* output_block  // Final output buffer (4*64*uint32_t)
)
{
    // --- Temporary Result Buffer ---
    // Holds results from all 4 engines sequentially [R0, R1, R2, R3]
    std::vector<uint32_t> all_engine_results_vec(CIME_NUM * RESULTS_PER_ENGINE);
    uint8_t* temp_result_buffer_ptr = reinterpret_cast<uint8_t*>(all_engine_results_vec.data());
    bool overall_success = true;

    // --- Engine Computation Loop ---
    for (size_t i = 0; i < CIME_NUM; ++i)
    {
        const uint8_t* input_for_this_engine = nullptr;
        uint8_t* output_for_this_engine = temp_result_buffer_ptr + (i * RESULTS_BYTES_PER_ENGINE);

        // Determine which input to use based on mode
        switch (current_mode)
        {
            case OperationMode::PATTERN1_OUTPUT_FUSION:
                // Use input corresponding to this engine index
                input_for_this_engine = getAlignedInputPtrForEngine(aligned_input_block, i);
                break;
            case OperationMode::PATTERN2_INPUT_DIST_OUTPUT_FUSION:
                // Engines 0, 1 use input 0; Engines 2, 3 use input 2
                if (i < 2)
                {
                    input_for_this_engine = getAlignedInputPtrForEngine(aligned_input_block, 0);
                }
                else
                {
                    input_for_this_engine = getAlignedInputPtrForEngine(aligned_input_block, 2);
                }
                break;
            case OperationMode::PATTERN3_INPUT_DIST:
                // All engines use input 0 (broadcast)
                input_for_this_engine = getAlignedInputPtrForEngine(aligned_input_block, 0);
                break;
            default:
                fprintf(stderr, "Error: Unknown operation mode in DcimCluster::computeInternal.\n");
                overall_success = false;
                break;  // Exit switch
        }

        if (!overall_success || !input_for_this_engine)
        {
            fprintf(stderr,
                    "Error: Failed to determine input for engine %zu in mode %d.\n",
                    i,
                    static_cast<int>(current_mode));
            overall_success = false;
            break;  // Exit loop
        }

        // Call the engine's compute function
        bool engine_success = engines[i].computeAlignedInput(
            input_for_this_engine,
            in_type,
            page_idx,
            wt_type,
            output_for_this_engine  // Write to the temporary buffer section
        );

        if (!engine_success)
        {
            fprintf(
                stderr,
                "Error: Computation failed in engine %zu within DcimCluster::computeInternal.\n",
                i);
            overall_success = false;
            // Decide whether to break or continue (e.g., to log all failures)
            // break;
        }
    }  // End of engine loop

    // --- Print Engine Results ---
    // if (overall_success) {
    //     printf("=== DcimCluster Engine Results (4 engines x 64 elements) ===\n");
    //     for (size_t data_idx = 0; data_idx < RESULTS_PER_ENGINE; ++data_idx) {
    //         printf("data %2zu: ", data_idx);
    //         for (size_t engine_idx = 0; engine_idx < CIME_NUM; ++engine_idx) {
    //             // Calculate the index in all_engine_results_vec
    //             // Layout: [Engine0_data0...Engine0_data63, Engine1_data0...Engine1_data63, ...]
    //             size_t vec_idx = engine_idx * RESULTS_PER_ENGINE + data_idx;

    //             // Interpret the uint32_t as float
    //             float result_value = *reinterpret_cast<float*>(&all_engine_results_vec[vec_idx]);
    //             printf("%f", result_value);

    //             if (engine_idx < CIME_NUM - 1) {
    //                 printf("  ");
    //             }
    //         }
    //         printf("\n");
    //     }
    //     printf("=== End of Engine Results ===\n");
    // }

    // --- Aggregate Results ---
    if (overall_success)
    {
        uint32_t* final_output_ptr = reinterpret_cast<uint32_t*>(output_block);
        switch (current_mode)
        {
            case OperationMode::PATTERN1_OUTPUT_FUSION:
                // Accumulate results from all 4 engines into the first 64 elements
                accumulateResults(all_engine_results_vec, final_output_ptr, 0, CIME_NUM, 0);
                // Zero out the rest of the buffer if needed (optional, depends on contract)
                // memset(output_block + RESULTS_BYTES_PER_ENGINE, 0, (CIME_NUM - 1) *
                // RESULTS_BYTES_PER_ENGINE);
                break;
            case OperationMode::PATTERN2_INPUT_DIST_OUTPUT_FUSION:
                // Accumulate E0+E1 into first 64; E2+E3 into next 64
                accumulateResults(
                    all_engine_results_vec, final_output_ptr, 0, 2, 0);  // E0, E1 -> Output[0..63]
                accumulateResults(all_engine_results_vec,
                                  final_output_ptr,
                                  2,
                                  2,
                                  RESULTS_PER_ENGINE);  // E2, E3 -> Output[64..127]
                // Zero out the rest (optional)
                memset(output_block + 2 * RESULTS_BYTES_PER_ENGINE,
                       0,
                       (CIME_NUM - 2) * RESULTS_BYTES_PER_ENGINE);
                break;
            case OperationMode::PATTERN3_INPUT_DIST:
                // Concatenate all results directly
                concatenateResults(all_engine_results_vec, final_output_ptr);
                break;
                // No default needed as all enum values are handled
        }
    }
    else
    {
        // Ensure output buffer is zeroed on failure
        memset(output_block, 0, TOTAL_OUTPUT_BYTES);
        return false;
    }

    return true;  // Computation successful
}

// --- Output Aggregation Helpers ---

void DcimCluster::accumulateResults(
    const std::vector<uint32_t>& all_engine_results,  // Flat vector [R0, R1, R2, R3]
    uint32_t* final_output,        // Pointer to start of final output buffer section
    size_t start_engine,           // Index of first engine result to include (0 or 2)
    size_t num_engines_to_sum,     // Number of engines to sum (4 or 2)
    size_t output_offset_elements  // Offset in final_output (0 or 64)
)
{
    if (!final_output)
        return;

    uint32_t* current_output_ptr = final_output + output_offset_elements;
    memset(current_output_ptr,
           0,
           RESULTS_PER_ENGINE * BYTES_PER_RESULT);  // Initialize target section to zero

    float result_sum_float = 0.0f;
    float temp_float = 0.0f;

    for (size_t j = 0; j < RESULTS_PER_ENGINE; ++j)
    {                             // Iterate through each of the 64 result elements
        result_sum_float = 0.0f;  // Reset sum for this element index

        for (size_t i = 0; i < num_engines_to_sum; ++i)
        {
            size_t engine_idx = start_engine + i;
            // Calculate the index in the flat all_engine_results vector
            size_t result_idx = (engine_idx * RESULTS_PER_ENGINE) + j;

            if (result_idx < all_engine_results.size())
            {
                // Convert uint32_t (assumed FP32 bit pattern) to float for accumulation
                // mh2f_conv(all_engine_results[result_idx], FP32, &temp_float); // Assuming FP32
                // result format
                memcpy(&temp_float, all_engine_results.data() + result_idx, sizeof(uint32_t));
                result_sum_float += temp_float;
            }
            else
            {
                fprintf(stderr,
                        "Warning: Index out of bounds during accumulation (idx=%zu, size=%zu).\n",
                        result_idx,
                        all_engine_results.size());
            }
        }
        // Convert the final float sum back to uint32_t (FP32 bit pattern)
        // current_output_ptr[j] = f2mh_conv(result_sum_float, FP32); // Assuming FP32 result format
        memcpy(current_output_ptr + j, &result_sum_float, sizeof(uint32_t));
    }
}

void DcimCluster::concatenateResults(
    const std::vector<uint32_t>& all_engine_results,  // Flat vector [R0, R1, R2, R3]
    uint32_t* final_output                            // Pointer to start of final output buffer
)
{
    if (!final_output)
        return;

    // Simple memory copy since the temporary vector layout matches the desired output layout
    if (all_engine_results.size() == CIME_NUM * RESULTS_PER_ENGINE)
    {
        memcpy(final_output, all_engine_results.data(), TOTAL_OUTPUT_BYTES);
    }
    else
    {
        fprintf(stderr,
                "Error: Mismatch in expected result size during concatenation (expected %zu, got "
                "%zu).\n",
                CIME_NUM * RESULTS_PER_ENGINE,
                all_engine_results.size());
        memset(final_output, 0, TOTAL_OUTPUT_BYTES);  // Zero out on error
    }
}

// --- Implementation for local_mem compatible linear access ---

bool DcimCluster::parseClusterWordOffset(uint64_t cluster_word_offset,
                                         uint32_t& engine_idx,
                                         uint32_t& macro_idx_in_engine,
                                         uint32_t& row_idx,
                                         uint32_t& page_idx) const
{
    // Check bounds based on the total words defined by the local_mem layout
    if (cluster_word_offset >= TOTAL_CLUSTER_WORDS)
    {
        fprintf(stderr,
                "Error: cluster_word_offset (%lu) is out of bounds [0, %lu] in "
                "parseClusterWordOffset.\n",
                cluster_word_offset,
                TOTAL_CLUSTER_WORDS - 1);
        return false;
    }

    // Calculate indices based on the local_mem layout constants
    engine_idx = cluster_word_offset / CLUSTER_WORDS_PER_ENGINE;
    uint64_t word_offset_in_engine = cluster_word_offset % CLUSTER_WORDS_PER_ENGINE;

    macro_idx_in_engine = word_offset_in_engine / CLUSTER_WORDS_PER_MACRO;
    uint64_t word_offset_in_macro = word_offset_in_engine % CLUSTER_WORDS_PER_MACRO;

    // IMPORTANT: This matches local_mem layout (Page-major within Row)
    row_idx = word_offset_in_macro / CLUSTER_PAGES_PER_MACRO_ROW;
    page_idx = word_offset_in_macro % CLUSTER_PAGES_PER_MACRO_ROW;

    // Optional: Add detailed checks for calculated indices against constants
    // if (engine_idx >= CIME_NUM || macro_idx_in_engine >= MACROS_PER_ENGINE || ...) return false;

    return true;
}

bool DcimCluster::readWordLinear(uint64_t cluster_word_offset, uint8_t* word_buffer)
{
    if (word_buffer == nullptr)
    {
        fprintf(stderr, "Error: Null word_buffer passed to DcimCluster::readWordLinear.\n");
        return false;
    }

    uint32_t engine_idx, macro_idx, row_idx, page_idx;

    // Parse the linear offset using the local_mem layout definition
    if (!parseClusterWordOffset(cluster_word_offset, engine_idx, macro_idx, row_idx, page_idx))
    {
        // Error already printed in parseClusterWordOffset
        memset(word_buffer, 0, DCIM_NUM_BYTES_PER_ROW);  // Clear buffer on error
        return false;
    }

    // Call the existing readRow function which accesses the DcimArray's internal storage
    // Note: We pass page_idx and row_idx as calculated by the parser.
    // The underlying DcimArray::readRow uses these indices with its [page][row] storage.
    return readRow(engine_idx, macro_idx, page_idx, row_idx, word_buffer);
}

bool DcimCluster::writeWordLinear(uint64_t cluster_word_offset, const uint8_t* word_buffer)
{
    if (word_buffer == nullptr)
    {
        fprintf(stderr, "Error: Null word_buffer passed to DcimCluster::writeWordLinear.\n");
        return false;
    }

    uint32_t engine_idx, macro_idx, row_idx, page_idx;

    // Parse the linear offset using the local_mem layout definition
    if (!parseClusterWordOffset(cluster_word_offset, engine_idx, macro_idx, row_idx, page_idx))
    {
        // Error already printed in parseClusterWordOffset
        return false;
    }

    // Call the existing writeRow function which accesses the DcimArray's internal storage
    // Note: We pass page_idx and row_idx as calculated by the parser.
    // The underlying DcimArray::writeRow uses these indices with its [page][row] storage.
    return writeRow(engine_idx, macro_idx, page_idx, row_idx, word_buffer);
}
