#include "../inc/dcim_compute.h"
#include <stdio.h>
#include <string.h>           // For memcpy, memset
#include "../inc/dcim_com.h"  // Includes data_fmt enum and dcim_macro_com prototype
#include "../inc/dcim_extract.h"
#include "../inc/dcim_storage.h"

/**
 * @brief Performs the full vector-matrix multiplication using data from a specific
 *        page in the DCIM storage, assuming pre-aligned input.
 */
void dcim_compute_from_storage_aligned_input(
    const uint8_t* aligned_input_vector,  // Input vector is pre-aligned (size 33*sizeof(uint16_t))
    uint8_t in_type,
    int page_idx,
    uint8_t wt_type,
    uint8_t* result_vector  // Output vector (size 64*sizeof(uint32_t))
)
{
    // --- Input Validation ---
    if (aligned_input_vector == NULL || result_vector == NULL)
    {
        fprintf(stderr, "Error: Null pointer passed to dcim_compute_from_storage_aligned_input.\n");
        if (result_vector != NULL)
            memset(result_vector, 0, 64 * sizeof(uint32_t));
        return;
    }
    if (page_idx < 0 || page_idx >= DCIM_NUM_PAGES)
    {
        fprintf(stderr, "Error: Invalid page index %d provided.\n", page_idx);
        memset(result_vector, 0, 64 * sizeof(uint32_t));
        return;
    }

    // --- Determine Max Columns for Weight Type ---
    int max_columns = 0;
    switch (wt_type)
    {
        case INT4:
            max_columns = 64;
            break;
        case INT8:
        case FP8E4:
        case FP8E5:
            max_columns = 32;
            break;
        case INT12:  // Fallthrough - treat as 16 for now
        case INT16:
        case FP16:
        case BF16:
        case BBF16:
            max_columns = 16;
            break;
        default:
            fprintf(stderr, "Error: Unsupported weight type (%d) in dcim_compute.\n", wt_type);
            memset(result_vector, 0, 64 * sizeof(uint32_t));
            return;
    }
    if (wt_type == INT12)
    {
        fprintf(stderr, "Warning: INT12 computation assumes simple packing within uint16_t.\n");
    }

    // --- Allocate Buffers ---
    uint8_t page_buffer[DCIM_NUM_ROWS_PER_PAGE][DCIM_NUM_BYTES_PER_ROW];
    uint16_t weight_arg_for_macro[DCIM_NUM_ROWS_PER_PAGE];  // Argument for dcim_macro_com (size 33)

    // --- Read Weight Page from Storage ---
    read_storage_page(page_idx, &page_buffer[0][0]);

    // --- Main Computation Loop (Iterate through 64 output elements) ---
    for (int j = 0; j < 64; ++j)
    {
        // Check if the current column index is valid for the weight type
        if (j >= max_columns)
        {
            ((uint32_t*)result_vector)[j] =
                0;  // Pad with zero if column doesn't exist for this type
            continue;
        }

        // Extract the full aligned weight column (33 elements) directly into the argument buffer
        extract_aligned_weight_column(page_buffer, wt_type, j, weight_arg_for_macro);

        // Call the core computation function
        ((uint32_t*)result_vector)[j] = dcim_macro_com(
            weight_arg_for_macro,             // Aligned weight column + exponent (size 33)
            wt_type,                          // Weight data type
            (uint16_t*)aligned_input_vector,  // Cast uint8_t* to uint16_t* for aligned input vector
            in_type                           // Input data type
        );
    }
}

// --- Optional Helper Function Implementation ---

void dcim_compute_from_storage_raw_input(
    const uint8_t* raw_input_vector,  // Size 32*sizeof(uint16_t)
    uint8_t in_type,
    int page_idx,
    uint8_t wt_type,
    uint8_t* result_vector  // Size 64*sizeof(uint32_t)
)
{
    if (raw_input_vector == NULL)
    {
        fprintf(stderr, "Error: Null raw_input_vector passed.\n");
        if (result_vector != NULL)
            memset(result_vector, 0, 64 * sizeof(uint32_t));
        return;
    }

    uint16_t aligned_input_vector[DCIM_NUM_ROWS_PER_PAGE];  // Size 33

    // Align the raw input vector
    float_data_align((uint16_t*)raw_input_vector, in_type, aligned_input_vector);

    // Call the main computation function with the aligned input
    dcim_compute_from_storage_aligned_input(
        (uint8_t*)aligned_input_vector, in_type, page_idx, wt_type, result_vector);
}
