#include "../inc/dcim_engine.hpp"
#include <cstdio>   // For fprintf, stderr
#include <cstring>  // For memset, memcpy
#include <iostream>
#include <numeric>    // For std::accumulate (optional for summing)
#include <stdexcept>  // For std::invalid_argument in constructor
#include <vector>     // For std::vector

// --- Constructor ---
DcimEngine::DcimEngine(size_t num_macros)
{
    if (num_macros == 0)
    {
        // Throw an exception as constructing with 0 macros is likely an error
        throw std::invalid_argument("DcimEngine requires at least one macro (num_macros > 0).");
    }
    // Resize the vector to hold the specified number of DcimArray objects.
    // DcimArray's default constructor will be called for each element.
    macros.resize(num_macros);

// Check if the constructed size matches the compile-time define (if consistency is desired)
#ifdef DCIM_ENGINE_N_MACROS
    if (num_macros != DCIM_ENGINE_N_MACROS)
    {
        fprintf(stderr,
                "Warning: DcimEngine constructed with %zu macros, but DCIM_ENGINE_N_MACROS is "
                "defined as %d.\n",
                num_macros,
                DCIM_ENGINE_N_MACROS);
    }
#endif
}

// --- Destructor ---
DcimEngine::~DcimEngine()
{
    // std::vector handles the destruction of DcimArray objects automatically.
}

// --- Private Helpers ---
bool DcimEngine::isValidMacroIdx(size_t macro_idx) const
{
    return (macro_idx < macros.size());
}

// --- Public Methods ---

// --- Storage Management (Unchanged) ---

bool DcimEngine::writePage(size_t macro_idx, int page_idx, const uint8_t* page_buffer)
{
    if (!isValidMacroIdx(macro_idx))
    {
        fprintf(stderr, "Error: Invalid macro index %zu in DcimEngine::writePage.\n", macro_idx);
        return false;
    }
    return macros[macro_idx].writePage(page_idx, page_buffer);
}

bool DcimEngine::readPage(size_t macro_idx, int page_idx, uint8_t* page_buffer)
{
    if (!isValidMacroIdx(macro_idx))
    {
        fprintf(stderr, "Error: Invalid macro index %zu in DcimEngine::readPage.\n", macro_idx);
        if (page_buffer != nullptr)
        {
            memset(page_buffer, 0, DCIM_NUM_ROWS_PER_PAGE * DCIM_NUM_BYTES_PER_ROW);
        }
        return false;
    }
    return macros[macro_idx].readPage(page_idx, page_buffer);
}

bool DcimEngine::writeRow(size_t macro_idx, int page_idx, int row_idx, const uint8_t* row_buffer)
{
    if (!isValidMacroIdx(macro_idx))
    {
        fprintf(stderr, "Error: Invalid macro index %zu in DcimEngine::writeRow.\n", macro_idx);
        return false;
    }
    return macros[macro_idx].writeRow(page_idx, row_idx, row_buffer);
}

bool DcimEngine::readRow(size_t macro_idx, int page_idx, int row_idx, uint8_t* row_buffer)
{
    if (!isValidMacroIdx(macro_idx))
    {
        fprintf(stderr, "Error: Invalid macro index %zu in DcimEngine::readRow.\n", macro_idx);
        if (row_buffer != nullptr)
        {
            memset(row_buffer, 0, DCIM_NUM_BYTES_PER_ROW);
        }
        return false;
    }
    return macros[macro_idx].readRow(page_idx, row_idx, row_buffer);
}

// --- Computation ---

bool DcimEngine::computeAlignedInput(
    const uint8_t* aligned_input_block,  // Size N * 33 * sizeof(uint16_t)
    uint8_t in_type,
    int page_idx,
    uint8_t wt_type,
    uint8_t* result_vector  // Size 64 * sizeof(uint32_t)
)
{
    // --- Basic Input Validation ---
    if (aligned_input_block == nullptr || result_vector == nullptr)
    {
        fprintf(stderr, "Error: Null pointer passed to DcimEngine::computeAlignedInput.\n");
        if (result_vector != nullptr)
            memset(result_vector, 0, 64 * sizeof(uint32_t));
        return false;
    }

    const size_t num_macros = macros.size();
    if (num_macros == 0)
    {
        fprintf(stderr, "Error: DcimEngine has no macros configured in computeAlignedInput.\n");
        memset(result_vector, 0, 64 * sizeof(uint32_t));
        return false;
    }

    // --- Page Index Validation (Rely on DcimArray::compute) ---
    // DcimArray::compute performs its own page index validation.

    // --- Temporary Result Buffers ---
    // Allocate a buffer to hold results from each macro temporarily.
    // Using std::vector for dynamic allocation based on num_macros.
    std::vector<uint32_t> temp_results(num_macros * 64);  // N * 64 results
    bool all_computes_successful = true;

    // --- Compute per Macro ---
    const size_t aligned_input_bytes_per_macro =
        ALIGNED_INPUT_ELEMENTS_PER_MACRO * BYTES_PER_ELEMENT;  // 33 * 2

    for (size_t i = 0; i < num_macros; ++i)
    {
        const uint8_t* current_aligned_input =
            aligned_input_block + (i * aligned_input_bytes_per_macro);
        uint8_t* current_temp_result_output =
            reinterpret_cast<uint8_t*>(temp_results.data() + (i * 64));
        // Print current_aligned_input, interpreting every two bytes as a uint16_t value
        // const uint16_t* aligned_input_ptr = reinterpret_cast<const
        // uint16_t*>(current_aligned_input); for (int j = 0; j < 33; j++) {
        //     std::cout << "macro " << i << " current_aligned_input[" << j << "] : " <<
        //     (int16_t)aligned_input_ptr[j] << std::endl;
        // }
        std::cout << std::endl;
        bool success = macros[i].compute(
            current_aligned_input, in_type, page_idx, wt_type, current_temp_result_output);
        // if (success){

        //     // Interpret the uint8_t buffer as an array of float (each float is 4 bytes)
        //     const float* result_ptr = reinterpret_cast<const float*>(current_temp_result_output);
        //     for (int j = 0; j < 64; j++) {
        //         // Print the floating-point value
        //         std::cout << "macro " << i << " current_temp_result_output[" << j << "] : " <<
        //         result_ptr[j] << std::endl;
        //     }
        //     std::cout << std::endl;
        // }
        if (!success)
        {
            fprintf(
                stderr,
                "Error: Computation failed in macro %zu within DcimEngine::computeAlignedInput.\n",
                i);
            all_computes_successful = false;
            // Optionally break early, or continue to see if others fail
            // break;
        }
    }

    // --- Sum Results ---
    if (all_computes_successful)
    {
        uint32_t* final_result_ptr = reinterpret_cast<uint32_t*>(result_vector);
        memset(final_result_ptr, 0, 64 * sizeof(uint32_t));  // Initialize final result to zero
        float result_sum = 0;
        float float_res = 0;
        for (int j = 0; j < 64; ++j)
        {
            result_sum = 0;
            for (size_t i = 0; i < num_macros; ++i)
            {
                const uint32_t* current_temp_result = temp_results.data() + (i * 64);
                // mh2f_conv(current_temp_result[j], FP32, &float_res);
                memcpy(&float_res, current_temp_result + j, sizeof(uint32_t));
                result_sum += float_res;
                // std::cout << "macro " << i << " : " << float_res << " ";
            }
            // std::cout << "result_sum : " << result_sum << std::endl;
            // final_result_ptr[j] = f2mh_conv(result_sum, FP32);
            memcpy(final_result_ptr + j, &result_sum, sizeof(uint32_t));
        }
    }
    else
    {
        // Ensure result vector is zeroed on failure
        memset(result_vector, 0, 64 * sizeof(uint32_t));
        return false;
    }

    return true;  // Computation successful
}

bool DcimEngine::computeRawInput(const uint8_t* raw_input_vector,  // Size N * 32 * sizeof(uint16_t)
                                 uint8_t in_type,
                                 int page_idx,
                                 uint8_t wt_type,
                                 uint8_t* result_vector  // Size 64 * sizeof(uint32_t)
)
{
    // --- Basic Input Validation ---
    if (raw_input_vector == nullptr || result_vector == nullptr)
    {
        fprintf(stderr, "Error: Null pointer passed to DcimEngine::computeRawInput.\n");
        if (result_vector != nullptr)
            memset(result_vector, 0, 64 * sizeof(uint32_t));
        return false;
    }

    const size_t num_macros = macros.size();
    if (num_macros == 0)
    {
        fprintf(stderr, "Error: DcimEngine has no macros configured in computeRawInput.\n");
        memset(result_vector, 0, 64 * sizeof(uint32_t));
        return false;
    }

    // --- Alignment ---
    // Allocate a single block for all aligned inputs.
    const size_t aligned_input_bytes_per_macro =
        ALIGNED_INPUT_ELEMENTS_PER_MACRO * BYTES_PER_ELEMENT;  // 33 * 2
    std::vector<uint8_t> aligned_input_block(num_macros * aligned_input_bytes_per_macro);

    const size_t raw_input_bytes_per_macro =
        RAW_INPUT_ELEMENTS_PER_MACRO * BYTES_PER_ELEMENT;  // 32 * 2

    for (size_t i = 0; i < num_macros; ++i)
    {
        const uint8_t* current_raw_input = raw_input_vector + (i * raw_input_bytes_per_macro);
        uint8_t* current_aligned_output =
            aligned_input_block.data() + (i * aligned_input_bytes_per_macro);

        // Align each part of the input vector using the respective macro's helper
        if (in_type < 4)
        {
            memcpy(current_aligned_output, current_raw_input, 32 * sizeof(uint16_t));
            current_aligned_output[32 * 2] = 0;
            current_aligned_output[32 * 2 + 1] = 0;
        }
        else
        {
            macros[i].alignInputVector(current_raw_input, in_type, current_aligned_output);
        }
    }

    // --- Call computeAlignedInput ---
    // Delegate the actual computation to the function handling aligned input.
    return computeAlignedInput(
        aligned_input_block.data(), in_type, page_idx, wt_type, result_vector);
}

// --- Getters ---

size_t DcimEngine::getNumMacros() const
{
    return macros.size();
}