<!DOCTYPE html><html><head>
      <title>cim_cluster</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:////home/<USER>/.cursor-server/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/katex/katex.min.css">
      
      
      <script type="text/javascript" src="file:////home/<USER>/.cursor-server/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/mermaid/mermaid.min.js" charset="UTF-8"></script>
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:Consolas,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<h1 id="dcim-cluster-cimc-架构文档">DCIM Cluster (CIMC) 架构文档 </h1>
<h2 id="1-说明">1. 说明 </h2>
<h3 id="11-设计目标">1.1 设计目标 </h3>
<p><code>DcimCluster</code> (CIMC) 类在多个 <code>DcimEngine</code> 实例之上提供了一个更高级别的抽象。其目标是协调四个 <code>DcimEngine</code>（每个包含 <code>DCIM_ENGINE_N_MACROS</code> = 2 个 <code>DcimArray</code> 宏单元）以执行大规模向量-矩阵计算，遵循特定的操作模式，从而简化应用层的集成。</p>
<h3 id="12-核心特性">1.2 核心特性 </h3>
<ul>
<li><strong>多引擎管理 (Multi-Engine Management)</strong>: 管理一个包含 4 个 <code>DcimEngine</code> 实例的固定集群。</li>
<li><strong>可配置操作模式 (Configurable Operational Modes)</strong>: 支持不同的输入分发和输出聚合模式：
<ul>
<li><code>PATTERN1_OUTPUT_FUSION</code> (模式1 - 输出融合): 所有引擎计算，结果累加。</li>
<li><code>PATTERN2_INPUT_DIST_OUTPUT_FUSION</code> (模式2 - 输入分发与输出融合): 成对输入分发，成对输出累加。</li>
<li><code>PATTERN3_INPUT_DIST</code> (模式3 - 输入分发): 广播输入，拼接输出。</li>
</ul>
</li>
<li><strong>统一接口 (Unified Interface)</strong>: 提供一致的存储访问和计算方法，隐藏多引擎协调的复杂性。</li>
<li><strong>标准化输入 (Standardized Input)</strong>: 接受一个足以容纳所有引擎数据的固定大小输入块，简化调用者逻辑。内部处理根据所选模式进行调整。</li>
<li><strong>利用底层功能 (Leverages Lower Layers)</strong>: 基于 <code>DcimEngine</code> 和 <code>DcimArray</code> 已验证的功能构建。</li>
</ul>
<h2 id="2-系统架构">2. 系统架构 </h2>
<p><code>DcimCluster</code> 位于 <code>DcimEngine</code> 层之上，负责协调多个引擎。</p>
<div class="mermaid">graph TD
    A[应用层] --&gt; Cluster[DCIM C++接口 - DcimCluster 类];

    subgraph DcimCluster 内部
        Cluster -- 管理 --&gt; E0["engines[0]: DcimEngine"];
        Cluster -- 管理 --&gt; E1["engines[1]: DcimEngine"];
        Cluster -- 管理 --&gt; E2["engines[2]: DcimEngine"];
        Cluster -- 管理 --&gt; E3["engines[3]: DcimEngine"];
        Cluster -- 包含 --&gt; Config["内部状态 (例如 current_mode)"];
        Cluster -- 包含 --&gt; ComputeLogic["Cluster 计算协调逻辑"];
        Cluster -- 包含 --&gt; StorageAccess["Cluster 存储访问接口"];
    end

    StorageAccess --&gt; E0;
    StorageAccess --&gt; E1;
    StorageAccess --&gt; E2;
    StorageAccess --&gt; E3;

    ComputeLogic -- 选择输入 &amp; 调用 --&gt; E0;
    ComputeLogic -- 选择输入 &amp; 调用 --&gt; E1;
    ComputeLogic -- 选择输入 &amp; 调用 --&gt; E2;
    ComputeLogic -- 选择输入 &amp; 调用 --&gt; E3;
    ComputeLogic -- 聚合/拼接 --&gt; OutputBuffer[输出缓冲区];


    E0 --&gt; EngCore["DcimEngine Core"];


    EngCore --&gt; M0["macros[0]: DcimArray"];
    EngCore --&gt; M1["macros[1]: DcimArray"];

    style Cluster fill:#ccf,stroke:#333,stroke-width:2px
    style E0 fill:#aae,stroke:#333,stroke-width:1px
    style E1 fill:#aae,stroke:#333,stroke-width:1px
    style E2 fill:#aae,stroke:#333,stroke-width:1px
    style E3 fill:#aae,stroke:#333,stroke-width:1px
</div><ul>
<li><strong>应用层 (Application Layer)</strong>: 与 <code>DcimCluster</code> 交互，进行配置、数据加载和计算请求。</li>
<li><strong>DcimCluster</strong>:
<ul>
<li>持有 4 个 <code>DcimEngine</code> 实例。</li>
<li>存储当前的 <code>OperationMode</code> (操作模式)。</li>
<li>提供存储访问方法 (<code>writePage</code>, <code>readPage</code> 等)，这些方法将请求路由到指定的引擎和宏单元。</li>
<li>提供计算方法 (<code>computeAlignedInput</code>, <code>computeRawInput</code>)，它们：
<ul>
<li>接受一个完整的输入块（大小足以容纳 4 个引擎的数据）。</li>
<li>根据当前模式内部确定使用输入块的哪些部分。</li>
<li>将计算任务分派给相关的 <code>DcimEngine</code> 实例。</li>
<li>根据模式聚合或拼接结果到输出缓冲区。</li>
</ul>
</li>
</ul>
</li>
<li><strong>DcimEngine</strong>: 每个实例管理其 <code>DCIM_ENGINE_N_MACROS</code> (2) 个 <code>DcimArray</code>，并执行 <code>dcim_engine.md</code> 中描述的组合计算。</li>
</ul>
<h2 id="3-操作模式与数据流">3. 操作模式与数据流 </h2>
<p>集群通过 <code>setOperationMode</code> 设置三种操作模式之一。<code>compute</code> 函数总是期望一个完整的输入缓冲区，但会根据活动模式以不同方式处理它。</p>
<p><strong>常量:</strong></p>
<ul>
<li><code>CIME_NUM = 4</code> (集群中的引擎数量)</li>
<li><code>MACROS_PER_ENGINE = 2</code> (<code>DCIM_ENGINE_N_MACROS</code>)</li>
<li><code>RAW_ELEMENTS_PER_MACRO = 32</code> (每个宏的原始输入元素数)</li>
<li><code>ALIGNED_ELEMENTS_PER_MACRO = 33</code> (每个宏的对齐输入元素数)</li>
<li><code>RESULTS_PER_ENGINE = 64</code> (每个引擎的结果数)</li>
</ul>
<p><strong>输入缓冲区大小:</strong></p>
<ul>
<li><code>computeRawInput</code> 期望 <code>CIME_NUM * MACROS_PER_ENGINE * RAW_ELEMENTS_PER_MACRO</code> = <code>4 * 2 * 32 = 256</code> 个 <code>uint16_t</code> 元素。</li>
<li><code>computeAlignedInput</code> 期望 <code>CIME_NUM * MACROS_PER_ENGINE * ALIGNED_ELEMENTS_PER_MACRO</code> = <code>4 * 2 * 33 = 264</code> 个 <code>uint16_t</code> 元素。</li>
</ul>
<p><strong>输出缓冲区大小:</strong></p>
<ul>
<li>传递给 <code>compute</code> 的输出缓冲区必须足够大以容纳最大可能的输出 (模式3): <code>CIME_NUM * RESULTS_PER_ENGINE</code> = <code>4 * 64 = 256</code> 个 <code>uint32_t</code> 元素。</li>
</ul>
<h3 id="31-模式-1-输出融合-output-fusion">3.1 模式 1: 输出融合 (Output Fusion) </h3>
<ul>
<li><strong>使用输入</strong>: 输入块中对应于引擎 0, 1, 2, 3 的数据。</li>
<li><strong>计算</strong>: 所有 4 个引擎使用各自的输入执行计算。</li>
<li><strong>输出</strong>: 所有 4 个引擎的结果 (每个 64*<code>uint32_t</code>) 被累加 (使用浮点转换进行元素求和) 到输出缓冲区的前 64*<code>uint32_t</code> 部分。</li>
<li><strong>输出大小</strong>: 64 * <code>uint32_t</code>.</li>
</ul>
<div class="mermaid">graph TD
    subgraph DcimCluster_Pattern1["DcimCluster (模式: PATTERN1)"]
        Input["输入块 "]
        Input --&gt; SelectInput["选择 E0, E1, E2, E3 的输入"]

        SelectInput -- 输入 E0 --&gt; E0[引擎 0 计算]
        SelectInput -- 输入 E1 --&gt; E1[引擎 1 计算]
        SelectInput -- 输入 E2 --&gt; E2[引擎 2 计算]
        SelectInput -- 输入 E3 --&gt; E3[引擎 3 计算]

        E0 --&gt; R0["结果 0 (64*uint32_t)"]
        E1 --&gt; R1["结果 1 (64*uint32_t)"]
        E2 --&gt; R2["结果 2 (64*uint32_t)"]
        E3 --&gt; R3["结果 3 (64*uint32_t)"]

        R0 --&gt; Accumulate["累加所有结果 (0+1+2+3)"]
        R1 --&gt; Accumulate
        R2 --&gt; Accumulate
        R3 --&gt; Accumulate
        Accumulate --&gt; Output["输出缓冲区 (写入前 64*uint32_t)"]
    end
    style Input fill:#ccffcc
    style Output fill:#ffddaa
</div><h3 id="32-模式-2-输入分发与输出融合-input-distribution--output-fusion">3.2 模式 2: 输入分发与输出融合 (Input Distribution &amp; Output Fusion) </h3>
<ul>
<li><strong>使用输入</strong>: 输入块中对应于引擎 0 (用于 E0, E1) 和引擎 2 (用于 E2, E3) 的数据。块中引擎 1 和引擎 3 的数据被忽略。</li>
<li><strong>计算</strong>: 引擎 0 和 1 使用输入 E0 进行计算。引擎 2 和 3 使用输入 E2 进行计算。</li>
<li><strong>输出</strong>: E0 和 E1 的结果累加到输出缓冲区的前 64*<code>uint32_t</code> 部分。E2 和 E3 的结果累加到输出缓冲区的接下来 64*<code>uint32_t</code> 部分。</li>
<li><strong>输出大小</strong>: 128 * <code>uint32_t</code>.</li>
</ul>
<div class="mermaid">graph TD
    subgraph DcimCluster_Pattern2["DcimCluster (模式: PATTERN2)"]
        Input["输入块 "]
        Input --&gt; SelectInput["选择 E0, E2 的输入"]

        SelectInput -- 输入 E0 --&gt; E0[引擎 0 计算]
        SelectInput -- 输入 E0 --&gt; E1[引擎 1 计算]
        SelectInput -- 输入 E2 --&gt; E2[引擎 2 计算]
        SelectInput -- 输入 E2 --&gt; E3[引擎 3 计算]

        E0 --&gt; R0["结果 0"]
        E1 --&gt; R1["结果 1"]
        E2 --&gt; R2["结果 2"]
        E3 --&gt; R3["结果 3"]

        R0 --&gt; Accumulate01["累加 0+1"]
        R1 --&gt; Accumulate01
        R2 --&gt; Accumulate23["累加 2+3"]
        R3 --&gt; Accumulate23

        Accumulate01 --&gt; Output1["输出缓冲区 (写入前 64*uint32_t)"]
        Accumulate23 --&gt; Output2["输出缓冲区 (写入后 64*uint32_t)"]

    end
    style Input fill:#ccffcc
    style Output1 fill:#ffddaa
    style Output2 fill:#ffddaa
</div><h3 id="33-模式-3-输入分发-input-distribution">3.3 模式 3: 输入分发 (Input Distribution) </h3>
<ul>
<li><strong>使用输入</strong>: 输入块中对应于引擎 0 的数据。块中引擎 1, 2, 3 的数据被忽略。</li>
<li><strong>计算</strong>: 所有 4 个引擎使用广播的输入 E0 执行计算。</li>
<li><strong>输出</strong>: E0, E1, E2, E3 的结果直接拼接到输出缓冲区中。</li>
<li><strong>输出大小</strong>: 256 * <code>uint32_t</code>.</li>
</ul>
<div class="mermaid">graph TD
    subgraph DcimCluster_Pattern3["DcimCluster (模式: PATTERN3)"]
        Input["输入块 "]
        Input --&gt; SelectInput["选择 E0 的输入"]

        SelectInput -- 输入 E0 --&gt; E0[引擎 0 计算]
        SelectInput -- 输入 E0 --&gt; E1[引擎 1 计算]
        SelectInput -- 输入 E0 --&gt; E2[引擎 2 计算]
        SelectInput -- 输入 E0 --&gt; E3[引擎 3 计算]

        E0 --&gt; R0["结果 0 (64*uint32_t)"]
        E1 --&gt; R1["结果 1 (64*uint32_t)"]
        E2 --&gt; R2["结果 2 (64*uint32_t)"]
        E3 --&gt; R3["结果 3 (64*uint32_t)"]

        R0 --&gt; Concatenate["拼接结果 [R0, R1, R2, R3]"]
        R1 --&gt; Concatenate
        R2 --&gt; Concatenate
        R3 --&gt; Concatenate
        Concatenate --&gt; Output["输出缓冲区 (写入 256*uint32_t)"]
    end
    style Input fill:#ccffcc
    style Output fill:#ffddaa
</div><h2 id="4-类接口-概念">4. 类接口 (概念) </h2>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;vector&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;cstdint&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">"dcim_engine.hpp"</span> <span class="token comment">// 假设 DcimEngine 在此定义</span></span>

<span class="token keyword keyword-class">class</span> <span class="token class-name">DcimCluster</span> <span class="token punctuation">{</span>
<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token comment">// 定义操作模式的枚举</span>
    <span class="token keyword keyword-enum">enum</span> <span class="token keyword keyword-class">class</span> <span class="token class-name">OperationMode</span> <span class="token punctuation">{</span>
        PATTERN1_OUTPUT_FUSION<span class="token punctuation">,</span>          <span class="token comment">// 模式1: 输出融合</span>
        PATTERN2_INPUT_DIST_OUTPUT_FUSION<span class="token punctuation">,</span> <span class="token comment">// 模式2: 输入分发 &amp; 输出融合</span>
        PATTERN3_INPUT_DIST              <span class="token comment">// 模式3: 输入分发</span>
    <span class="token punctuation">}</span><span class="token punctuation">;</span>

    <span class="token comment">/**
     * @brief 构造函数。初始化 4 个 DcimEngine 实例。
     */</span>
    <span class="token function">DcimCluster</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">/**
     * @brief 析构函数。
     */</span>
    <span class="token operator">~</span><span class="token function">DcimCluster</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// 禁用拷贝构造和赋值操作</span>
    <span class="token function">DcimCluster</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> DcimCluster<span class="token operator">&amp;</span><span class="token punctuation">)</span> <span class="token operator">=</span> <span class="token keyword keyword-delete">delete</span><span class="token punctuation">;</span>
    DcimCluster<span class="token operator">&amp;</span> <span class="token keyword keyword-operator">operator</span><span class="token operator">=</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> DcimCluster<span class="token operator">&amp;</span><span class="token punctuation">)</span> <span class="token operator">=</span> <span class="token keyword keyword-delete">delete</span><span class="token punctuation">;</span>

    <span class="token comment">/**
     * @brief 设置后续计算调用的操作模式。
     * @param mode 所需的 OperationMode。
     */</span>
    <span class="token keyword keyword-void">void</span> <span class="token function">setOperationMode</span><span class="token punctuation">(</span>OperationMode mode<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">/**
     * @brief 获取当前配置的操作模式。
     * @return 当前的 OperationMode。
     */</span>
    OperationMode <span class="token function">getOperationMode</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span><span class="token punctuation">;</span>

    <span class="token comment">// --- 存储管理 (传递给特定引擎和宏单元) ---</span>

    <span class="token comment">/**
     * @brief 将预对齐数据写入引擎内特定宏单元的整个页面。
     * @param engine_idx 目标引擎索引 (0 到 3)。
     * @param macro_idx 引擎内目标宏单元索引 (0 到 MACROS_PER_ENGINE - 1)。
     * @param page_idx 宏单元内页面索引 (0 到 DCIM_NUM_PAGES - 1)。
     * @param page_buffer 包含对齐页面数据的缓冲区。
     * @return 如果写入成功返回 true，否则返回 false。
     */</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">writePage</span><span class="token punctuation">(</span>size_t engine_idx<span class="token punctuation">,</span> size_t macro_idx<span class="token punctuation">,</span> <span class="token keyword keyword-int">int</span> page_idx<span class="token punctuation">,</span> <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token operator">*</span>page_buffer<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">/**
     * @brief 从引擎内特定宏单元的存储中读取整个页面。
     * @param engine_idx 目标引擎索引 (0 到 3)。
     * @param macro_idx 引擎内目标宏单元索引 (0 到 MACROS_PER_ENGINE - 1)。
     * @param page_idx 宏单元内页面索引 (0 到 DCIM_NUM_PAGES - 1)。
     * @param page_buffer 用于存储读取页面数据的缓冲区。
     * @return 如果读取成功返回 true，否则返回 false。
     */</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">readPage</span><span class="token punctuation">(</span>size_t engine_idx<span class="token punctuation">,</span> size_t macro_idx<span class="token punctuation">,</span> <span class="token keyword keyword-int">int</span> page_idx<span class="token punctuation">,</span> <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token operator">*</span>page_buffer<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">/**
     * @brief 将预对齐数据写入引擎内特定宏单元的特定页面的特定行。
     * @param engine_idx 目标引擎索引 (0 到 3)。
     * @param macro_idx 引擎内目标宏单元索引 (0 到 MACROS_PER_ENGINE - 1)。
     * @param page_idx 宏单元内页面索引 (0 到 DCIM_NUM_PAGES - 1)。
     * @param row_idx 页面内行索引 (0 到 DCIM_NUM_ROWS_PER_PAGE - 1)。
     * @param row_buffer 包含对齐行数据的缓冲区。
     * @return 如果写入成功返回 true，否则返回 false。
     */</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">writeRow</span><span class="token punctuation">(</span>size_t engine_idx<span class="token punctuation">,</span> size_t macro_idx<span class="token punctuation">,</span> <span class="token keyword keyword-int">int</span> page_idx<span class="token punctuation">,</span> <span class="token keyword keyword-int">int</span> row_idx<span class="token punctuation">,</span> <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token operator">*</span>row_buffer<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">/**
     * @brief 从引擎内特定宏单元的特定页面的特定行读取数据。
     * @param engine_idx 目标引擎索引 (0 到 3)。
     * @param macro_idx 引擎内目标宏单元索引 (0 到 MACROS_PER_ENGINE - 1)。
     * @param page_idx 宏单元内页面索引 (0 到 DCIM_NUM_PAGES - 1)。
     * @param row_idx 页面内行索引 (0 到 DCIM_NUM_ROWS_PER_PAGE - 1)。
     * @param row_buffer 用于存储读取行数据的缓冲区。
     * @return 如果读取成功返回 true，否则返回 false。
     */</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">readRow</span><span class="token punctuation">(</span>size_t engine_idx<span class="token punctuation">,</span> size_t macro_idx<span class="token punctuation">,</span> <span class="token keyword keyword-int">int</span> page_idx<span class="token punctuation">,</span> <span class="token keyword keyword-int">int</span> row_idx<span class="token punctuation">,</span> <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token operator">*</span>row_buffer<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// --- 计算 ---</span>

    <span class="token comment">/**
     * @brief 使用预对齐输入执行集群计算 (基于配置的 OperationMode)。
     * @param aligned_input_block 包含 4 个引擎数据的连续块 (大小 4*MACROS_PER_ENGINE*ALIGNED_ELEMENTS_PER_MACRO*sizeof(uint16_t))。
     * @param in_type 原始输入向量的数据类型。
     * @param page_idx 每个相关引擎存储中使用的页面索引。
     * @param wt_type 每个相关引擎存储中使用的权重数据类型。
     * @param output_block 输出缓冲区 (必须是大小 CIME_NUM*RESULTS_PER_ENGINE*sizeof(uint32_t))。
     * @return 如果成功返回 true，否则返回 false。
     */</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">computeAlignedInput</span><span class="token punctuation">(</span>
        <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token operator">*</span>aligned_input_block<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> in_type<span class="token punctuation">,</span>
        <span class="token keyword keyword-int">int</span> page_idx<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> wt_type<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token operator">*</span>output_block
    <span class="token punctuation">)</span><span class="token punctuation">;</span>

   <span class="token comment">/**
     * @brief 使用原始输入执行集群计算 (内部对齐, 基于配置的 OperationMode)。
     * @param raw_input_block 包含 4 个引擎数据的连续块 (大小 4*MACROS_PER_ENGINE*RAW_ELEMENTS_PER_MACRO*sizeof(uint16_t))。
     * @param in_type 输入向量的数据类型。
     * @param page_idx 每个相关引擎存储中使用的页面索引。
     * @param wt_type 每个相关引擎存储中使用的权重数据类型。
     * @param output_block 输出缓冲区 (必须是大小 CIME_NUM*RESULTS_PER_ENGINE*sizeof(uint32_t))。
     * @return 如果成功返回 true，否则返回 false。
     */</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">computeRawInput</span><span class="token punctuation">(</span>
        <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token operator">*</span>raw_input_block<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> in_type<span class="token punctuation">,</span>
        <span class="token keyword keyword-int">int</span> page_idx<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> wt_type<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token operator">*</span>output_block
    <span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// --- 常量 ---</span>
    <span class="token comment">// 公开以便外部使用/验证 (如果需要)</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-constexpr">constexpr</span> size_t CIME_NUM <span class="token operator">=</span> <span class="token number">4</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-constexpr">constexpr</span> size_t MACROS_PER_ENGINE <span class="token operator">=</span> DCIM_ENGINE_N_MACROS<span class="token punctuation">;</span> <span class="token comment">// 应为 2</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-constexpr">constexpr</span> size_t RESULTS_PER_ENGINE <span class="token operator">=</span> <span class="token number">64</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-constexpr">constexpr</span> size_t RAW_ELEMENTS_PER_MACRO <span class="token operator">=</span> <span class="token number">32</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-constexpr">constexpr</span> size_t ALIGNED_ELEMENTS_PER_MACRO <span class="token operator">=</span> <span class="token number">33</span><span class="token punctuation">;</span> <span class="token comment">// DCIM_NUM_ROWS_PER_PAGE</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-constexpr">constexpr</span> size_t BYTES_PER_ELEMENT <span class="token operator">=</span> <span class="token keyword keyword-sizeof">sizeof</span><span class="token punctuation">(</span><span class="token keyword keyword-uint16_t">uint16_t</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-constexpr">constexpr</span> size_t BYTES_PER_RESULT <span class="token operator">=</span> <span class="token keyword keyword-sizeof">sizeof</span><span class="token punctuation">(</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// 计算得出的尺寸</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-constexpr">constexpr</span> size_t RAW_INPUT_BYTES_PER_ENGINE <span class="token operator">=</span> MACROS_PER_ENGINE <span class="token operator">*</span> RAW_ELEMENTS_PER_MACRO <span class="token operator">*</span> BYTES_PER_ELEMENT<span class="token punctuation">;</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-constexpr">constexpr</span> size_t ALIGNED_INPUT_BYTES_PER_ENGINE <span class="token operator">=</span> MACROS_PER_ENGINE <span class="token operator">*</span> ALIGNED_ELEMENTS_PER_MACRO <span class="token operator">*</span> BYTES_PER_ELEMENT<span class="token punctuation">;</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-constexpr">constexpr</span> size_t TOTAL_RAW_INPUT_BYTES <span class="token operator">=</span> CIME_NUM <span class="token operator">*</span> RAW_INPUT_BYTES_PER_ENGINE<span class="token punctuation">;</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-constexpr">constexpr</span> size_t TOTAL_ALIGNED_INPUT_BYTES <span class="token operator">=</span> CIME_NUM <span class="token operator">*</span> ALIGNED_INPUT_BYTES_PER_ENGINE<span class="token punctuation">;</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-constexpr">constexpr</span> size_t RESULTS_BYTES_PER_ENGINE <span class="token operator">=</span> RESULTS_PER_ENGINE <span class="token operator">*</span> BYTES_PER_RESULT<span class="token punctuation">;</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-constexpr">constexpr</span> size_t TOTAL_OUTPUT_BYTES <span class="token operator">=</span> CIME_NUM <span class="token operator">*</span> RESULTS_BYTES_PER_ENGINE<span class="token punctuation">;</span>


<span class="token keyword keyword-private">private</span><span class="token operator">:</span>
    std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span>DcimEngine<span class="token operator">&gt;</span> engines<span class="token punctuation">;</span>
    OperationMode current_mode<span class="token punctuation">;</span>

    <span class="token comment">// 用于计算完整块内输入偏移量的辅助函数</span>
    <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> <span class="token function">getRawInputPtrForEngine</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> block_start<span class="token punctuation">,</span> size_t engine_idx<span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> <span class="token function">getAlignedInputPtrForEngine</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span><span class="token operator">*</span> block_start<span class="token punctuation">,</span> size_t engine_idx<span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span><span class="token punctuation">;</span>

    <span class="token comment">// 用于输出聚合的辅助函数</span>
    <span class="token comment">// 接受包含所有引擎结果的扁平向量 [R0, R1, R2, R3]</span>
    <span class="token keyword keyword-void">void</span> <span class="token function">accumulateResults</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&gt;</span><span class="token operator">&amp;</span> all_engine_results<span class="token punctuation">,</span> <span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">*</span> final_output<span class="token punctuation">,</span> size_t start_engine<span class="token punctuation">,</span> size_t num_engines_to_sum<span class="token punctuation">,</span> size_t output_offset_elements<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-void">void</span> <span class="token function">concatenateResults</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span><span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">&gt;</span><span class="token operator">&amp;</span> all_engine_results<span class="token punctuation">,</span> <span class="token keyword keyword-uint32_t">uint32_t</span><span class="token operator">*</span> final_output<span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// 由 computeRawInput 和 computeAlignedInput 共享的内部计算逻辑</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">computeInternal</span><span class="token punctuation">(</span>
        <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token operator">*</span>aligned_input_block<span class="token punctuation">,</span> <span class="token comment">// 总是接收对齐的输入块</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> in_type<span class="token punctuation">,</span>
        <span class="token keyword keyword-int">int</span> page_idx<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> wt_type<span class="token punctuation">,</span>
        <span class="token keyword keyword-uint8_t">uint8_t</span> <span class="token operator">*</span>output_block <span class="token comment">// 最终输出缓冲区</span>
    <span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// 用于检查引擎索引有效性的辅助函数</span>
    <span class="token keyword keyword-bool">bool</span> <span class="token function">isValidEngineIdx</span><span class="token punctuation">(</span>size_t engine_idx<span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span><span class="token punctuation">;</span>

<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre>
      </div>
      
      
    
    
    <script type="module">
// TODO: If ZenUML gets integrated into mermaid in the future,
//      we can remove the following lines.


var MERMAID_CONFIG = ({"startOnLoad":false});
if (typeof MERMAID_CONFIG !== 'undefined') {
  MERMAID_CONFIG.startOnLoad = false
  MERMAID_CONFIG.cloneCssStyles = false
  MERMAID_CONFIG.theme = "default"
}

mermaid.initialize(MERMAID_CONFIG || {})
if (typeof(window['Reveal']) !== 'undefined') {
  function mermaidRevealHelper(event) {
    var currentSlide = event.currentSlide
    var diagrams = currentSlide.querySelectorAll('.mermaid')
    for (var i = 0; i < diagrams.length; i++) {
      var diagram = diagrams[i]
      if (!diagram.hasAttribute('data-processed')) {
        mermaid.init(null, diagram, ()=> {
          Reveal.slide(event.indexh, event.indexv)
        })
      }
    }
  }
  Reveal.addEventListener('slidetransitionend', mermaidRevealHelper)
  Reveal.addEventListener('ready', mermaidRevealHelper)
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
} else {
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
}
</script>
    
    
    
  
    </body></html>