#pragma once

#include <systemc>
#include <tlm>
#include <vector>
#include <memory>
#include <random>
#include <cstring>
#include <string>
#include <iomanip>
#include <sstream>

#include "../feat_tmu.h"
#include "../../local_mem/local_mem.h"
#include "../../cim_cluster/inc/dcim_cluster.hpp"
#include "npu_config.h"
#include "utils/register.h"
#include "tlm_utils/simple_initiator_socket.h"

namespace feat_tmu_test {

// TMU配置结构
struct TMUTestConfig : public hardware_instruction::TensorManipConfig {
    // 便利函数
    uint32_t get_data_width_bits() const {
        return (cfg_wd == 2) ? 4 : ((cfg_wd == 3) ? 8 : ((cfg_wd == 4) ? 16 : 32));
    }
    
    uint32_t get_data_width_bytes() const {
        return (get_data_width_bits() + 7) / 8;
    }
    
    size_t get_total_elements() const {
        return static_cast<size_t>(cfg_size_dim0b) * cfg_size_dim1 * cfg_size_dim2;
    }
    
    size_t get_total_bytes() const {
        return get_total_elements() * 32;
    }
    
    // 获取块大小 (dim1 x dim0b)
    std::pair<uint32_t, uint32_t> get_block_size() const {
        switch (cfg_wd) {
            case 0: return {64, 1}; // 4bit: 64x1
            case 1: return {32, 2}; // 8bit: 32x2  
            case 2: return {16, 4}; // 16bit: 16x4
            default: return {16, 4};
        }
    }
};

// 测试数据模式
enum class PatternType {
    ZEROS,
    ONES,
    INCREMENTAL,
    ALTERNATING,
    RANDOM,
    SPECIFIC_VALUE
};

// 测试结果结构体
struct TestResult {
    std::string test_name;
    bool passed;
    std::string error_message;
};

// TMU测试基类
class TMUTestBase : public sc_core::sc_module {
public:
    tlm_utils::simple_initiator_socket<TMUTestBase> cfg_socket;

    TMUTestBase(sc_core::sc_module_name name, feat_tmu::FeatTMU* tmu, npu_sc::LocalMemory* memory);

protected:
    // 测试生命周期
    void setup();
    void teardown();
    using IssueQueueCmd = instruction::IssueQueueCmd;
    
    SC_HAS_PROCESS(TMUTestBase);
    
    
    // TMU模块接口
    void configure_tmu(const TMUTestConfig& config);
    void send_command(const IssueQueueCmd& cmd);
    void wait_completion();
    
    // 内存操作 - 使用local_mem
    bool write_memory(uint64_t addr, const std::vector<uint8_t>& data);
    std::vector<uint8_t> read_memory(uint64_t addr, size_t len);
    void clear_memory_region(uint64_t addr, size_t len);
    
    // 地址计算辅助
    uint64_t get_spad_address(uint32_t unit_id, uint64_t word_offset);
    uint64_t get_cimc_address(uint64_t word_offset);
    
    // 验证工具
    bool verify_exact_match(const std::vector<uint8_t>& actual, const std::vector<uint8_t>& expected, std::string& error_msg);
    bool verify_broadcast_result(uint64_t base_addr, uint64_t scalar_val, const TMUTestConfig& config, std::string& error_msg);
    bool verify_pattern_at_address(uint64_t addr, uint8_t expected_value, 
                                  size_t num_elements, uint32_t element_size);
    
    // 测试数据生成
    std::vector<uint8_t> generate_test_pattern(PatternType type, size_t size, 
                                              uint32_t bit_width, uint64_t value = 0);
    std::vector<uint8_t> generate_random_data(size_t size, uint32_t bit_width);
    
    // 数据转换工具
    std::vector<uint8_t> scalar_to_bytes(uint64_t scalar, uint32_t bit_width);
    uint64_t bytes_to_scalar(const std::vector<uint8_t>& bytes, uint32_t bit_width);
    void configure_tmu_bc(uint64_t base_addr_lmem,uint64_t scalar_in);
    
    // 广播测试函数（在sc_thread中执行）
    bool run_broadcast_test_impl(const TMUTestConfig& config, uint64_t scalar_value,
                                uint64_t output_addr, const std::string& test_name);

protected:
    feat_tmu::FeatTMU* m_tmu;
    npu_sc::LocalMemory* m_memory;
    
    std::mt19937 m_rng;
    
    // 地址常量
    static const uint64_t SPAD_BASE = 0x0;
    static const uint64_t CIMC_BASE = (static_cast<uint64_t>(NPUConfig::SPAD_NUM) << (NPUConfig::LMEM_OFFSET + 5));
    static const size_t MAX_TENSOR_SIZE = 1024 * 1024; // 1MB
    
    // 测试统计
    struct TestStats {
        uint32_t tests_run = 0;
        uint32_t tests_passed = 0;
        uint32_t tests_failed = 0;
    } m_stats;

    std::vector<TestResult> test_results;
};

} // namespace feat_tmu_test 