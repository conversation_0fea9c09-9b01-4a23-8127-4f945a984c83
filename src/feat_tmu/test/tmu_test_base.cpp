#include "tmu_test_base.h"
#include <algorithm>
#include <iomanip>
#include <sstream>
using namespace sc_core;
using namespace npu_sc;
using namespace instruction;
namespace feat_tmu_test {

TMUTestBase::TMUTestBase(sc_module_name name, feat_tmu::FeatTMU* tmu, LocalMemory* memory)
    : sc_module(name), m_tmu(tmu), m_memory(memory) {
    cfg_socket.bind(m_tmu->cfg_socket);
    tmu->mem_socket.bind(memory->target_socket);
}

void TMUTestBase::setup() {
    m_rng.seed(std::random_device{}());
    test_results.clear();
    m_memory->reset();
    std::cout << "--- TMU Test Setup ---" << std::endl;
}

void TMUTestBase::teardown() {
    std::cout << "\n===== TMU Test Results =====\n";
    int passed_count = 0;
    for (const auto& result : test_results) {
        std::cout << (result.passed ? "[PASS] " : "[FAIL] ") << result.test_name;
        if (!result.passed) {
            std::cout << " - " << result.error_message;
        }
        std::cout << std::endl;
        if (result.passed) passed_count++;
    }
    std::cout << "\nPassed " << passed_count << "/" << test_results.size() << " tests\n";
    std::cout << "--- TMU Test Teardown ---" << std::endl;
}

void TMUTestBase::send_command(const IssueQueueCmd& cmd) {
    tlm::tlm_generic_payload trans;
    sc_time delay = SC_ZERO_TIME;
    trans.set_command(tlm::TLM_WRITE_COMMAND);
    trans.set_data_ptr(reinterpret_cast<uint8_t*>(const_cast<IssueQueueCmd*>(&cmd)));
    trans.set_data_length(sizeof(IssueQueueCmd));
    trans.set_streaming_width(sizeof(IssueQueueCmd));
    trans.set_byte_enable_ptr(0);
    trans.set_dmi_allowed(false);
    trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);
    cfg_socket->b_transport(trans, delay);
    if (trans.is_response_error()) {
        SC_REPORT_ERROR("TMUTestBase", "Command send failed");
    }
}

void TMUTestBase::configure_tmu(const TMUTestConfig& config) {
    feat_tmu::FeatTMU::TMUConfigManager cfg_manager;
    cfg_manager.setConfig(config);
    IssueQueueCmd cfg_cmd;
    for (int i = 0; i < 7; i++) {
        cfg_cmd.funct7 = opcode::TM_CFG;
        cfg_cmd.rs1val = i;
        cfg_cmd.rs2val = cfg_manager.readReg(i);
        send_command(cfg_cmd);
        wait(0, SC_NS);
    }
}

void TMUTestBase::configure_tmu_bc(uint64_t base_addr_lmem, uint64_t scalar_in) {
    IssueQueueCmd cmd;
    cmd.funct7 = opcode::BC_PRE;
    cmd.rs1val = scalar_in;
    send_command(cmd);
    wait(0, SC_NS);
    cmd.funct7 = opcode::BC_DRV;
    cmd.rs1val = base_addr_lmem;
    send_command(cmd);
    wait(0, SC_NS);
}

void TMUTestBase::wait_completion() {
    wait(100, SC_NS);
}

bool TMUTestBase::write_memory(uint64_t addr, const std::vector<uint8_t>& data) {
    size_t unit_index;
    uint64_t word_offset;
    if (!decode(addr, unit_index, word_offset)) return false;

    size_t bytes_written = 0;
    while (bytes_written < data.size()) {
        Word256b word_data;
        word_data.fill(0);
        size_t bytes_to_copy = std::min(data.size() - bytes_written, static_cast<size_t>(32));
        std::memcpy(word_data.data(), data.data() + bytes_written, bytes_to_copy);
        if (!m_memory->write_word(unit_index, word_offset++, word_data)) return false;
        bytes_written += bytes_to_copy;
    }
    return true;
}

std::vector<uint8_t> TMUTestBase::read_memory(uint64_t addr, size_t len) {
    std::vector<uint8_t> result;
    result.reserve(len);
    size_t unit_index;
    uint64_t word_offset;
    if (!decode(addr, unit_index, word_offset)) return {};

    size_t bytes_read = 0;
    while (bytes_read < len) {
        Word256b word_data;
        if (!m_memory->read_word(unit_index, word_offset++, word_data)) return {};
        size_t bytes_to_copy = std::min(len - bytes_read, static_cast<size_t>(32));
        result.insert(result.end(), word_data.begin(), word_data.begin() + bytes_to_copy);
        bytes_read += bytes_to_copy;
    }
    return result;
}

void TMUTestBase::clear_memory_region(uint64_t addr, size_t len) {
    std::vector<uint8_t> zeros(len, 0);
    write_memory(addr, zeros);
}

uint64_t TMUTestBase::get_spad_address(uint32_t unit_id, uint64_t word_offset) {
    return make_address(unit_id, word_offset);
}

uint64_t TMUTestBase::get_cimc_address(uint64_t word_offset) {
    return make_address(NPUConfig::SPAD_NUM, word_offset);
}

bool TMUTestBase::verify_exact_match(const std::vector<uint8_t>& actual, const std::vector<uint8_t>& expected, std::string& error_msg) {
    if (actual.size() != expected.size()) {
        error_msg = "Size mismatch: expected " + std::to_string(expected.size()) + ", got " + std::to_string(actual.size());
        return false;
    }
    for (size_t i = 0; i < actual.size(); ++i) {
        if (actual[i] != expected[i]) {
            std::stringstream ss;
            ss << "Data mismatch at byte " << i << ": expected 0x" << std::hex << (int)expected[i]
               << ", got 0x" << (int)actual[i];
            error_msg = ss.str();
            return false;
        }
    }
    return true;
}

bool TMUTestBase::verify_broadcast_result(uint64_t base_addr, uint64_t scalar_val, const TMUTestConfig& config, std::string& error_msg) {
    auto expected_data = generate_test_pattern(PatternType::SPECIFIC_VALUE, config.get_total_bytes(), config.get_data_width_bits(), scalar_val);
    auto actual_data = read_memory(base_addr, config.get_total_bytes());
    return verify_exact_match(actual_data, expected_data, error_msg);
}

bool TMUTestBase::verify_pattern_at_address(uint64_t addr, uint8_t expected_value, 
                                           size_t num_elements, uint32_t element_size) {
    size_t total_bytes = num_elements * element_size;
    auto actual_data = read_memory(addr, total_bytes);
    
    for (size_t i = 0; i < total_bytes; ++i) {
        if (actual_data[i] != expected_value) {
            std::cerr << "Pattern verification failed at byte " << i 
                      << ": expected 0x" << std::hex << (int)expected_value
                      << ", got 0x" << (int)actual_data[i] << std::dec << std::endl;
            return false;
        }
    }
    
    return true;
}

std::vector<uint8_t> TMUTestBase::generate_test_pattern(PatternType type, size_t size, 
                                                       uint32_t bit_width, uint64_t value) {
    std::vector<uint8_t> result(size);
    
    switch (type) {
        case PatternType::ZEROS:
            std::fill(result.begin(), result.end(), 0);
            break;
            
        case PatternType::ONES:
            std::fill(result.begin(), result.end(), 0xFF);
            break;
            
        case PatternType::INCREMENTAL:
            for (size_t i = 0; i < size; ++i) {
                result[i] = static_cast<uint8_t>(i & 0xFF);
            }
            break;
            
        case PatternType::ALTERNATING:
            for (size_t i = 0; i < size; ++i) {
                result[i] = (i % 2) ? 0xAA : 0x55;
            }
            break;
            
        case PatternType::RANDOM:
            return generate_random_data(size, bit_width);
            
        case PatternType::SPECIFIC_VALUE: {
            // 生成与广播逻辑一致的期望数据
            switch (bit_width) {
                case 4: {
                    // 4位数据：每个字节包含2个元素，与广播逻辑一致
                    uint8_t val_4bit = static_cast<uint8_t>(value & 0xF);
                    uint8_t packed_val = (val_4bit << 4) | val_4bit; // 将4位值打包成一个字节
                    std::fill(result.begin(), result.end(), packed_val);
                    break;
                }
                case 8: {
                    // 8位数据：每个字节一个元素
                    uint8_t val_8bit = static_cast<uint8_t>(value & 0xFF);
                    std::fill(result.begin(), result.end(), val_8bit);
                    break;
                }
                case 16: {
                    // 16位数据：每2个字节一个元素，小端序
                    uint16_t val_16bit = static_cast<uint16_t>(value & 0xFFFF);
                    for (size_t i = 0; i < size; i += 2) {
                        result[i] = static_cast<uint8_t>(val_16bit & 0xFF);
                        if (i + 1 < size) {
                            result[i + 1] = static_cast<uint8_t>((val_16bit >> 8) & 0xFF);
                        }
                    }
                    break;
                }
                case 32: {
                    // 32位数据：每4个字节一个元素，小端序
                    uint32_t val_32bit = static_cast<uint32_t>(value & 0xFFFFFFFF);
                    for (size_t i = 0; i < size; i += 4) {
                        result[i] = static_cast<uint8_t>(val_32bit & 0xFF);
                        if (i + 1 < size) {
                            result[i + 1] = static_cast<uint8_t>((val_32bit >> 8) & 0xFF);
                        }
                        if (i + 2 < size) {
                            result[i + 2] = static_cast<uint8_t>((val_32bit >> 16) & 0xFF);
                        }
                        if (i + 3 < size) {
                            result[i + 3] = static_cast<uint8_t>((val_32bit >> 24) & 0xFF);
                        }
                    }
                    break;
                }
                default: {
                    // 默认行为：使用原有的scalar_to_bytes逻辑
                    auto scalar_bytes = scalar_to_bytes(value, bit_width);
                    size_t element_size = scalar_bytes.size();
                    
                    for (size_t i = 0; i < size; i += element_size) {
                        size_t copy_size = std::min(element_size, size - i);
                        std::memcpy(result.data() + i, scalar_bytes.data(), copy_size);
                    }
                    break;
                }
            }
            break;
        }
    }
    
    return result;
}

std::vector<uint8_t> TMUTestBase::generate_random_data(size_t size, uint32_t bit_width) {
    std::vector<uint8_t> result(size);
    std::uniform_int_distribution<uint8_t> dist(0, 255);
    
    for (size_t i = 0; i < size; ++i) {
        result[i] = dist(m_rng);
    }
    
    return result;
}

std::vector<uint8_t> TMUTestBase::scalar_to_bytes(uint64_t scalar, uint32_t bit_width) {
    size_t byte_count = (bit_width + 7) / 8;
    std::vector<uint8_t> result(byte_count);
    
    for (size_t i = 0; i < byte_count; ++i) {
        result[i] = static_cast<uint8_t>((scalar >> (i * 8)) & 0xFF);
    }
    
    return result;
}

uint64_t TMUTestBase::bytes_to_scalar(const std::vector<uint8_t>& bytes, uint32_t bit_width) {
    uint64_t result = 0;
    size_t byte_count = std::min(bytes.size(), static_cast<size_t>((bit_width + 7) / 8));
    
    for (size_t i = 0; i < byte_count; ++i) {
        result |= static_cast<uint64_t>(bytes[i]) << (i * 8);
    }
    
    return result;
}




} // namespace feat_tmu_test 