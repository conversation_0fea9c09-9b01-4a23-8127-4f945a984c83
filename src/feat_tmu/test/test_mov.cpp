#include <iostream>
#include <vector>
#include <string>
#include <memory>
#include <random>
#include <cstring>
#include <systemc>
#include <tlm>
#include <iomanip>

#include "../feat_tmu.h"
#include "../../local_mem/local_mem.h"
#include "../../cim_cluster/inc/dcim_cluster.hpp"
#include "npu_config.h"
#include "sc_logger.h"
#include "utils/register.h"
#include "tlm_utils/simple_initiator_socket.h"
#include "tmu_test_base.h"

using namespace sc_core;
using namespace npu_sc;
using namespace instruction;
using namespace feat_tmu_test;

namespace feat_tmu_test {

class TMUMoveTest : public TMUTestBase {
public:
    SC_HAS_PROCESS(TMUMoveTest);
    TMUMoveTest(sc_module_name name, feat_tmu::FeatTMU* tmu, LocalMemory* memory)
        : TMUTestBase(name, tmu, memory)
    {
        SC_THREAD(test_thread);
    }

private:
    void test_thread() {
        setup();
        
        test_BasicMove_SameStride();
        test_Move_INT4();
        test_Move_INT16();
        test_MoveWithBlockBoundary();
        test_MoveWithRemainder();
        test_MoveSPADToCIMC();
        test_MoveComplexLayout();
        
        teardown();
    }

    void run_move_test(const std::string& test_name, 
                      const TMUTestConfig& config,
                      uint64_t input_addr, 
                      uint64_t output_addr,
                      const std::vector<uint8_t>& input_data) {
        std::cout << "Running test: " << test_name << std::endl;
        
        // 1. 配置TMU
        configure_tmu(config);
        
        // 2. 写入测试数据到输入地址
        write_memory(input_addr, input_data);
        
        // 3. 清空输出区域
        clear_memory_region(output_addr, config.get_total_bytes());
        
        // 4. 执行MOV_DRV命令
        configure_tmu_mov(output_addr, input_addr);
        wait_completion();
        
        // 5. 验证结果
        std::string error_message;
        bool success = verify_move_result(input_addr, output_addr, config, error_message);
        test_results.push_back({test_name, success, error_message});
    }

    void configure_tmu_mov(uint64_t output_addr, uint64_t input_addr) {
        IssueQueueCmd cmd;
        cmd.funct7 = opcode::MOV_DRV;
        cmd.rs1val = output_addr;
        cmd.rs2val = input_addr;
        send_command(cmd);
        wait(0, SC_NS);
    }

    bool verify_move_result(uint64_t input_addr, uint64_t output_addr, 
                           const TMUTestConfig& config, std::string& error_msg) {
        // 读取输入和输出数据
        auto input_data = read_memory(input_addr, config.get_total_bytes());
        auto output_data = read_memory(output_addr, config.get_total_bytes());
        
        // 考虑stride的影响，需要按照逻辑布局比较
        return verify_move_with_strides(input_data, output_data, config, error_msg);
    }

    bool verify_move_with_strides(const std::vector<uint8_t>& input_data,
                                 const std::vector<uint8_t>& output_data,
                                 const TMUTestConfig& config,
                                 std::string& error_msg) {
        // 对于MOV操作，需要按照张量的逻辑布局进行验证
        uint32_t chunk_size = 32; // 256位 = 32字节
        
        for (uint32_t d2 = 0; d2 < config.cfg_size_dim2; ++d2) {
            for (uint32_t d1 = 0; d1 < config.cfg_size_dim1; ++d1) {
                for (uint32_t d0b = 0; d0b < config.cfg_size_dim0b; ++d0b) {
                    // 计算输入地址偏移
                    uint64_t input_offset = (d2 * config.cfg_stride_dim2_in + 
                                           d1 * config.cfg_stride_dim1_in + d0b) * chunk_size;
                    
                    // 计算输出地址偏移
                    uint64_t output_offset = (d2 * config.cfg_stride_dim2_out + 
                                            d1 * config.cfg_stride_dim1_out + d0b) * chunk_size;
                    
                    // 确定要比较的字节数
                    uint32_t bytes_to_compare = chunk_size;
                    if (config.cfg_rem_dim0 > 0 && d0b == config.cfg_size_dim0b - 1) {
                        // 最后一个chunk，只比较有效字节
                        uint32_t data_width = config.get_data_width_bits();
                        if (data_width == 4) {
                            bytes_to_compare = (config.cfg_rem_dim0 + 1) / 2;
                        } else {
                            uint32_t bytes_per_elem = data_width / 8;
                            bytes_to_compare = config.cfg_rem_dim0 * bytes_per_elem;
                        }
                    }
                    
                    // 比较对应的chunk
                    for (uint32_t byte = 0; byte < bytes_to_compare; ++byte) {
                        if (input_offset + byte >= input_data.size() || 
                            output_offset + byte >= output_data.size()) {
                            continue;
                        }
                        
                        uint8_t input_byte = input_data[input_offset + byte];
                        uint8_t output_byte = output_data[output_offset + byte];
                        
                        // 对于INT4的特殊处理
                        if (config.get_data_width_bits() == 4 && 
                            config.cfg_rem_dim0 > 0 && 
                            d0b == config.cfg_size_dim0b - 1 && 
                            byte == bytes_to_compare - 1 && 
                            (config.cfg_rem_dim0 & 1)) {
                            // 最后一个字节只比较低4位
                            input_byte &= 0x0F;
                            output_byte &= 0x0F;
                        }
                        
                        if (input_byte != output_byte) {
                            std::stringstream ss;
                            ss << "Data mismatch at tensor position (" << d2 << "," << d1 << "," << d0b 
                               << ") byte " << byte << ": expected 0x" << std::hex << (int)input_byte
                               << ", got 0x" << (int)output_byte;
                            error_msg = ss.str();
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }



    std::vector<uint8_t> generate_move_test_data(const TMUTestConfig& config) {
        // 使用generate_test_pattern生成基础数据
        uint32_t data_width = config.get_data_width_bits();
        size_t total_size = config.get_total_bytes();
        
        // 生成总大小的增量模式数据
        auto data = generate_test_pattern(PatternType::INCREMENTAL, total_size, data_width);
        
        // 如果有remainder，需要清零无效区域
        if (config.cfg_rem_dim0 > 0) {
            uint32_t chunk_size = 32; // 256位 = 32字节
            
            // 计算有效数据大小：size_dim2 * size_dim1 * (size_dim0b-1) + rem_dim0_chunks
            size_t valid_chunks = config.cfg_size_dim2 * config.cfg_size_dim1 * (config.cfg_size_dim0b - 1);
            
            // 最后一个chunk中的有效字节数
            uint32_t valid_bytes_in_last_chunk = 0;
            if (data_width == 4) {
                // INT4: 2 elements per byte
                valid_bytes_in_last_chunk = (config.cfg_rem_dim0 + 1) / 2;
            } else {
                // Other types
                uint32_t bytes_per_elem = data_width / 8;
                valid_bytes_in_last_chunk = config.cfg_rem_dim0 * bytes_per_elem;
            }
            
            // 清零最后一个chunk的无效部分
            for (uint32_t d2 = 0; d2 < config.cfg_size_dim2; ++d2) {
                for (uint32_t d1 = 0; d1 < config.cfg_size_dim1; ++d1) {
                    // 最后一个chunk在dim0b维度上
                    uint32_t last_d0b = config.cfg_size_dim0b - 1;
                    
                    // 计算最后chunk的起始地址
                    uint64_t chunk_offset = (d2 * config.cfg_stride_dim2_in + 
                                           d1 * config.cfg_stride_dim1_in + last_d0b) * chunk_size;
                    
                    // 清零最后chunk中的无效字节
                    for (uint32_t byte = valid_bytes_in_last_chunk; byte < chunk_size; ++byte) {
                        if (chunk_offset + byte < data.size()) {
                            data[chunk_offset + byte] = 0;
                        }
                    }
                }
            }
        }
        
        return data;
    }

    void test_BasicMove_SameStride() {
        TMUTestConfig config;
        uint32_t type = dtype::INT8;
        config.cfg_type = type >> 3;
        config.cfg_wd = type & 0x7;
        config.cfg_size_dim0b = 2;
        config.cfg_size_dim1 = 4;
        config.cfg_size_dim2 = 2;
        config.cfg_rem_dim0 = 0;
        config.cfg_stride_dim1_in = 2;
        config.cfg_stride_dim2_in = 8;
        config.cfg_stride_dim1_out = 2;
        config.cfg_stride_dim2_out = 8;
        
        auto input_data = generate_move_test_data(config);
        run_move_test("Basic Move Same Stride", config, 
                     get_spad_address(0, 0), get_spad_address(1, 0), input_data);
    }



    void test_Move_INT4() {
        TMUTestConfig config;
        uint32_t type = dtype::INT4;
        config.cfg_type = type >> 3;
        config.cfg_wd = type & 0x7;
        config.cfg_size_dim0b = 2;  // 简化配置：只有2个chunk
        config.cfg_size_dim1 = 4;   // 4行，不会跨块
        config.cfg_size_dim2 = 1;
        config.cfg_rem_dim0 = 0;
        config.cfg_stride_dim1_in = 2;
        config.cfg_stride_dim2_in = 8;
        config.cfg_stride_dim1_out = 2;
        config.cfg_stride_dim2_out = 8;
        
        auto input_data = generate_move_test_data(config);
        run_move_test("Move INT4", config,
                     get_spad_address(0, 0), get_spad_address(2, 0), input_data);
    }

    void test_Move_INT16() {
        TMUTestConfig config;
        uint32_t type = dtype::INT16;
        config.cfg_type = type >> 3;
        config.cfg_wd = type & 0x7;
        config.cfg_size_dim0b = 2;
        config.cfg_size_dim1 = 8;
        config.cfg_size_dim2 = 2;
        config.cfg_rem_dim0 = 0;
        config.cfg_stride_dim1_in = 2;
        config.cfg_stride_dim2_in = 16;
        config.cfg_stride_dim1_out = 2;
        config.cfg_stride_dim2_out = 16;
        
        auto input_data = generate_move_test_data(config);
        run_move_test("Move INT16", config,
                     get_spad_address(0, 0), get_spad_address(1, 0), input_data);
    }

    void test_MoveWithBlockBoundary() {
        TMUTestConfig config;
        uint32_t type = dtype::INT8;
        config.cfg_type = type >> 3;
        config.cfg_wd = type & 0x7;
        config.cfg_size_dim0b = 1;
        config.cfg_size_dim1 = 100;  // 需要2个块 (64 + 36)
        config.cfg_size_dim2 = 1;
        config.cfg_rem_dim0 = 0;
        config.cfg_stride_dim1_in = 1;
        config.cfg_stride_dim2_in = 100;
        config.cfg_stride_dim1_out = 1;
        config.cfg_stride_dim2_out = 100;
        
        auto input_data = generate_move_test_data(config);
        run_move_test("Move Block Boundary", config,
                     get_spad_address(0, 0), get_spad_address(1, 0), input_data);
    }

    void test_MoveWithRemainder() {
        TMUTestConfig config;
        uint32_t type = dtype::INT8;  // 使用INT8简化测试
        config.cfg_type = type >> 3;
        config.cfg_wd = type & 0x7;
        config.cfg_size_dim0b = 2;  // 2个chunk
        config.cfg_size_dim1 = 3;   // 3行
        config.cfg_size_dim2 = 1;
        config.cfg_rem_dim0 = 10;   // 最后chunk中有效元素数（10个字节）
        config.cfg_stride_dim1_in = 2;
        config.cfg_stride_dim2_in = 6;
        config.cfg_stride_dim1_out = 2;
        config.cfg_stride_dim2_out = 6;
        
        auto input_data = generate_move_test_data(config);
        run_move_test("Move With Remainder", config,
                     get_spad_address(0, 0), get_spad_address(1, 0), input_data);
    }

    void test_MoveSPADToCIMC() {
        TMUTestConfig config;
        uint32_t type = dtype::INT16;
        config.cfg_type = type >> 3;
        config.cfg_wd = type & 0x7;
        config.cfg_size_dim0b = 1;
        config.cfg_size_dim1 = 64;
        config.cfg_size_dim2 = 4;
        config.cfg_rem_dim0 = 0;
        config.cfg_stride_dim1_in = 1;
        config.cfg_stride_dim2_in = 64;
        config.cfg_stride_dim1_out = 1;
        config.cfg_stride_dim2_out = 64;
        
        auto input_data = generate_move_test_data(config);
        
        // 写入SPAD
        uint64_t spad_addr = get_spad_address(0, 0);
        write_memory(spad_addr, input_data);
        
        // 配置TMU
        configure_tmu(config);
        
        // 清空CIMC目标区域
        uint64_t cimc_addr = get_cimc_address(0);
        clear_memory_region(cimc_addr, config.get_total_bytes());
        
        // 执行MOV_DRV到CIMC
        configure_tmu_mov(cimc_addr, spad_addr);
        wait_completion();
        
        // 验证CIMC中的数据
        std::string error_message;
        bool success = verify_cimc_move_result(config, input_data, cimc_addr, error_message);
        test_results.push_back({"Move SPAD to CIMC", success, error_message});
    }

    void test_MoveComplexLayout() {
        TMUTestConfig config;
        uint32_t type = dtype::INT8;
        config.cfg_type = type >> 3;
        config.cfg_wd = type & 0x7;
        config.cfg_size_dim0b = 3;
        config.cfg_size_dim1 = 7;
        config.cfg_size_dim2 = 3;
        config.cfg_rem_dim0 = 0;
        config.cfg_stride_dim1_in = 5;   // 不规则输入stride
        config.cfg_stride_dim2_in = 35;
        config.cfg_stride_dim1_out = 3;  // 不规则输出stride
        config.cfg_stride_dim2_out = 21;
        
        auto input_data = generate_move_test_data(config);
        run_move_test("Move Complex Layout", config,
                     get_spad_address(0, 0), get_spad_address(1, 0), input_data);
    }

    bool verify_cimc_move_result(const TMUTestConfig& config, 
                                const std::vector<uint8_t>& input_data,
                                uint64_t cimc_addr, 
                                std::string& error_msg) {
        // 对于SPAD到CIMC的移动，需要按照process_mov中的地址计算方式验证
        uint32_t chunk_size = 32; // 256位 = 32字节
        
        // 根据process_mov的逻辑，MOV使用64x1的块大小
        const uint32_t BLOCK_SIZE_DIM1 = 64;
        const uint32_t BLOCK_SIZE_DIM0B = 1;
        
        uint32_t block_num_dim1 = (config.cfg_size_dim1 + BLOCK_SIZE_DIM1 - 1) / BLOCK_SIZE_DIM1;
        uint32_t block_num_dim0b = config.cfg_size_dim0b;
        
        // 按照process_mov的地址计算方式验证
        for (uint32_t idx_d2 = 0; idx_d2 < config.cfg_size_dim2; ++idx_d2) {
            for (uint32_t block_idx_d1 = 0; block_idx_d1 < block_num_dim1; ++block_idx_d1) {
                for (uint32_t block_idx_d0b = 0; block_idx_d0b < block_num_dim0b; ++block_idx_d0b) {
                    for (uint32_t rel_idx_d1 = 0; rel_idx_d1 < BLOCK_SIZE_DIM1; ++rel_idx_d1) {
                        uint32_t abs_idx_d1 = block_idx_d1 * BLOCK_SIZE_DIM1 + rel_idx_d1;
                        uint32_t abs_idx_d0b = block_idx_d0b;
                        
                        // 跳过超出张量边界的位置
                        if (abs_idx_d1 >= config.cfg_size_dim1) {
                            continue;
                        }
                        
                        // 计算输入地址（SPAD布局）
                        // 注意：process_mov中的调用顺序与函数定义不一致，这里直接计算
                        uint64_t input_offset = (idx_d2 * config.cfg_stride_dim2_in + 
                                               abs_idx_d1 * config.cfg_stride_dim1_in + 
                                               abs_idx_d0b) * chunk_size;
                        
                        // 计算CIMC物理地址偏移
                        uint32_t physical_engine = block_idx_d1;
                        uint32_t physical_macro = rel_idx_d1 / 32;
                        uint32_t physical_row = rel_idx_d1 % 32;
                        uint32_t physical_page = idx_d2;
                        
                        // 使用NPUConfig中的常数计算word offset
                        uint64_t target_word_offset = 
                            (physical_engine * NPUConfig::WORDS_PER_ENGINE) +
                            (physical_macro * NPUConfig::WORDS_PER_MACRO) +
                            (physical_row * NPUConfig::PAGES_PER_MACRO_ROW) + 
                            physical_page;
                        
                        // 转换为字节偏移
                        uint64_t cimc_byte_offset = target_word_offset * chunk_size;
                        
                        // 确定要比较的字节数
                        uint32_t bytes_to_compare = chunk_size;
                        if (config.cfg_rem_dim0 > 0 && abs_idx_d0b == config.cfg_size_dim0b - 1) {
                            uint32_t data_width = config.get_data_width_bits();
                            if (data_width == 4) {
                                bytes_to_compare = (config.cfg_rem_dim0 + 1) / 2;
                            } else {
                                bytes_to_compare = config.cfg_rem_dim0 * (data_width / 8);
                            }
                        }
                        
                        // 读取并比较数据
                        auto cimc_chunk = read_memory(cimc_addr + cimc_byte_offset, chunk_size);
                        
                        for (uint32_t byte = 0; byte < bytes_to_compare; ++byte) {
                            if (input_offset + byte >= input_data.size()) continue;
                            
                            uint8_t input_byte = input_data[input_offset + byte];
                            uint8_t cimc_byte = (byte < cimc_chunk.size()) ? cimc_chunk[byte] : 0;
                            
                            // INT4特殊处理
                            if (config.get_data_width_bits() == 4 && 
                                config.cfg_rem_dim0 > 0 && 
                                abs_idx_d0b == config.cfg_size_dim0b - 1 && 
                                byte == bytes_to_compare - 1 && 
                                (config.cfg_rem_dim0 & 1)) {
                                input_byte &= 0x0F;
                                cimc_byte &= 0x0F;
                            }
                            
                            if (input_byte != cimc_byte) {
                                std::stringstream ss;
                                ss << "CIMC data mismatch at position (" << idx_d2 << "," << abs_idx_d1 
                                   << "," << abs_idx_d0b << ") byte " << byte << ": expected 0x" 
                                   << std::hex << (int)input_byte << ", got 0x" << (int)cimc_byte
                                   << " (E=" << physical_engine << ",M=" << physical_macro 
                                   << ",R=" << physical_row << ",P=" << physical_page << ")";
                                error_msg = ss.str();
                                return false;
                            }
                        }
                    }
                }
            }
        }
        
        return true;
    }
};

} // namespace feat_tmu_test

int sc_main(int argc, char* argv[]) {
    sc_logger::initialize(spdlog::level::debug, "tmu_mov_test.log");

    // Create instances
    DcimCluster dcim_cluster;
    LocalMemory local_mem("local_mem", &dcim_cluster);
    feat_tmu::FeatTMU tmu("feat_tmu");
    TMUMoveTest testbench("tmu_move_testbench", &tmu, &local_mem);

    
    // Start simulation
    std::cout << "Starting SystemC simulation for TMU MOV tests..." << std::endl;
    sc_start(30000, SC_NS); // Increased simulation time for more complex tests
    std::cout << "Simulation completed." << std::endl;

    return 0;
} 