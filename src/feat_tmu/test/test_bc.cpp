#include <iostream>
#include <vector>
#include <string>
#include <memory>
#include <random>
#include <cstring>
#include <systemc>
#include <tlm>
#include <iomanip>

#include "../feat_tmu.h"
#include "../../local_mem/local_mem.h"
#include "../../cim_cluster/inc/dcim_cluster.hpp"
#include "npu_config.h"
#include "sc_logger.h"
#include "utils/register.h"
#include "tlm_utils/simple_initiator_socket.h"
#include "tmu_test_base.h"

using namespace sc_core;
using namespace npu_sc;
using namespace instruction;
using namespace feat_tmu_test;
namespace feat_tmu_test {




class TMUBroadcastTest : public TMUTestBase {
public:
    SC_HAS_PROCESS(TMUBroadcastTest);
    TMUBroadcastTest(sc_module_name name, feat_tmu::FeatTMU* tmu, LocalMemory* memory)
        : TMUTestBase(name, tmu, memory)
    {
        SC_THREAD(test_thread);
    }

private:
    void test_thread() {
        setup();
        
        test_BasicBroadcast_INT8();
        test_BasicBroadcast_INT16();
        test_BasicBroadcast_INT4();
        test_BroadcastWithRemainder();
        
        teardown();
    }

    void run_broadcast_test(const std::string& test_name, const TMUTestConfig& config, uint64_t scalar_value, uint64_t output_addr) {
        std::cout << "Running test: " << test_name << " at addr 0x" << std::hex << output_addr << std::dec << std::endl;
        
        configure_tmu(config);
        clear_memory_region(output_addr, config.get_total_bytes());
        configure_tmu_bc(output_addr, scalar_value);
        wait_completion();

        std::string error_message;
        bool success = verify_broadcast_result(output_addr, scalar_value, config, error_message);
        test_results.push_back({test_name, success, error_message});
    }

    void test_BasicBroadcast_INT8() {
        TMUTestConfig config;
        uint32_t type = dtype::INT8;
        config.cfg_type = type>>3;
        config.cfg_wd = type&0x7;
        config.cfg_size_dim0b = 4;
        config.cfg_size_dim1 = 8; config.cfg_size_dim2 = 2; config.cfg_rem_dim0 = 0;
        config.cfg_stride_dim1_in = 0; config.cfg_stride_dim2_in = 0;
        config.cfg_stride_dim1_out = 4; config.cfg_stride_dim2_out = 32;
        
        std::vector<uint64_t> test_values = {0x00, 0xFF, 0x5A, 0xA5, 0x12, 0x87};
        for (auto val : test_values) {
            run_broadcast_test("INT8 Scalar " + std::to_string(val), config, val, get_spad_address(0, 0));
        }
    }

    void test_BasicBroadcast_INT16() {
        TMUTestConfig config;
        uint32_t type = dtype::INT16;
        config.cfg_type = type>>3;
        config.cfg_wd = type&0x7;
        config.cfg_size_dim0b = 2;
        config.cfg_size_dim1 = 8; config.cfg_size_dim2 = 2; config.cfg_rem_dim0 = 0;
        config.cfg_stride_dim1_in = 0; config.cfg_stride_dim2_in = 0;
        config.cfg_stride_dim1_out = 2; config.cfg_stride_dim2_out = 16;

        std::vector<uint64_t> test_values = {0x0000, 0xFFFF, 0x1234, 0xABCD, 0x5555, 0xAAAA};
        for (auto val : test_values) {
            run_broadcast_test("INT16 Scalar 0x" + std::to_string(val), config, val, get_spad_address(1, 0));
        }
    }

    void test_BasicBroadcast_INT4() {
        TMUTestConfig config;
        uint32_t type = dtype::INT4;
        config.cfg_type = type>>3;
        config.cfg_wd = type&0x7;
        config.cfg_size_dim0b = 8;
        config.cfg_size_dim1 = 16; config.cfg_size_dim2 = 1; config.cfg_rem_dim0 = 0;
        config.cfg_stride_dim1_in = 0; config.cfg_stride_dim2_in = 0;
        config.cfg_stride_dim1_out = 8; config.cfg_stride_dim2_out = 128;
        
        std::vector<uint64_t> test_values = {0x0, 0xF, 0x5, 0xA, 0x7, 0x3};
        for (auto val : test_values) {
            run_broadcast_test("INT4 Scalar 0x" + std::to_string(val), config, val, get_spad_address(2, 0));
        }
    }

    
    void test_BroadcastWithRemainder() {
        TMUTestConfig config;
        uint32_t type = dtype::INT16;
        config.cfg_type = type>>3;
        config.cfg_wd = type&0x7;
        config.cfg_size_dim0b = 3;
        config.cfg_size_dim1 = 5; config.cfg_size_dim2 = 1; config.cfg_rem_dim0 = 10;
        config.cfg_stride_dim1_in = 0; config.cfg_stride_dim2_in = 0;
        config.cfg_stride_dim1_out = 3; config.cfg_stride_dim2_out = 15;
        
        run_broadcast_test("BC with Remainder", config, 0x42, get_spad_address(0, 100));
    }
};

} // namespace feat_tmu_test 

int sc_main(int argc, char* argv[]) {
    sc_logger::initialize(spdlog::level::debug, "tmu_bc_test.log");
    sc_logger::enable_auto_stats();
    // Create instances
    DcimCluster dcim_cluster;
    LocalMemory local_mem("local_mem", &dcim_cluster);
    feat_tmu::FeatTMU tmu("feat_tmu");
    TMUBroadcastTest testbench("tmu_testbench", &tmu, &local_mem);

    
    // Start simulation
    std::cout << "Starting SystemC simulation for TMU..." << std::endl;
    sc_start(20000, SC_NS); // Increased simulation time
    std::cout << "Simulation completed." << std::endl;
    sc_logger::print_id_statistics();
    return 0;
}