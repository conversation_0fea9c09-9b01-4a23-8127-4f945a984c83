target("feat_tmu")
    set_kind("static")
    add_files("feat_tmu.cpp")
    add_headerfiles("feat_tmu.h",{public = true})
    add_deps("common")
    add_deps("local_mem")
    add_deps("sc_logger")
    add_packages("spdlog")
target("tmu_test_base")
    set_kind("static")
    add_files("test/tmu_test_base.cpp")
    add_includedirs("test")
    add_deps("feat_tmu")
    add_packages("spdlog")
target("tmu_bc_test")
    set_kind("binary")
    add_files("test/test_bc.cpp")
    add_deps("tmu_test_base")
    set_group("feat_tmu")
    add_tests("tmu_bc_test")
    add_packages("spdlog")
target("tmu_mov_test")
    set_kind("binary")
    add_files("test/test_mov.cpp")
    add_deps("tmu_test_base")
    set_group("feat_tmu")
    add_tests("tmu_mov_test")   
    add_packages("spdlog")


includes("../local_mem/xmake.lua")