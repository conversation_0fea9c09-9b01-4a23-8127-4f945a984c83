#pragma once
#include <cstdint>
#include <systemc>
#include <tlm>
#include "npu_config.h"
#include "tlm_utils/simple_initiator_socket.h"
#include "tlm_utils/simple_target_socket.h"

#include <iomanip>
#include "utils/register.h"
namespace feat_tmu
{

class FeatTMU : public sc_core::sc_module
{
  public:
    using Word256b = std::array<uint8_t, NPUConfig::LMEM_WD / 8>;
    using IssueQueueCmd = instruction::IssueQueueCmd;
    using TMUConfigManager = hardware_instruction::TmConfigManager;
    using TMUConfig = hardware_instruction::TensorManipConfig;
    // TLM socket for configuration
    using Chunk = Word256b;                // Alias for clarity
    using BlockData = std::vector<Chunk>;  // Buffer to hold chunks of an input block
    // TileSize: Represents the valid dimensions (dim1, dim0b) within an edge block
    using TileSize = std::pair<uint32_t, uint32_t>;
    // BlockIndices: Represents the indices (d2, d1, d0b) of a block
    struct BlockIndices
    {
        uint32_t d2, d1, d0b;
    };

    tlm_utils::simple_target_socket<FeatTMU> cfg_socket;
    tlm_utils::simple_initiator_socket<FeatTMU> mem_socket;
    SC_HAS_PROCESS(FeatTMU);
    FeatTMU(sc_core::sc_module_name name);
    struct MaskInfo
    {
        Word256b mask;
        bool need_mask;
        MaskInfo() : need_mask(false)
        {
            mask.fill(0xFF);
        };
    };

  private:
    tlm::tlm_generic_payload m_trans;
    // Configuration and state
    TMUConfigManager m_cfg;
    TMUConfig m_cfg_tmu;

    // Base addresses
    uint64_t byte_base_in;
    uint64_t byte_base_out;
    uint64_t val_in;

    // Events
    sc_core::sc_event rec_cfg_event;
    sc_core::sc_event drv_event;
    sc_core::sc_time m_delay;
    // 配置
    IssueQueueCmd m_issue_queue_cmd;
    uint8_t m_prec;
    bool m_is_write_to_cim;
    bool m_is_float_type;
    Word256b generate_mask(uint32_t rem_dim0, uint8_t prec) const;
    BlockIndices get_input_block_indices_for_output(
        const BlockIndices& output_block_indices,
        const std::pair<uint32_t, uint32_t>& input_block_dims_pair) const;
    BlockData process_transpose(const BlockData& input_block_buffer,
                                uint32_t data_bit_width,
                                const std::pair<uint32_t, uint32_t>& input_block_dims_pair) const;
    BlockData process_align(const BlockData& input_block_buffer, uint8_t prec) const;
    TileSize calculate_tile_size(uint32_t block_idx_d1,
                                 uint32_t block_idx_d0b,
                                 uint32_t cfg_size_dim1,
                                 uint32_t cfg_size_dim0b,
                                 uint32_t block_num_dim1,
                                 uint32_t block_num_dim0b,
                                 uint32_t block_size_dim1,
                                 uint32_t block_size_dim0b) const;
    MaskInfo calculate_mask_info(uint32_t dim0b) const;
    void write_data(uint64_t write_byte_address, Word256b& data);
    Word256b read_data(uint64_t read_byte_address, MaskInfo& mask_info);
    uint64_t calculate_byte_address(uint64_t byte_base,
                                    uint32_t dim0b,
                                    uint32_t dim1,
                                    uint32_t dim2,
                                    uint32_t stride_dim2,
                                    uint32_t stride_dim1);
    // TLM interface
    void b_transport_cfg(tlm::tlm_generic_payload& trans, sc_core::sc_time& delay);
    std::pair<uint32_t, uint32_t> get_trans_input_block_dims(const TMUConfig& config,
                                                             uint32_t func7) const;
    // Processing threads
    void thread_cfg();
    void thread_drv();

    // Processing functions
    void process_bc();
    void process_mov();
    void process_trans();
    void process_mov_align();
    void process_trans_align();
    void process_set_cim_exp();
    void process_get_cim_exp();
};
}  // namespace feat_tmu