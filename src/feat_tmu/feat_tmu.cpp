#include "feat_tmu.h"
#include <cstdint>
#include <iostream>
#include <sstream>
#include <iomanip>
#include "../cim_cluster/inc/dcim_com.h"
#include "local_mem.h"
#include "npu_config.h"
#include "utils/utils.h"
#include "utils/sc_logger.h"
using namespace dtype;
using Word256b = std::array<uint8_t, NPUConfig::LMEM_WD / 8>;
using namespace npu_sc;

using namespace instruction::opcode;
namespace feat_tmu
{

FeatTMU::FeatTMU(sc_core::sc_module_name name)
    : sc_core::sc_module(name), cfg_socket("cfg_socket"), mem_socket("mem_socket")
{
    SC_INFO("TMU_INIT", "FeatTMU constructor: module_name={}", this->name());
    
    // 连接配置接口
    cfg_socket.register_b_transport(this, &FeatTMU::b_transport_cfg);
    SC_THREAD(thread_cfg);
    SC_THREAD(thread_drv);
    
    SC_INFO("TMU_INIT_OK", "FeatTMU initialization completed");
}

void FeatTMU::b_transport_cfg(tlm::tlm_generic_payload& trans,
                              sc_core::sc_time& delay )
{
    m_issue_queue_cmd = *(IssueQueueCmd*)trans.get_data_ptr();
    trans.set_response_status(tlm::TLM_OK_RESPONSE);
    
    SC_INFO("TMU_CFG", "Received configuration command: funct7={:#x}, rs1val={:#x}, rs2val={:#x}", 
               m_issue_queue_cmd.funct7, m_issue_queue_cmd.rs1val, m_issue_queue_cmd.rs2val);
    
    m_issue_queue_cmd.print(); 
    rec_cfg_event.notify();
}

void FeatTMU::thread_cfg()
{
    while (true)
    {
        wait(rec_cfg_event);
        if (m_issue_queue_cmd.funct7 == TM_CFG)
        {
            SC_DEBUG("TMU_CFG", "TMU config write: reg={:#x}, value={:#x}", 
                        m_issue_queue_cmd.rs1val&0xffff, m_issue_queue_cmd.rs2val);
            m_cfg.writeReg(m_issue_queue_cmd.rs1val&0xffff, m_issue_queue_cmd.rs2val);
        }
        else if (m_issue_queue_cmd.funct7 == BC_PRE)
        {
            val_in = m_issue_queue_cmd.rs1val;
        }
        else if (m_issue_queue_cmd.funct7 == BC_DRV)
        {
            byte_base_out = m_issue_queue_cmd.rs1val;
        }
        else if (m_issue_queue_cmd.funct7 == MOV_DRV || m_issue_queue_cmd.funct7 == TRANS_DRV)
        {
            byte_base_out = m_issue_queue_cmd.rs1val;
            byte_base_in = m_issue_queue_cmd.rs2val;
        }
        else if (m_issue_queue_cmd.funct7 == SET_CIMEXP)
        {
            byte_base_in = m_issue_queue_cmd.rs1val;
            byte_base_out = m_issue_queue_cmd.rs2val;
        }
        else if (m_issue_queue_cmd.funct7 == GET_CIMEXP)
        {
            byte_base_out = m_issue_queue_cmd.rs1val;
            byte_base_in = m_issue_queue_cmd.rs2val;
        }
        // 触发驱动事件
        if (m_issue_queue_cmd.funct7 == BC_DRV || m_issue_queue_cmd.funct7 == MOV_DRV ||
            m_issue_queue_cmd.funct7 == TRANS_DRV || m_issue_queue_cmd.funct7 == SET_CIMEXP ||
            m_issue_queue_cmd.funct7 == GET_CIMEXP)
        {
            m_cfg_tmu = m_cfg.getConfig();
            m_cfg_tmu.print();
            m_prec = static_cast<uint8_t>(m_cfg_tmu.cfg_type << 3 | m_cfg_tmu.cfg_wd);

            size_t base_write_unit_index;
            uint64_t base_write_word_offset;  // Not strictly needed here, but decode provides it
            decode(byte_base_out, base_write_unit_index, base_write_word_offset);
            m_is_write_to_cim = (base_write_unit_index == NPUConfig::LMEM_NUM - 1);
            m_is_float_type = is_float_type(m_prec);
            drv_event.notify();
        }
    }
}

void FeatTMU::thread_drv()
{
    while (true)
    {
        wait(drv_event);
        if (m_issue_queue_cmd.funct7 == BC_DRV)
        {
            process_bc();
        }
        else if (m_issue_queue_cmd.funct7 == MOV_DRV)
        {
            if (m_is_float_type && m_is_write_to_cim)
            {
                process_mov_align();
            }
            else
            {
                process_mov();
            }
        }
        else if (m_issue_queue_cmd.funct7 == TRANS_DRV)
        {
            if (m_is_float_type && m_is_write_to_cim)
            {
                process_trans_align();
            }
            else
            {
                process_trans();
            }
        }
        else if (m_issue_queue_cmd.funct7 == SET_CIMEXP)
        {
            process_set_cim_exp();
        }
        else if (m_issue_queue_cmd.funct7 == GET_CIMEXP)
        {
            process_get_cim_exp();
        }
    }
}

uint64_t FeatTMU::calculate_byte_address(uint64_t byte_base,
                                         uint32_t dim0b,
                                         uint32_t dim1,
                                         uint32_t dim2,
                                         uint32_t stride_dim2,
                                         uint32_t stride_dim1)
{
    return byte_base + (dim2 * stride_dim2 + dim1 * stride_dim1 + dim0b) * NPUConfig::LMEM_WD / 8;
}

void FeatTMU::process_bc()
{
    SC_INFO("TMU_BC", "Starting broadcast operation: val_in={:#x}, dimensions={}x{}x{}", 
               val_in, m_cfg_tmu.cfg_size_dim2, m_cfg_tmu.cfg_size_dim1, m_cfg_tmu.cfg_size_dim0b);
    
    // BC操作不需要读取，直接写入
    Word256b broadcast_data;
    
#ifdef BC_SPECIAL
    // 原有的实现：根据RV_XLEN的值选择合适的拼接方式
    if constexpr (NPUConfig::RV_XLEN == NPUConfig::XLEN::RV64)
    {
        for (int i = 0; i < 4; ++i)
        {
            std::memcpy(&broadcast_data[i * 8], &val_in, 8);
        }
    }
    else if constexpr (NPUConfig::RV_XLEN == NPUConfig::XLEN::RV32)
    {
        uint32_t val_in_32 = static_cast<uint32_t>(val_in);
        for (int i = 0; i < 8; ++i)
        {
            std::memcpy(&broadcast_data[i * 4], &val_in_32, 4);
        }
    }
#else
    // 新的实现：根据位宽信息处理广播
    uint32_t data_width_bits = width_code_to_bits(static_cast<WidthCode>(m_cfg_tmu.cfg_wd));
    uint32_t data_width_bytes = (data_width_bits + 7) / 8;  // 向上取整到字节
    
    // 清零广播数据
    broadcast_data.fill(0);
    
    // 根据不同的位宽处理标量值
    switch (data_width_bits) {
        case 4: {
            // 4位数据：每个字节包含2个元素
            uint8_t val_4bit = static_cast<uint8_t>(val_in & 0xF);
            uint8_t packed_val = (val_4bit << 4) | val_4bit; // 将4位值打包成一个字节
            
            // 填充整个256位数据
            for (size_t i = 0; i < broadcast_data.size(); ++i) {
                broadcast_data[i] = packed_val;
            }
            break;
        }
        case 8: {
            // 8位数据：每个字节一个元素
            uint8_t val_8bit = static_cast<uint8_t>(val_in & 0xFF);
            
            // 填充整个256位数据
            for (size_t i = 0; i < broadcast_data.size(); ++i) {
                broadcast_data[i] = val_8bit;
            }
            break;
        }
        case 16: {
            // 16位数据：每2个字节一个元素
            uint16_t val_16bit = static_cast<uint16_t>(val_in & 0xFFFF);
            
            // 小端序存储16位值
            for (size_t i = 0; i < broadcast_data.size(); i += 2) {
                broadcast_data[i] = static_cast<uint8_t>(val_16bit & 0xFF);
                if (i + 1 < broadcast_data.size()) {
                    broadcast_data[i + 1] = static_cast<uint8_t>((val_16bit >> 8) & 0xFF);
                }
            }
            break;
        }
        case 32: {
            // 32位数据：每4个字节一个元素
            uint32_t val_32bit = static_cast<uint32_t>(val_in & 0xFFFFFFFF);
            
            // 小端序存储32位值
            for (size_t i = 0; i < broadcast_data.size(); i += 4) {
                broadcast_data[i] = static_cast<uint8_t>(val_32bit & 0xFF);
                if (i + 1 < broadcast_data.size()) {
                    broadcast_data[i + 1] = static_cast<uint8_t>((val_32bit >> 8) & 0xFF);
                }
                if (i + 2 < broadcast_data.size()) {
                    broadcast_data[i + 2] = static_cast<uint8_t>((val_32bit >> 16) & 0xFF);
                }
                if (i + 3 < broadcast_data.size()) {
                    broadcast_data[i + 3] = static_cast<uint8_t>((val_32bit >> 24) & 0xFF);
                }
            }
            break;
        }
        default:
            SC_ERROR("TMU_BC_ERROR", "Unsupported data width for broadcast: {} bits", data_width_bits);
            broadcast_data.fill(0);
            break;
    }
#endif
    // Iterate directly over output tensor chunks
    for (uint32_t idx_d2 = 0; idx_d2 < m_cfg_tmu.cfg_size_dim2; ++idx_d2)
    {
        for (uint32_t idx_d1 = 0; idx_d1 < m_cfg_tmu.cfg_size_dim1; ++idx_d1)
        {
            for (uint32_t idx_d0b = 0; idx_d0b < m_cfg_tmu.cfg_size_dim0b; ++idx_d0b)
            {
                // 1. Calculate Write Address
                uint64_t write_address = calculate_byte_address(byte_base_out,
                                                                idx_d0b,
                                                                idx_d1,
                                                                idx_d2,
                                                                m_cfg_tmu.cfg_stride_dim2_out,
                                                                m_cfg_tmu.cfg_stride_dim1_out);
                // 2. Write Data
                write_data(write_address, broadcast_data);
            }
        }
    }
    SC_INFO("TMU_BC_OK", "Broadcast operation completed successfully");
    // Task Complete
}

void FeatTMU::process_mov()
{
    SC_INFO("TMU_MOV", "Starting MOV operation: byte_base_in={:#x}, byte_base_out={:#x}", 
               byte_base_in, byte_base_out);
    
    auto input_block_dims = get_trans_input_block_dims(m_cfg_tmu, m_issue_queue_cmd.funct7);
    const uint32_t BLOCK_SIZE_DIM1 = input_block_dims.second;
    const uint32_t BLOCK_SIZE_DIM0B = input_block_dims.first;
    uint32_t block_num_dim1 = ceil_div(m_cfg_tmu.cfg_size_dim1, BLOCK_SIZE_DIM1);
    // Since BLOCK_SIZE_DIM0B is 1, each dim0b index is its own "block"
    uint32_t block_num_dim0b = m_cfg_tmu.cfg_size_dim0b;
    Word256b input_chunk;  // Holds data read from valid input areas
    Word256b zero_chunk;   // Pre-allocate zero chunk for CIM writes
    zero_chunk.fill(0);

    // Determine target unit index once
    size_t base_write_unit_index;
    uint64_t base_write_word_offset;  // Not strictly needed here, but decode provides it
    bool base_decode_success = decode(byte_base_out, base_write_unit_index, base_write_word_offset);

    if (!base_decode_success)
    {
        SC_ERROR("TMU_MOV_ERROR", "Invalid byte_base_out provided to MOV: {:#x}", byte_base_out);
        return;  // Cannot proceed with invalid base address
    }
    bool is_write_to_cim = (base_write_unit_index == NPUConfig::LMEM_NUM - 1);

    // Iterate over tensor dimensions (dim2 is outermost)
    for (uint32_t idx_d2 = 0; idx_d2 < m_cfg_tmu.cfg_size_dim2; ++idx_d2)
    {  // Logical Page
        // Iterate over blocks in dim1
        for (uint32_t block_idx_d1 = 0; block_idx_d1 < block_num_dim1; ++block_idx_d1)
        {  // Logical Engine
            // Iterate over "blocks" (individual chunks) in dim0b
            for (uint32_t block_idx_d0b = 0; block_idx_d0b < block_num_dim0b; ++block_idx_d0b)
            {  // Logical Chunk Index (always 0 here)

                // Calculate mask info for potential chunk-internal padding (rem_dim0)
                // This mask is used by read_data if reading the last chunk in dim0b
                MaskInfo mask_info = calculate_mask_info(block_idx_d0b);

                // Iterate WITHIN the conceptual 64x1 block (chunk by chunk along dim1)
                for (uint32_t rel_idx_d1 = 0; rel_idx_d1 < BLOCK_SIZE_DIM1; ++rel_idx_d1)
                {  // Logical Row within Engine (0-63)
                    // rel_idx_d0b is always 0 because BLOCK_SIZE_DIM0B is 1

                    // Absolute indices for the current chunk within the *logical* tensor view
                    uint32_t abs_idx_d1 =
                        block_idx_d1 * BLOCK_SIZE_DIM1 + rel_idx_d1;  // Logical Overall Row (0-255)
                    uint32_t abs_idx_d0b = block_idx_d0b;             // Logical Chunk Index (0)

                    // Determine if the *input* location corresponds to padding
                    bool is_input_padding = (abs_idx_d1 >= m_cfg_tmu.cfg_size_dim1);
                    // || abs_idx_d0b >= m_cfg_tmu.cfg_size_dim0b); // d0b check implicit in loop

                    // 1. Read Input Chunk (Only if NOT input padding)
                    if (!is_input_padding)
                    {
                        uint64_t read_address = calculate_byte_address(
                            byte_base_in,
                            abs_idx_d0b,  // dim0b parameter
                            abs_idx_d1,   // dim1 parameter  
                            idx_d2,       // dim2 parameter
                            m_cfg_tmu.cfg_stride_dim2_in,
                            m_cfg_tmu.cfg_stride_dim1_in);
                        // read_data should handle the mask_info internally if needed for rem_dim0
                        input_chunk = read_data(read_address, mask_info);
                    }
                    // If it is input padding, we don't read. input_chunk might hold stale data.

                    // 2. Calculate Write Address based on destination type
                    uint64_t write_address = 0;
                    if (is_write_to_cim)
                    {
                        // Calculate physical CIMC word offset
                        uint32_t physical_engine =
                            block_idx_d1;  // Logical Engine maps to Physical Engine
                        uint32_t physical_macro =
                            rel_idx_d1 / 32;  // Determine Macro within Engine (0 or 1)
                        uint32_t physical_row =
                            rel_idx_d1 % 32;              // Determine Data Row within Macro (0-31)
                        uint32_t physical_page = idx_d2;  // Logical Page maps to Physical Page

                        // Formula: E * WORDS_PER_ENGINE + M * WORDS_PER_MACRO + R *
                        // PAGES_PER_MACRO_ROW + P
                        uint64_t target_word_offset =
                            (physical_engine * NPUConfig::WORDS_PER_ENGINE) +
                            (physical_macro * NPUConfig::WORDS_PER_MACRO) +
                            (physical_row * NPUConfig::PAGES_PER_MACRO_ROW) + physical_page;

                        // Check if calculated offset is valid (optional sanity check)
                        if (target_word_offset >= NPUConfig::TOTAL_WORDS)
                        {
                            SC_WARN("TMU_MOV_OFFSET", "MOV calculated invalid CIMC word_offset: {} for E={} M={} R={} P={}", 
                                       target_word_offset, physical_engine, physical_macro, physical_row, physical_page);
                            continue;  // Skip this write if offset is bad
                        }

                        // Construct the final byte address using the CIMC unit index and calculated
                        // word offset
                        write_address = make_address(base_write_unit_index, target_word_offset);
                    }
                    else
                    {
                        // Destination is SPAD, use standard linear calculation with output strides
                        write_address =
                            calculate_byte_address(byte_base_out,
                                                   abs_idx_d0b,  // dim0b parameter
                                                   abs_idx_d1,   // dim1 parameter
                                                   idx_d2,       // dim2 parameter
                                                   m_cfg_tmu.cfg_stride_dim2_out,
                                                   m_cfg_tmu.cfg_stride_dim1_out);
                    }

                    // 3. Conditional Write based on padding
                    // We don't need to decode again, we already know if it's CIMC or SPAD
                    if (is_input_padding)
                    {
                        // Input was padding
                        if (is_write_to_cim)
                        {
                            // Write zeros to CIM if address is valid
                            if (write_address != 0)
                            {  // Check if address calculation was skipped due to error
                                write_data(write_address, zero_chunk);
                            }
                        }
                        else
                        {
                            // Skip writing padding to SPAD
                        }
                    }
                    else
                    {
                        // Input was valid data
                        // Write actual data to SPAD or CIM if address is valid
                        if (write_address != 0)
                        {
                            write_data(write_address, input_chunk);
                        }
                    }
                }  // End inner loop (rel_idx_d1)
            }  // End block_idx_d0b loop
        }  // End block_idx_d1 loop
    }  // End idx_d2 loop
    // Task Complete
    SC_INFO("TMU_MOV_OK", "MOV operation completed successfully");
}

FeatTMU::TileSize FeatTMU::calculate_tile_size(uint32_t block_idx_d1,
                                               uint32_t block_idx_d0b,
                                               uint32_t cfg_size_dim1,
                                               uint32_t cfg_size_dim0b,
                                               uint32_t block_num_dim1,
                                               uint32_t block_num_dim0b,
                                               uint32_t block_size_dim1,
                                               uint32_t block_size_dim0b) const
{
    bool is_dim1_edge = (block_idx_d1 == block_num_dim1 - 1);
    bool is_dim0b_edge = (block_idx_d0b == block_num_dim0b - 1);

    uint32_t rem_dim1 = cfg_size_dim1 % block_size_dim1;
    uint32_t rem_dim0b = cfg_size_dim0b % block_size_dim0b;

    uint32_t tile_dim1 =
        is_dim1_edge ? (rem_dim1 == 0 ? block_size_dim1 : rem_dim1) : block_size_dim1;
    uint32_t tile_dim0b =
        is_dim0b_edge ? (rem_dim0b == 0 ? block_size_dim0b : rem_dim0b) : block_size_dim0b;

    // Use .first for dim1, .second for dim0b to match std::pair usage elsewhere if TileSize is
    // std::pair
    return {tile_dim1, tile_dim0b};
}

FeatTMU::BlockIndices FeatTMU::get_input_block_indices_for_output(
    const BlockIndices& output_block_indices,
    const std::pair<uint32_t, uint32_t>& input_block_dims_pair) const
{
    // Extract input block dimensions
    uint32_t IN_BLOCK_SIZE_DIM0B = input_block_dims_pair.first;
    uint32_t IN_BLOCK_SIZE_DIM1 = input_block_dims_pair.second;

    // Output block dimensions are fixed
    const uint32_t OUT_BLOCK_SIZE_DIM1 = 64;
    const uint32_t OUT_BLOCK_SIZE_DIM0B = 1;

    // For transpose, we need to map the output block to the corresponding input block
    // In transpose, dim1 and dim0 are swapped, so:
    // - output dim1 (rows) comes from input dim0 (columns)
    // - output dim0 (columns) comes from input dim1 (rows)

    // Calculate the logical start indices of the output block
    uint32_t output_start_dim1 = output_block_indices.d1 * OUT_BLOCK_SIZE_DIM1;
    uint32_t output_start_dim0b = output_block_indices.d0b * OUT_BLOCK_SIZE_DIM0B;

    // These map to the transposed positions in the input tensor
    // output_dim1 -> input_dim0b, output_dim0b -> input_dim1
    uint32_t input_start_dim1 = output_start_dim0b;
    uint32_t input_start_dim0b = output_start_dim1;

    // Convert to block indices by dividing by block dimensions
    BlockIndices input_block_indices;
    input_block_indices.d2 = output_block_indices.d2;  // dim2 is unchanged in transpose
    input_block_indices.d1 = input_start_dim1 / IN_BLOCK_SIZE_DIM1;
    input_block_indices.d0b = input_start_dim0b / IN_BLOCK_SIZE_DIM0B;

    return input_block_indices;
}

// Helper functions for element extraction and placement

/**
 * @brief Extracts a single element from a chunk based on its index and bit width.
 *
 * @param chunk The chunk containing the element.
 * @param element_index Index of the element within the chunk (0-based).
 * @param bit_width Bit width of each element (4, 8, 16).
 * @return uint16_t The extracted element value.
 */
uint16_t extract_element(const FeatTMU::Chunk& chunk, uint32_t element_index, uint32_t bit_width)
{
    // Calculate elements per chunk and validate index
    uint32_t elements_per_chunk = 256 / bit_width;
    if (element_index >= elements_per_chunk)
    {
        SC_ERROR("TMU_ELEMENT_ERROR", "Element index {} out of bounds for bit width {}", element_index, bit_width);
        return 0;
    }

    uint16_t result = 0;

    switch (bit_width)
    {
        case 4:
        {
            // For 4-bit elements: 2 elements per byte
            uint32_t byte_index = element_index / 2;
            bool is_upper_nibble = (element_index % 2) != 0;

            if (byte_index < chunk.size())
            {
                if (is_upper_nibble)
                {
                    result = (chunk[byte_index] >> 4) & 0x0F;
                }
                else
                {
                    result = chunk[byte_index] & 0x0F;
                }
            }
            break;
        }

        case 8:
        {
            // For 8-bit elements: 1 element per byte
            if (element_index < chunk.size())
            {
                result = chunk[element_index];
            }
            break;
        }

        case 16:
        {
            // For 16-bit elements: 1 element per 2 bytes
            uint32_t byte_index = element_index * 2;
            if (byte_index + 1 < chunk.size())
            {
                result = static_cast<uint16_t>(chunk[byte_index]) |
                         (static_cast<uint16_t>(chunk[byte_index + 1]) << 8);
            }
            break;
        }
        default:
            SC_ERROR("TMU_ELEMENT_ERROR", "Unsupported bit width in extract_element: {}", bit_width);
            break;
    }

    return result;
}

/**
 * @brief Places a single element into a chunk at the specified index.
 *
 * @param chunk The chunk to modify.
 * @param element_index Index where to place the element (0-based).
 * @param bit_width Bit width of each element (4, 8, 16).
 * @param value The value to place.
 */
void place_element(FeatTMU::Chunk& chunk,
                   uint32_t element_index,
                   uint32_t bit_width,
                   uint16_t value)
{
    // Calculate elements per chunk and validate index
    uint32_t elements_per_chunk = 256 / bit_width;
    if (element_index >= elements_per_chunk)
    {
        SC_ERROR("TMU_ELEMENT_ERROR", "Element index {} out of bounds for bit width {} in place_element", 
                    element_index, bit_width);
        return;
    }

    // Mask the value to ensure it fits within the bit width
    value &= (1 << bit_width) - 1;

    switch (bit_width)
    {
        case 4:
        {
            // For 4-bit elements: 2 elements per byte
            uint32_t byte_index = element_index / 2;
            bool is_upper_nibble = (element_index % 2) != 0;

            if (byte_index < chunk.size())
            {
                if (is_upper_nibble)
                {
                    // Clear upper nibble and set new value
                    chunk[byte_index] = (chunk[byte_index] & 0x0F) | ((value & 0x0F) << 4);
                }
                else
                {
                    // Clear lower nibble and set new value
                    chunk[byte_index] = (chunk[byte_index] & 0xF0) | (value & 0x0F);
                }
            }
            break;
        }

        case 8:
        {
            // For 8-bit elements: 1 element per byte
            if (element_index < chunk.size())
            {
                chunk[element_index] = static_cast<uint8_t>(value);
            }
            break;
        }

        case 16:
        {
            // For 16-bit elements: 1 element per 2 bytes
            uint32_t byte_index = element_index * 2;
            if (byte_index + 1 < chunk.size())
            {
                chunk[byte_index] = static_cast<uint8_t>(value & 0xFF);
                chunk[byte_index + 1] = static_cast<uint8_t>((value >> 8) & 0xFF);
            }
            break;
        }
        default:
            SC_ERROR("TMU_ELEMENT_ERROR", "Unsupported bit width in place_element: {}", bit_width);
            break;
    }
}

/**
 * @brief Performs block transpose by extracting input columns to form output chunks (rows).
 * Assumes input_block_buffer is valid and correctly sized. No padding checks.
 *
 * @param input_block_buffer Vector containing B1_in * B0b_in input chunks.
 * @param B1_in Input block dimension 1 (rows of chunks).
 * @param B0b_in Input block dimension 0b (columns of chunks).
 * @param data_bit_width The bit width of each element (e.g., 4, 8, 16).
 * @return std::vector<Chunk> A vector containing exactly 64 transposed output chunks.
 */
FeatTMU::BlockData FeatTMU::process_transpose(
    const BlockData& input_block_buffer,
    uint32_t data_bit_width,
    const std::pair<uint32_t, uint32_t>& input_block_dims_pair) const
{
    // Extract input block dimensions
    uint32_t B0b_in = input_block_dims_pair.first;  // dim0b size (columns of chunks)
    uint32_t B1_in = input_block_dims_pair.second;  // dim1 size (rows of chunks)

    // Calculate elements per chunk based on data width
    uint32_t elements_per_chunk = 256 / data_bit_width;

    // Validate input buffer size
    uint32_t expected_input_chunks = B1_in * B0b_in;
    if (input_block_buffer.size() != expected_input_chunks)
    {
        SC_ERROR("TMU_TRANSPOSE_ERROR", "Input buffer size {} does not match expected dimensions {}", 
                    input_block_buffer.size(), expected_input_chunks);
        // Return empty block data
        BlockData empty_block_data(64);
        for (auto& chunk : empty_block_data)
        {
            chunk.fill(0);
        }
        return empty_block_data;
    }

    // Create a vector to hold all 64 output chunks
    // Each output chunk corresponds to one row in the transposed matrix
    BlockData output_chunks(64);
    for (auto& chunk : output_chunks)
    {
        chunk.fill(0);  // Initialize all chunks to zero
    }

    // Perform the transpose operation
    // We iterate through each element in the input block and place it in the transposed position
    for (uint32_t r = 0; r < B1_in; ++r)
    {  // Input row (dim1)
        for (uint32_t c = 0; c < B0b_in; ++c)
        {  // Input column (dim0b)
            // Calculate the index of the current input chunk
            uint32_t input_chunk_idx = r * B0b_in + c;

            // For each element in the current chunk
            for (uint32_t k = 0; k < elements_per_chunk; ++k)
            {
                // Extract the element from the input chunk
                uint16_t value =
                    extract_element(input_block_buffer[input_chunk_idx], k, data_bit_width);

                // Calculate the transposed position
                // Input position (r, c*elements_per_chunk + k) -> Output position
                // (c*elements_per_chunk + k, r)
                uint32_t output_row = c * elements_per_chunk + k;
                uint32_t output_col = r;

                // Skip if the output row is beyond our 64-row limit
                if (output_row >= 64)
                {
                    continue;
                }

                // Place the element in the transposed position
                place_element(output_chunks[output_row], output_col, data_bit_width, value);
            }
        }
    }

    // Return all 64 output chunks
    return output_chunks;
}

void FeatTMU::process_trans()
{
    SC_INFO("TMU_TRANS", "Starting TRANS operation: byte_base_in={:#x}, byte_base_out={:#x}", 
               byte_base_in, byte_base_out);

    // Determine INPUT block size based on config.cfg_type/cfg_wd
    auto input_block_dims_pair = get_trans_input_block_dims(
        m_cfg_tmu, m_issue_queue_cmd.funct7);  // e.g., returns {4, 16} for 16b {dim0b, dim1}
    const uint32_t IN_BLOCK_SIZE_DIM0B = input_block_dims_pair.first;
    const uint32_t IN_BLOCK_SIZE_DIM1 = input_block_dims_pair.second;

    // OUTPUT block size is fixed
    const uint32_t OUT_BLOCK_SIZE_DIM1 = 64;  // Output has 64 rows
    // Output is always a single chunk in dim0b

    // Calculate number of output blocks based on *output* tensor dimensions
    uint32_t block_num_dim0b_out = m_cfg_tmu.cfg_size_dim1;
    uint32_t block_num_dim1_out = ceil_div(m_cfg_tmu.cfg_size_dim0b, OUT_BLOCK_SIZE_DIM1);
    uint32_t block_num_dim0b_in = ceil_div(m_cfg_tmu.cfg_size_dim0b, IN_BLOCK_SIZE_DIM0B);
    uint32_t block_num_dim1_in = ceil_div(m_cfg_tmu.cfg_size_dim1, IN_BLOCK_SIZE_DIM1);

    Word256b zero_chunk;  // Pre-allocate zero chunk for CIM writes
    zero_chunk.fill(0);

    // Determine target unit index once
    size_t base_write_unit_index;
    uint64_t base_write_word_offset;  // Not strictly needed here, but decode provides it
    bool base_decode_success = decode(byte_base_out, base_write_unit_index, base_write_word_offset);

    if (!base_decode_success)
    {
        SC_ERROR("TMU_TRANS_ERROR", "Invalid byte_base_out provided to TRANS: {:#x}", byte_base_out);
        return;  // Cannot proceed with invalid base address
    }
    bool is_write_to_cim = (base_write_unit_index == NPUConfig::LMEM_NUM - 1);

    // Iterate over OUTPUT blocks (each corresponds to one output chunk)
    for (uint32_t idx_d2 = 0; idx_d2 < m_cfg_tmu.cfg_size_dim2; ++idx_d2)
    {  // Assuming dim2 sizes match in/out
        for (uint32_t block_idx_d1_out = 0; block_idx_d1_out < block_num_dim1_out;
             ++block_idx_d1_out)
        {
            for (uint32_t block_idx_d0b_out = 0; block_idx_d0b_out < block_num_dim0b_out;
                 ++block_idx_d0b_out)
            {
                // 1. Determine corresponding INPUT block indices
                // This mapping is crucial and depends on the transpose definition. Using
                // placeholder.
                BlockIndices input_block_idx = get_input_block_indices_for_output(
                    BlockIndices{idx_d2, block_idx_d1_out, block_idx_d0b_out},
                    input_block_dims_pair);
                // 2. Read Full Input Block into buffer
                BlockData input_block_buffer;
                input_block_buffer.reserve(IN_BLOCK_SIZE_DIM1 * IN_BLOCK_SIZE_DIM0B);
                // Calculate valid data area within the input block
                TileSize input_tile_size = calculate_tile_size(input_block_idx.d1,
                                                               input_block_idx.d0b,
                                                               m_cfg_tmu.cfg_size_dim1,
                                                               m_cfg_tmu.cfg_size_dim0b,
                                                               block_num_dim1_in,
                                                               block_num_dim0b_in,
                                                               IN_BLOCK_SIZE_DIM1,
                                                               IN_BLOCK_SIZE_DIM0B);

                for (uint32_t rel_idx_d1_in = 0; rel_idx_d1_in < IN_BLOCK_SIZE_DIM1;
                     ++rel_idx_d1_in)
                {
                    for (uint32_t rel_idx_d0b_in = 0; rel_idx_d0b_in < IN_BLOCK_SIZE_DIM0B;
                         ++rel_idx_d0b_in)
                    {
                        Chunk chunk_data;
                        // Check if the current relative index is within the valid tile (not block
                        // padding)
                        if (rel_idx_d1_in < input_tile_size.first &&
                            rel_idx_d0b_in < input_tile_size.second)
                        {
                            uint32_t abs_idx_d1_in =
                                input_block_idx.d1 * IN_BLOCK_SIZE_DIM1 + rel_idx_d1_in;
                            uint32_t abs_idx_d0b_in =
                                input_block_idx.d0b * IN_BLOCK_SIZE_DIM0B + rel_idx_d0b_in;

                            // Ensure we don't try to read beyond tensor bounds (belt-and-suspenders
                            // check)
                            if (abs_idx_d1_in < m_cfg_tmu.cfg_size_dim1 &&
                                abs_idx_d0b_in < m_cfg_tmu.cfg_size_dim0b)
                            {
                                uint64_t read_address =
                                    calculate_byte_address(  // Use byte address calculation
                                        byte_base_in,
                                        abs_idx_d0b_in,
                                        abs_idx_d1_in,
                                        input_block_idx.d2,
                                        m_cfg_tmu.cfg_stride_dim2_in,
                                        m_cfg_tmu.cfg_stride_dim1_in);
                                // Read data - Assuming read_data handles potential rem_dim0 masking
                                // internally via MaskInfo
                                MaskInfo read_mask = calculate_mask_info(abs_idx_d0b_in);
                                chunk_data = read_data(read_address, read_mask);
                            }
                            else
                            {
                                // This case (within tile but outside tensor) should ideally not
                                // happen if tile_size is correct
                                chunk_data.fill(0);
                            }
                        }
                        else
                        {
                            // This chunk corresponds to input block padding, treat as zeros
                            chunk_data.fill(0);
                        }
                        input_block_buffer.push_back(chunk_data);
                    }
                }

                // 3. Process Block (Transpose)
                // This function takes the input block data, performs transpose,
                // handles internal padding, and returns all 64 output chunks (64x1 format).
                uint32_t data_bit_width =
                    width_code_to_bits(static_cast<WidthCode>(m_cfg_tmu.cfg_wd));
                // Perform the transpose operation
                BlockData output_chunks =
                    process_transpose(input_block_buffer, data_bit_width, input_block_dims_pair);

                // 4. Write all 64 output chunks
                // The output block is 64x1, so we need to write all 64 chunks
                uint32_t abs_idx_d0b_out =
                    block_idx_d0b_out;  // d0b index of the output chunk (logical column)

                for (uint32_t chunk_idx = 0; chunk_idx < 64; ++chunk_idx)
                {  // chunk_idx is logical row within the 64-chunk output block
                    // Calculate the absolute dim1 index for this chunk (logical row index)
                    uint32_t abs_idx_d1_out = block_idx_d1_out * OUT_BLOCK_SIZE_DIM1 + chunk_idx;

                    // Calculate write address based on destination type
                    uint64_t write_address = 0;
                    if (is_write_to_cim)
                    {
                        // Calculate physical CIMC word offset
                        uint32_t physical_page = idx_d2;
                        uint32_t physical_engine =
                            block_idx_d0b_out;  // Logical output column maps to Engine
                        uint32_t physical_macro =
                            chunk_idx / 32;  // Logical row within block maps to Macro
                        uint32_t physical_row =
                            chunk_idx %
                            32;  // Logical row within block maps to Data Row within Macro

                        // Formula: E * WORDS_PER_ENGINE + M * WORDS_PER_MACRO + R *
                        // PAGES_PER_MACRO_ROW + P
                        uint64_t target_word_offset =
                            (physical_engine * NPUConfig::WORDS_PER_ENGINE) +
                            (physical_macro * NPUConfig::WORDS_PER_MACRO) +
                            (physical_row * NPUConfig::PAGES_PER_MACRO_ROW) + physical_page;

                        // Check if calculated offset is valid (optional sanity check)
                        if (target_word_offset >= NPUConfig::TOTAL_WORDS)
                        {
                            SC_WARN("TMU_TRANS_OFFSET", "TRANS calculated invalid CIMC word_offset: {} for E={} M={} R={} P={}", 
                                       target_word_offset, physical_engine, physical_macro, physical_row, physical_page);
                            continue;  // Skip this write if offset is bad
                        }
                        // Construct the final byte address
                        write_address = make_address(base_write_unit_index, target_word_offset);
                    }
                    else
                    {
                        // Destination is SPAD, use standard linear calculation
                        write_address =
                            calculate_byte_address(byte_base_out,
                                                   abs_idx_d0b_out,
                                                   abs_idx_d1_out,
                                                   idx_d2,  // Use logical output indices
                                                   m_cfg_tmu.cfg_stride_dim2_out,
                                                   m_cfg_tmu.cfg_stride_dim1_out);
                    }

                    // Write the chunk if address is valid
                    if (write_address != 0)
                    {
                        // Note: Transpose output padding should ideally be handled within
                        // process_transpose or checked against output tensor bounds if necessary.
                        // Assuming output_chunks[chunk_idx] contains the correct data (or zeros for
                        // padding).
                        write_data(write_address, output_chunks[chunk_idx]);
                    }
                }
            }  // End block_idx_d0b_out loop
        }  // End block_idx_d1_out loop
    }  // End idx_d2 loop
    // Task Complete
    SC_INFO("TMU_TRANS_OK", "TRANS operation completed successfully");
}

Word256b FeatTMU::generate_mask(uint32_t rem_dim0, uint8_t prec) const
{
    Word256b mask;
    mask.fill(0x00);  // Initialize all to zero

    uint8_t widthCode = prec & 0x07;

    // Handle special case of zero remainder
    if (rem_dim0 == 0)
    {
        return mask;  // Return all zeros
    }

    // Calculate bytes per element and valid bytes
    uint32_t bytes_per_elem;
    uint32_t valid_bytes;
    bool is_int4 = (widthCode == static_cast<uint8_t>(dtype::WidthCode::W4));

    if (is_int4)
    {
        // For INT4: 2 elements per byte
        valid_bytes = (rem_dim0 + 1) >> 1;  // Equivalent to (rem_dim0 + 1) / 2
    }
    else
    {
        // For other types: use power of 2 shift for bytes calculation
        bytes_per_elem = 1u << (widthCode - static_cast<uint8_t>(dtype::WidthCode::W8));
        valid_bytes = rem_dim0 * bytes_per_elem;
    }

    // Set mask values for complete bytes
    for (uint32_t i = 0; i < valid_bytes && i < mask.size(); i++)
    {
        mask[i] = 0xFF;
    }

    // Handle special case for last byte of INT4 with odd elements
    if (is_int4 && (rem_dim0 & 1))
    {
        mask[valid_bytes - 1] = 0x0F;  // Only lower 4 bits valid
    }

    return mask;
}

FeatTMU::MaskInfo FeatTMU::calculate_mask_info(uint32_t dim0b) const
{
    MaskInfo info;

    // 只在dim0的最后一块且有余数时需要掩码
    if (dim0b == m_cfg_tmu.cfg_size_dim0b - 1 && m_cfg_tmu.cfg_rem_dim0 != 0)
    {
        info.need_mask = true;
        info.mask = generate_mask(m_cfg_tmu.cfg_rem_dim0, m_prec);
    }

    return info;
}

std::pair<uint32_t, uint32_t> FeatTMU::get_trans_input_block_dims(const TMUConfig& config,
                                                                  uint32_t func7) const
{
    uint32_t input_width = width_code_to_bits(static_cast<WidthCode>(config.cfg_wd));
    uint32_t input_block_dim0b = 1, input_block_dim1 = 64;
    if (func7 == TRANS_DRV)
    {
        if (input_width == 8)
        {
            input_block_dim0b = 2;
            input_block_dim1 = 32;
        }
        else if (input_width == 16)
        {
            input_block_dim0b = 4;
            input_block_dim1 = 16;
        }
    }
    return std::make_pair(input_block_dim0b, input_block_dim1);
}

FeatTMU::BlockData FeatTMU::process_align(const BlockData& input_block_buffer,
                                          uint8_t data_type) const
{

    // Validate input buffer size - should be 64 chunks
    if (input_block_buffer.size() != 64)
    {
        SC_ERROR("TMU_ALIGN_ERROR", "Input buffer size for alignment must be 64 chunks, got {}", 
                    input_block_buffer.size());
        // Return empty block data
        BlockData empty_block_data(66);
        for (auto& chunk : empty_block_data)
        {
            chunk.fill(0);
        }
        return empty_block_data;
    }

    // Create output buffer with 66 chunks (64 for aligned data + 2 for exponents)
    BlockData output_buffer(66);
    for (auto& chunk : output_buffer)
    {
        chunk.fill(0);  // Initialize all chunks to zero
    }

    // Get data bit width based on data type
    uint32_t data_bit_width;
    data_bit_width = width_code_to_bits(static_cast<WidthCode>(data_type & 0x07));
    uint8_t data_fmt_cim = dtype_to_dcim_fmt(data_type);
    // Calculate elements per chunk
    uint32_t elements_per_chunk = 256 / data_bit_width;

    // Process the input in two halves (32 chunks each)
    for (uint32_t half = 0; half < 2; ++half)
    {
        // For each element position within a chunk
        for (uint32_t element_idx = 0; element_idx < elements_per_chunk; ++element_idx)
        {
            // Extract the same element from each of the 32 chunks in this half
            uint16_t column_data[32];   // Array to hold the column of 32 elements
            uint16_t aligned_data[33];  // Array to hold aligned data (32 elements + 1 exponent)

            // Extract elements from the 32 chunks
            for (uint32_t chunk_idx = 0; chunk_idx < 32; ++chunk_idx)
            {
                uint32_t input_chunk_idx = half * 32 + chunk_idx;
                column_data[chunk_idx] = extract_element(
                    input_block_buffer[input_chunk_idx], element_idx, data_bit_width);
            }

            // Perform alignment using float_data_align
            float_data_align(column_data, data_fmt_cim, aligned_data);

            // Place aligned data back into output chunks
            for (uint32_t chunk_idx = 0; chunk_idx < 32; ++chunk_idx)
            {
                uint32_t output_chunk_idx = half * 32 + chunk_idx;
                place_element(output_buffer[output_chunk_idx],
                              element_idx,
                              data_bit_width,
                              aligned_data[chunk_idx]);
            }

            // Store the exponent in the exponent chunks (64 and 65)
            // Each exponent chunk can store multiple exponents
            uint32_t exponent_element_idx = half * elements_per_chunk + element_idx;
            if (exponent_element_idx < elements_per_chunk)
            {
                // Store in chunk 64
                place_element(
                    output_buffer[64], exponent_element_idx, data_bit_width, aligned_data[32]);
            }
            else
            {
                // Store in chunk 65
                place_element(output_buffer[65],
                              exponent_element_idx - elements_per_chunk,
                              data_bit_width,
                              aligned_data[32]);
            }
        }
    }

    return output_buffer;
}

void FeatTMU::process_mov_align()
{
    SC_INFO("TMU_MOV_ALIGN", "Starting MOV_ALIGN operation: byte_base_in={:#x}, byte_base_out={:#x}", 
               byte_base_in, byte_base_out);

    // Input block size is (64,1) - 64 chunks, each with 1 256-bit word
    const uint32_t BLOCK_SIZE_DIM1 = 64;

    // Calculate number of blocks
    uint32_t block_num_dim1 = ceil_div(m_cfg_tmu.cfg_size_dim1, BLOCK_SIZE_DIM1);
    uint32_t block_num_dim0b = m_cfg_tmu.cfg_size_dim0b;

    // Determine target unit index once
    size_t base_write_unit_index;
    uint64_t base_write_word_offset;  // Not strictly needed here, but decode provides it
    bool base_decode_success = decode(byte_base_out, base_write_unit_index, base_write_word_offset);

    if (!base_decode_success)
    {
        SC_ERROR("TMU_MOV_ALIGN_ERROR", "Invalid byte_base_out provided to MOV_ALIGN: {:#x}", byte_base_out);
        return;  // Cannot proceed with invalid base address
    }
    bool is_write_to_cim = (base_write_unit_index == NPUConfig::LMEM_NUM - 1);

    // Iterate over tensor dimensions (dim2 is outermost)
    for (uint32_t idx_d2 = 0; idx_d2 < m_cfg_tmu.cfg_size_dim2; ++idx_d2)
    {  // Logical Page
        // Iterate over blocks in dim1
        for (uint32_t block_idx_d1 = 0; block_idx_d1 < block_num_dim1; ++block_idx_d1)
        {  // Logical Engine
            // Iterate over blocks in dim0b
            for (uint32_t block_idx_d0b = 0; block_idx_d0b < block_num_dim0b; ++block_idx_d0b)
            {  // Logical Chunk Index (0)

                // 1. Read input block (64 chunks)
                BlockData input_block_buffer;
                input_block_buffer.reserve(BLOCK_SIZE_DIM1);

                for (uint32_t rel_idx_d1 = 0; rel_idx_d1 < BLOCK_SIZE_DIM1; ++rel_idx_d1)
                {  // Logical Row within Engine (0-63)
                    // Calculate absolute indices
                    uint32_t abs_idx_d1 =
                        block_idx_d1 * BLOCK_SIZE_DIM1 + rel_idx_d1;  // Logical Overall Row (0-255)
                    uint32_t abs_idx_d0b = block_idx_d0b;             // Logical Chunk Index (0)

                    // Skip if beyond tensor bounds
                    if (abs_idx_d1 >= m_cfg_tmu.cfg_size_dim1)
                    {
                        Chunk zero_chunk;
                        zero_chunk.fill(0);
                        input_block_buffer.push_back(zero_chunk);
                        continue;
                    }

                    // Read input chunk
                    uint64_t read_address = calculate_byte_address(byte_base_in,
                                                                   abs_idx_d0b,
                                                                   abs_idx_d1,
                                                                   idx_d2,
                                                                   m_cfg_tmu.cfg_stride_dim2_in,
                                                                   m_cfg_tmu.cfg_stride_dim1_in);

                    MaskInfo mask_info = calculate_mask_info(abs_idx_d0b);
                    Chunk chunk_data = read_data(read_address, mask_info);
                    input_block_buffer.push_back(chunk_data);
                }

                // 2. Process alignment
                BlockData aligned_block_buffer =
                    process_align(input_block_buffer, m_prec);  // Returns 66 chunks

                // 3. Write aligned data (first 64 chunks) to output
                for (uint32_t rel_idx_d1 = 0; rel_idx_d1 < BLOCK_SIZE_DIM1; ++rel_idx_d1)
                {  // Logical Row within Engine (0-63)
                    // Calculate absolute logical indices
                    uint32_t abs_idx_d1 =
                        block_idx_d1 * BLOCK_SIZE_DIM1 + rel_idx_d1;  // Logical Overall Row (0-255)
                    uint32_t abs_idx_d0b = block_idx_d0b;             // Logical Chunk Index (0)

                    // Skip writing if the corresponding input was padding (i.e., beyond tensor
                    // bounds)
                    if (abs_idx_d1 >= m_cfg_tmu.cfg_size_dim1)
                    {
                        continue;
                    }

                    // Calculate write address based on destination type
                    uint64_t write_address = 0;
                    if (is_write_to_cim)
                    {
                        // Calculate physical CIMC word offset for data rows
                        uint32_t physical_engine = block_idx_d1;
                        uint32_t physical_macro = rel_idx_d1 / 32;
                        uint32_t physical_row = rel_idx_d1 % 32;  // Data row 0-31
                        uint32_t physical_page = idx_d2;

                        uint64_t target_word_offset =
                            (physical_engine * NPUConfig::WORDS_PER_ENGINE) +
                            (physical_macro * NPUConfig::WORDS_PER_MACRO) +
                            (physical_row * NPUConfig::PAGES_PER_MACRO_ROW) + physical_page;

                        if (target_word_offset >= NPUConfig::TOTAL_WORDS)
                        {
                            SC_WARN("TMU_MOV_ALIGN_OFFSET", "MOV_ALIGN data calculated invalid CIMC word_offset: {} for E={} M={} R={} P={}", 
                                       target_word_offset, physical_engine, physical_macro, physical_row, physical_page);
                            continue;  // Skip this write if offset is bad
                        }
                        write_address = make_address(base_write_unit_index, target_word_offset);
                    }
                    else
                    {
                        // Destination is SPAD, use standard linear calculation
                        write_address = calculate_byte_address(byte_base_out,
                                                               abs_idx_d0b,
                                                               abs_idx_d1,
                                                               idx_d2,
                                                               m_cfg_tmu.cfg_stride_dim2_out,
                                                               m_cfg_tmu.cfg_stride_dim1_out);
                    }

                    // Write aligned data chunk if address is valid
                    if (write_address != 0)
                    {
                        write_data(write_address, aligned_block_buffer[rel_idx_d1]);
                    }
                }

                // 4. Write exponents (last 2 chunks)
                for (uint32_t exp_idx = 0; exp_idx < 2; ++exp_idx)
                {  // exp_idx 0 -> Macro 0, exp_idx 1 -> Macro 1
                    uint64_t write_address = 0;
                    uint32_t physical_page = idx_d2;
                    uint32_t physical_engine = block_idx_d1;
                    uint32_t physical_macro = exp_idx;  // 0 or 1
                    uint32_t physical_row = 32;         // Exponent row index is 32

                    if (is_write_to_cim)
                    {
                        // Calculate physical CIMC word offset for the exponent row
                        uint64_t target_word_offset =
                            (physical_engine * NPUConfig::WORDS_PER_ENGINE) +
                            (physical_macro * NPUConfig::WORDS_PER_MACRO) +
                            (physical_row * NPUConfig::PAGES_PER_MACRO_ROW) + physical_page;

                        if (target_word_offset >= NPUConfig::TOTAL_WORDS)
                        {
                            SC_WARN("TMU_MOV_ALIGN_EXP_OFFSET", "MOV_ALIGN exponent calculated invalid CIMC word_offset: {} for E={} M={} R={} P={}", 
                                       target_word_offset, physical_engine, physical_macro, physical_row, physical_page);
                            continue;  // Skip this write if offset is bad
                        }
                        write_address = make_address(base_write_unit_index, target_word_offset);
                    }
                    else
                    {
                        // Destination is SPAD, use standard linear calculation
                        // Need logical indices corresponding to exponent position
                        // These logical indices might not perfectly align with a tensor structure,
                        // but we calculate them sequentially after the data block for SPAD.
                        // TODO: 关于写入exp在local_mem中地址计算
                        uint32_t abs_idx_d1_exp =
                            block_idx_d1 * BLOCK_SIZE_DIM1 + BLOCK_SIZE_DIM1 + exp_idx;
                        uint32_t abs_idx_d0b_exp = block_idx_d0b;

                        write_address = calculate_byte_address(byte_base_out,
                                                               abs_idx_d0b_exp,
                                                               abs_idx_d1_exp,
                                                               idx_d2,
                                                               m_cfg_tmu.cfg_stride_dim2_out,
                                                               m_cfg_tmu.cfg_stride_dim1_out);
                    }

                    // Write exponent chunk if address is valid
                    if (write_address != 0)
                    {
                        write_data(write_address, aligned_block_buffer[64 + exp_idx]);
                    }
                }
            }  // End block_idx_d0b loop
        }  // End block_idx_d1 loop
    }  // End idx_d2 loop

    // Task Complete
    SC_INFO("TMU_MOV_ALIGN_OK", "MOV_ALIGN operation completed successfully");
}

void FeatTMU::process_trans_align()
{
    SC_INFO("TMU_TRANS_ALIGN", "Starting TRANS_ALIGN operation: byte_base_in={:#x}, byte_base_out={:#x}", 
               byte_base_in, byte_base_out);
    
    // INPUT block size depends on data width
    std::pair<uint32_t, uint32_t> input_block_dims_pair =
        get_trans_input_block_dims(m_cfg_tmu, TRANS_DRV);
    uint32_t IN_BLOCK_SIZE_DIM0B = input_block_dims_pair.first;
    uint32_t IN_BLOCK_SIZE_DIM1 = input_block_dims_pair.second;

    // OUTPUT block size is fixed
    const uint32_t OUT_BLOCK_SIZE_DIM1 = 64;  // Output has 64 rows

    // Calculate number of blocks
    uint32_t block_num_dim0b_in = m_cfg_tmu.cfg_size_dim0b;
    uint32_t block_num_dim1_in = ceil_div(m_cfg_tmu.cfg_size_dim1, IN_BLOCK_SIZE_DIM1);

    // For transpose, the output dimensions are swapped
    uint32_t block_num_dim0b_out = m_cfg_tmu.cfg_size_dim1;
    uint32_t block_num_dim1_out = ceil_div(m_cfg_tmu.cfg_size_dim0b, OUT_BLOCK_SIZE_DIM1);

    // Determine target unit index once
    size_t base_write_unit_index;
    uint64_t base_write_word_offset;  // Not strictly needed here, but decode provides it
    bool base_decode_success = decode(byte_base_out, base_write_unit_index, base_write_word_offset);

    if (!base_decode_success)
    {
        SC_ERROR("TMU_TRANS_ALIGN_ERROR", "Invalid byte_base_out provided to TRANS_ALIGN: {:#x}", byte_base_out);
        return;  // Cannot proceed with invalid base address
    }
    bool is_write_to_cim = (base_write_unit_index == NPUConfig::LMEM_NUM - 1);

    // Iterate over tensor dimensions (dim2 is outermost)
    for (uint32_t idx_d2 = 0; idx_d2 < m_cfg_tmu.cfg_size_dim2; ++idx_d2)
    {  // Logical Page
        // Iterate over output blocks in dim1
        for (uint32_t block_idx_d1_out = 0; block_idx_d1_out < block_num_dim1_out;
             ++block_idx_d1_out)
        {  // Block index for output dim1 (maps to input dim0b)
            // Iterate over output blocks in dim0b
            for (uint32_t block_idx_d0b_out = 0; block_idx_d0b_out < block_num_dim0b_out;
                 ++block_idx_d0b_out)
            {  // Block index for output dim0b (maps to input dim1)

                // 1. Calculate Input Block Indices
                BlockIndices output_block_indices = {idx_d2, block_idx_d1_out, block_idx_d0b_out};
                BlockIndices input_block_idx =
                    get_input_block_indices_for_output(output_block_indices, input_block_dims_pair);

                // 2. Read Input Block
                BlockData input_block_buffer;
                input_block_buffer.reserve(IN_BLOCK_SIZE_DIM1 * IN_BLOCK_SIZE_DIM0B);

                // Calculate valid data area within the input block
                TileSize input_tile_size = calculate_tile_size(input_block_idx.d1,
                                                               input_block_idx.d0b,
                                                               m_cfg_tmu.cfg_size_dim1,
                                                               m_cfg_tmu.cfg_size_dim0b,
                                                               block_num_dim1_in,
                                                               block_num_dim0b_in,
                                                               IN_BLOCK_SIZE_DIM1,
                                                               IN_BLOCK_SIZE_DIM0B);

                for (uint32_t rel_idx_d1_in = 0; rel_idx_d1_in < IN_BLOCK_SIZE_DIM1;
                     ++rel_idx_d1_in)
                {
                    for (uint32_t rel_idx_d0b_in = 0; rel_idx_d0b_in < IN_BLOCK_SIZE_DIM0B;
                         ++rel_idx_d0b_in)
                    {
                        Chunk chunk_data;

                        // Check if the current relative index is within the valid tile (not block
                        // padding)
                        if (rel_idx_d1_in < input_tile_size.first &&
                            rel_idx_d0b_in < input_tile_size.second)
                        {
                            // Calculate absolute indices
                            uint32_t abs_idx_d1_in =
                                input_block_idx.d1 * IN_BLOCK_SIZE_DIM1 + rel_idx_d1_in;
                            uint32_t abs_idx_d0b_in =
                                input_block_idx.d0b * IN_BLOCK_SIZE_DIM0B + rel_idx_d0b_in;

                            // Read actual data
                            uint64_t read_address =
                                calculate_byte_address(byte_base_in,
                                                       abs_idx_d0b_in,
                                                       abs_idx_d1_in,
                                                       idx_d2,
                                                       m_cfg_tmu.cfg_stride_dim2_in,
                                                       m_cfg_tmu.cfg_stride_dim1_in);

                            MaskInfo mask_info = calculate_mask_info(abs_idx_d0b_in);
                            chunk_data = read_data(read_address, mask_info);
                        }
                        else
                        {
                            // This chunk corresponds to input block padding, treat as zeros
                            chunk_data.fill(0);
                        }

                        input_block_buffer.push_back(chunk_data);
                    }
                }

                // 3. Process Block (Transpose)
                uint32_t data_bit_width =
                    width_code_to_bits(static_cast<WidthCode>(m_cfg_tmu.cfg_wd));
                BlockData transposed_chunks =
                    process_transpose(input_block_buffer, data_bit_width, input_block_dims_pair);

                // 4. Process alignment on the transposed data
                BlockData aligned_block_buffer =
                    process_align(transposed_chunks, m_prec);  // Returns 66 chunks

                // 5. Write aligned data (first 64 chunks)
                uint32_t abs_idx_d0b_out = block_idx_d0b_out;  // Logical output column index

                for (uint32_t chunk_idx = 0; chunk_idx < 64; ++chunk_idx)
                {  // Logical row within the 64-chunk output block
                    // Calculate the absolute logical dim1 index for this chunk
                    uint32_t abs_idx_d1_out = block_idx_d1_out * OUT_BLOCK_SIZE_DIM1 + chunk_idx;

                    // Calculate write address based on destination type
                    uint64_t write_address = 0;
                    if (is_write_to_cim)
                    {
                        // Calculate physical CIMC word offset for data rows
                        uint32_t physical_page = idx_d2;
                        uint32_t physical_engine =
                            block_idx_d0b_out;  // Logical output column maps to Engine
                        uint32_t physical_macro =
                            chunk_idx / 32;  // Logical row within block maps to Macro
                        uint32_t physical_row =
                            chunk_idx %
                            32;  // Logical row within block maps to Data Row within Macro

                        uint64_t target_word_offset =
                            (physical_engine * NPUConfig::WORDS_PER_ENGINE) +
                            (physical_macro * NPUConfig::WORDS_PER_MACRO) +
                            (physical_row * NPUConfig::PAGES_PER_MACRO_ROW) + physical_page;

                        if (target_word_offset >= NPUConfig::TOTAL_WORDS)
                        {
                            SC_WARN("TMU_TRANS_ALIGN_OFFSET", "TRANS_ALIGN data calculated invalid CIMC word_offset: {}", 
                                       target_word_offset);
                            continue;
                        }
                        write_address = make_address(base_write_unit_index, target_word_offset);
                    }
                    else
                    {
                        // Destination is SPAD, use standard linear calculation
                        write_address = calculate_byte_address(byte_base_out,
                                                               abs_idx_d0b_out,
                                                               abs_idx_d1_out,
                                                               idx_d2,
                                                               m_cfg_tmu.cfg_stride_dim2_out,
                                                               m_cfg_tmu.cfg_stride_dim1_out);
                    }

                    // Write the aligned data chunk if address is valid
                    if (write_address != 0)
                    {
                        write_data(write_address, aligned_block_buffer[chunk_idx]);
                    }
                }

                // 6. Write exponents (last 2 chunks)
                for (uint32_t exp_idx = 0; exp_idx < 2; ++exp_idx)
                {  // exp_idx 0 -> Macro 0, exp_idx 1 -> Macro 1
                    uint64_t write_address = 0;
                    uint32_t physical_page = idx_d2;
                    uint32_t physical_engine =
                        block_idx_d0b_out;              // Logical output column maps to Engine
                    uint32_t physical_macro = exp_idx;  // 0 or 1
                    uint32_t physical_row = 32;         // Exponent row index is 32

                    if (is_write_to_cim)
                    {
                        // Calculate physical CIMC word offset for the exponent row
                        uint64_t target_word_offset =
                            (physical_engine * NPUConfig::WORDS_PER_ENGINE) +
                            (physical_macro * NPUConfig::WORDS_PER_MACRO) +
                            (physical_row * NPUConfig::PAGES_PER_MACRO_ROW) + physical_page;

                        if (target_word_offset >= NPUConfig::TOTAL_WORDS)
                        {
                            SC_WARN("TMU_TRANS_ALIGN_EXP_OFFSET", "TRANS_ALIGN exponent calculated invalid CIMC word_offset: {}", 
                                       target_word_offset);
                            continue;
                        }
                        write_address = make_address(base_write_unit_index, target_word_offset);
                    }
                    else
                    {
                        // Destination is SPAD, use standard linear calculation
                        // Need logical indices corresponding to exponent position
                        uint32_t abs_idx_d1_exp =
                            block_idx_d1_out * OUT_BLOCK_SIZE_DIM1 + OUT_BLOCK_SIZE_DIM1 + exp_idx;
                        uint32_t abs_idx_d0b_exp = block_idx_d0b_out;

                        write_address = calculate_byte_address(byte_base_out,
                                                               abs_idx_d0b_exp,
                                                               abs_idx_d1_exp,
                                                               idx_d2,
                                                               m_cfg_tmu.cfg_stride_dim2_out,
                                                               m_cfg_tmu.cfg_stride_dim1_out);
                    }

                    // Write exponent chunk if address is valid
                    if (write_address != 0)
                    {
                        write_data(write_address, aligned_block_buffer[64 + exp_idx]);
                    }
                }
            }  // End block_idx_d0b_out loop
        }  // End block_idx_d1_out loop
    }  // End idx_d2 loop

    // Task Complete
    SC_INFO("TMU_TRANS_ALIGN_OK", "TRANS_ALIGN operation completed successfully");
}

void FeatTMU::write_data(uint64_t write_byte_address, Word256b& data)
{
    // Log the write operation details with data content
    SC_INFO("TMU_WRITE", "TMU WRITE: function=write_data, addr={:#x}, data_size={} bytes", 
                write_byte_address, sizeof(data));
    
    // Log all the data being written
    std::ostringstream data_stream;
    data_stream << "Write data: ";
    for (size_t i = 0; i < data.size(); ++i) {
        data_stream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(data[i]) << " ";
    }
    SC_INFO("TMU_WRITE_DATA", "TMU WRITE DATA: {}", data_stream.str());
    
    // Create TLM payload
    m_trans.set_command(tlm::TLM_WRITE_COMMAND);
    m_trans.set_address(write_byte_address);
    m_trans.set_data_ptr(reinterpret_cast<uint8_t*>(data.data()));
    m_trans.set_data_length(sizeof(data));  // 256 bits = 32 bytes
    m_trans.set_streaming_width(sizeof(data));
    m_trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

    // Send transaction through socket
    mem_socket->b_transport(m_trans, m_delay);
    
    // Log the transaction result
    if (m_trans.get_response_status() == tlm::TLM_OK_RESPONSE) {
        SC_INFO("TMU_WRITE_OK", "TMU WRITE SUCCESS: addr={:#x}, status=OK", write_byte_address);
    } else {
        SC_ERROR("TMU_WRITE_ERR", "TMU WRITE ERROR: addr={:#x}, status={}", 
                     write_byte_address, static_cast<int>(m_trans.get_response_status()));
    }
}

Word256b FeatTMU::read_data(uint64_t read_byte_address, MaskInfo& mask_info)
{
    Word256b data;
    
    // Log the read operation details
    SC_INFO("TMU_READ", "TMU READ: function=read_data, addr={:#x}, data_size={} bytes", 
                read_byte_address, sizeof(data));
    
    // Create TLM payload
    m_trans.set_command(tlm::TLM_READ_COMMAND);
    m_trans.set_address(read_byte_address);
    m_trans.set_data_ptr(reinterpret_cast<uint8_t*>(data.data()));
    m_trans.set_data_length(sizeof(data));
    m_trans.set_streaming_width(sizeof(data));
    m_trans.set_byte_enable_ptr(nullptr);
    m_trans.set_response_status(tlm::TLM_INCOMPLETE_RESPONSE);

    // Send transaction through socket
    mem_socket->b_transport(m_trans, m_delay);
    if (m_trans.get_response_status() == tlm::TLM_OK_RESPONSE)
    {
        SC_INFO("TMU_READ_OK", "TMU READ SUCCESS: addr={:#x}, status=OK", read_byte_address);
        
        // Log all the data read
        std::ostringstream data_stream;
        data_stream << "Read data: ";
        for (size_t i = 0; i < data.size(); ++i) {
            data_stream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(data[i]) << " ";
        }
        SC_INFO("TMU_READ_DATA", "TMU READ DATA: {}", data_stream.str());
        
        if (mask_info.need_mask)
        {
            SC_INFO("TMU_READ", "TMU READ: Applying mask to data");
            for (size_t i = 0; i < data.size(); i++)
            {
                data[i] &= mask_info.mask[i];
            }
            
            // Log all masked data
            std::ostringstream masked_stream;
            masked_stream << "Masked data: ";
            for (size_t i = 0; i < data.size(); ++i) {
                masked_stream << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(data[i]) << " ";
            }
            SC_INFO("TMU_READ_MASKED_DATA", "TMU READ MASKED DATA: {}", masked_stream.str());
        }
        return data;
    }
    else
    {
        SC_ERROR("TMU_READ_ERR", "TMU READ ERROR: Failed to read data from local memory at address: {:#x}, status={}", 
                     read_byte_address, static_cast<int>(m_trans.get_response_status()));
        return Word256b();
    }
}
void FeatTMU::process_set_cim_exp()
{
    SC_INFO("TMU_SET_CIMEXP", "Starting SET_CIMEXP operation: byte_base_in={:#x}, byte_base_out={:#x}", 
               byte_base_in, byte_base_out);
    
    // Reads 8 chunks (exponent vector for 8 macros) from SPAD (byte_base_in)
    // Writes them to the exponent row (row 32) of the 8 corresponding macros
    // in the CIMC page specified by byte_base_out.

    // 1. Verify source (SPAD) and destination (CIMC) addresses
    size_t src_unit_index;
    uint64_t src_word_offset;
    if (!decode(byte_base_in, src_unit_index, src_word_offset) ||
        src_unit_index >= NPUConfig::SPAD_NUM)
    {
        SC_ERROR("TMU_SET_CIMEXP_ERROR", "Invalid source SPAD address: {:#x}", byte_base_in);
        return;
    }

    size_t dest_unit_index;
    uint64_t
        dest_base_word_offset;  // Base offset, likely points to E=0, M=0, R=0 of the target page
    if (!decode(byte_base_out, dest_unit_index, dest_base_word_offset) ||
        dest_unit_index != (NPUConfig::LMEM_NUM - 1))
    {
        SC_ERROR("TMU_SET_CIMEXP_ERROR", "Invalid destination CIMC address: {:#x}", byte_base_out);
        return;
    }

    // 2. Determine target CIMC page from destination base address
    uint32_t target_engine, target_macro_in_eng, target_row, target_page;
    if (!parse_word_offset(
            dest_base_word_offset, target_engine, target_macro_in_eng, target_row, target_page))
    {
        SC_ERROR("TMU_SET_CIMEXP_ERROR", "Failed to parse destination base word offset: {}", dest_base_word_offset);
        return;
    }
    // We only care about the target_page derived from the base address.

    // 3. Read 8 chunks from SPAD
    std::vector<Word256b> exp_chunks(8);
    MaskInfo dummy_mask;  // No masking needed for SPAD reads/writes in this context
    for (uint32_t i = 0; i < 8; ++i)
    {
        // Assume the 8 chunks are stored linearly in SPAD starting from byte_base_in
        uint64_t read_addr = byte_base_in + i * (NPUConfig::LMEM_WD / 8);
        exp_chunks[i] = read_data(read_addr, dummy_mask);  // Read each chunk
    }

    // 4. Write each chunk to the corresponding macro's exponent row in the target CIMC page
    const uint32_t EXPONENT_ROW_INDEX = 32;
    for (uint32_t macro_idx = 0; macro_idx < 8; ++macro_idx)
    {
        // Calculate physical engine and macro index within engine
        uint32_t physical_engine =
            macro_idx / NPUConfig::MACROS_PER_ENGINE;  // 0, 0, 1, 1, 2, 2, 3, 3
        uint32_t physical_macro_in_engine =
            macro_idx % NPUConfig::MACROS_PER_ENGINE;  // 0, 1, 0, 1, 0, 1, 0, 1

        // Calculate the target word offset for the exponent row of this macro
        uint64_t target_word_offset = (physical_engine * NPUConfig::WORDS_PER_ENGINE) +
                                      (physical_macro_in_engine * NPUConfig::WORDS_PER_MACRO) +
                                      (EXPONENT_ROW_INDEX * NPUConfig::PAGES_PER_MACRO_ROW) +
                                      target_page;

        // Check bounds (safety)
        if (target_word_offset >= NPUConfig::TOTAL_WORDS)
        {
            SC_WARN("TMU_SET_CIMEXP_OFFSET", "Calculated invalid target CIMC word offset: {} for Macro {}, Page {}", 
                       target_word_offset, macro_idx, target_page);
            continue;  // Skip writing this chunk
        }

        // Construct the final CIMC byte address
        uint64_t write_addr = make_address(dest_unit_index, target_word_offset);

        // Write the chunk
        write_data(write_addr, exp_chunks[macro_idx]);
    }
    // Task Complete
    SC_INFO("TMU_SET_CIMEXP_OK", "SET_CIMEXP operation completed successfully");
}

void FeatTMU::process_get_cim_exp()
{
    SC_INFO("TMU_GET_CIMEXP", "Starting GET_CIMEXP operation: byte_base_in={:#x}, byte_base_out={:#x}", 
               byte_base_in, byte_base_out);
    
    // Reads 8 exponent chunks (row 32) from the 8 macros of a specific CIMC page (byte_base_in)
    // Writes them linearly to SPAD (byte_base_out).

    // 1. Verify source (CIMC) and destination (SPAD) addresses
    size_t src_unit_index;
    uint64_t src_base_word_offset;  // Base offset of the source page
    if (!decode(byte_base_in, src_unit_index, src_base_word_offset) ||
        src_unit_index != (NPUConfig::LMEM_NUM - 1))
    {
        SC_ERROR("TMU_GET_CIMEXP_ERROR", "Invalid source CIMC address: {:#x}", byte_base_in);
        return;
    }

    size_t dest_unit_index;
    uint64_t dest_word_offset;  // Starting offset in SPAD
    if (!decode(byte_base_out, dest_unit_index, dest_word_offset) ||
        dest_unit_index >= NPUConfig::SPAD_NUM)
    {
        SC_ERROR("TMU_GET_CIMEXP_ERROR", "Invalid destination SPAD address: {:#x}", byte_base_out);
        return;
    }

    // 2. Determine source CIMC page from source base address
    uint32_t src_engine, src_macro_in_eng, src_row, src_page;
    if (!parse_word_offset(src_base_word_offset, src_engine, src_macro_in_eng, src_row, src_page))
    {
        SC_ERROR("TMU_GET_CIMEXP_ERROR", "Failed to parse source base word offset: {}", src_base_word_offset);
        return;
    }

    // 3. Read 8 exponent chunks from the specified CIMC page
    std::vector<Word256b> exp_chunks(8);
    MaskInfo dummy_mask;  // No masking needed for reading full exponent chunks
    const uint32_t EXPONENT_ROW_INDEX = 32;

    for (uint32_t macro_idx = 0; macro_idx < 8; ++macro_idx)
    {
        // Calculate physical engine and macro index within engine
        uint32_t physical_engine = macro_idx / NPUConfig::MACROS_PER_ENGINE;
        uint32_t physical_macro_in_engine = macro_idx % NPUConfig::MACROS_PER_ENGINE;

        // Calculate the source word offset for the exponent row of this macro
        uint64_t source_word_offset = (physical_engine * NPUConfig::WORDS_PER_ENGINE) +
                                      (physical_macro_in_engine * NPUConfig::WORDS_PER_MACRO) +
                                      (EXPONENT_ROW_INDEX * NPUConfig::PAGES_PER_MACRO_ROW) +
                                      src_page;

        // Check bounds (safety)
        if (source_word_offset >= NPUConfig::TOTAL_WORDS)
        {
            SC_WARN("TMU_GET_CIMEXP_OFFSET", "Calculated invalid source CIMC word offset: {} for Macro {}, Page {}. Reading zeros.", 
                       source_word_offset, macro_idx, src_page);
            exp_chunks[macro_idx].fill(0);  // Read zeros if offset is invalid
            continue;
        }

        // Construct the final CIMC byte address
        uint64_t read_addr = make_address(src_unit_index, source_word_offset);

        // Read the chunk
        exp_chunks[macro_idx] = read_data(read_addr, dummy_mask);
    }

    // 4. Write the 8 chunks linearly to SPAD
    for (uint32_t i = 0; i < 8; ++i)
    {
        uint64_t write_addr = byte_base_out + i * (NPUConfig::LMEM_WD / 8);
        // Basic bounds check for SPAD write (optional but good practice)
        size_t check_unit;
        uint64_t check_offset;
        if (!decode(write_addr, check_unit, check_offset) || check_unit != dest_unit_index)
        {
            SC_WARN("TMU_GET_CIMEXP_ADDR", "Calculated invalid destination SPAD address: {:#x}", write_addr);
            continue;  // Skip writing this chunk
        }
        write_data(write_addr, exp_chunks[i]);
    }
    // Task Complete
    SC_INFO("TMU_GET_CIMEXP_OK", "GET_CIMEXP operation completed successfully");
}
}  // namespace feat_tmu
