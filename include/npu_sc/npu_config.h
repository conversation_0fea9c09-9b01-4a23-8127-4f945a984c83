#ifndef NPU_CONFIG_H
#define NPU_CONFIG_H

#include <cstdint>
#include <string>
#include <unordered_map>
#include <iostream>

#define CH0_RD_SIZE_MAX 8
#define CH0_WR_SIZE_MAX 2
// 定义 Local Memory Status
enum LMemStatus : uint8_t
{
    IDLE = 0,
    READY = 1,
    BUSY = 2,
    LOCK = 3
};

enum FunctionUnitType : uint8_t
{
    LMU = 0,
    TLU,
    TSU,
    TMU,
    MPU,
    VPU,
    INVALID
};

// 编译期计算log2_ceil
template <unsigned N>
struct Log2Ceil
{
    static constexpr unsigned value = 1 + Log2Ceil<(N + 1) / 2>::value;
};

template <>
struct Log2Ceil<1>
{
    static constexpr unsigned value = 0;
};

template <>
struct Log2Ceil<0>
{
    static constexpr unsigned value = 0;
};

// 编译期计算最大值
template <unsigned A, unsigned B>
struct Max
{
    static constexpr unsigned value = A > B ? A : B;
};

class NPUConfig
{
  public:
    // XLEN枚举
    enum class XLEN
    {
        RV32 = 32,
        RV64 = 64
    };

    static constexpr XLEN RV_XLEN = XLEN::RV32;
    static constexpr int FU_NUM = 6;

    static constexpr int LMEM_WD = 256;  // local memory width
    static constexpr int SPAD_NUM = 4;

    static constexpr int SPAD_WD = LMEM_WD;
    static constexpr int SPAD_DP = 4096;  // spad depth ,must be 32 的倍数
    static constexpr int SPAD_NUM_BYTES = SPAD_DP * SPAD_WD / 8;
    static constexpr int SPAD_ADDR_WD = Log2Ceil<SPAD_DP>::value;  // single spad address width

    static constexpr int CIMC_NUM = 1;
    static constexpr int CIMM_PG_NUM = 16;
    static constexpr int CIMC_CIME_NUM = 4;           // must be 2^n
    static constexpr int CIME_ROW_NUM = LMEM_WD / 4;  // cime row number
    static constexpr int CIME_COL_NUM = LMEM_WD;      // cime col number
    static constexpr int CIMC_DP = CIMC_CIME_NUM * CIME_ROW_NUM * CIMM_PG_NUM;
    static constexpr int CIMC_NUM_BYTES = CIMC_DP * LMEM_WD / 8;
    static constexpr int CIMC_ADDR_WD = Log2Ceil<CIMC_DP>::value;  // single cime address width

    static constexpr int LMEM_OFFSET = Max<SPAD_ADDR_WD, CIMC_ADDR_WD>::value;
    static constexpr int LMEM_INDEX = Log2Ceil<SPAD_NUM + CIMC_NUM>::value;

    static constexpr int FU_ID_WD = Log2Ceil<FU_NUM>::value;             // log2_ceil(FU_NUM)
    static constexpr int CIMC_MODE_WD = Log2Ceil<CIMC_CIME_NUM>::value;  // log2_ceil(CIMC_CIME_NUM)
    // Constants for the new structure (8 macros, 33 rows/macro, 16 pages/row)
    static constexpr uint32_t TOTAL_MACROS = 8;
    static constexpr uint32_t ROWS_PER_MACRO = 33;                           // 32 data + 1 exponent
    static constexpr uint32_t PAGES_PER_MACRO_ROW = NPUConfig::CIMM_PG_NUM;  // Assuming 16
    static constexpr uint32_t WORDS_PER_MACRO =
        ROWS_PER_MACRO * PAGES_PER_MACRO_ROW;  // 33 * 16 = 528
    static constexpr uint32_t MACROS_PER_ENGINE = 2;
    static constexpr uint32_t WORDS_PER_ENGINE =
        WORDS_PER_MACRO * MACROS_PER_ENGINE;  // 528 * 2 = 1056
    static constexpr uint32_t TOTAL_ENGINES = 4;
    static constexpr uint64_t TOTAL_WORDS = TOTAL_MACROS * WORDS_PER_MACRO;  // 8 * 528 = 4224
    static constexpr uint64_t TOTAL_BYTES = TOTAL_WORDS * (NPUConfig::LMEM_WD / 8);  // 4224 * 32

    static constexpr int LMEM_NUM = SPAD_NUM + CIMC_NUM;
    static constexpr int SBD_TRAN_SIZE = LMEM_NUM + 1 + LMEM_NUM * 2 + 1 + LMEM_NUM * FU_ID_WD + 1 +
                                         LMEM_NUM * 3 + 1 + LMEM_NUM * CIMC_MODE_WD;
    // rsp_rob configuration
    static constexpr int ROB_QUEUE_SIZE = 16;
    // Prevent instantiation
    NPUConfig() = delete;
};

namespace instruction
{
// 指令组枚举
enum class Group
{
    CONTROL,
    DATA_TRANSFER,
    TENSOR_MANIPULATION,
    MATRIX_PROCESSING,
    VECTOR_PROCESSING,
    UNKNOWN
};

// 字段掩码
static constexpr uint32_t FUNCT7_MASK = 0xFE000000;
static constexpr uint32_t RS2_MASK = 0x01F00000;
static constexpr uint32_t RS1_MASK = 0x000F8000;
static constexpr uint32_t XD_MASK = 0x00004000;
static constexpr uint32_t XS1_MASK = 0x00002000;
static constexpr uint32_t XS2_MASK = 0x00001000;
static constexpr uint32_t RD_MASK = 0x00000F80;
static constexpr uint32_t OPCODE_MASK = 0x0000007F;

// 指令信息结构
struct InstructionInfo
{
    Group group;
    std::string targetPrimitive;
    std::string instName;
};

// 解码后的指令结构
struct DecodedInstruction
{
    Group group;
    std::string targetPrimitive;
    std::string instName;
    uint32_t funct7;
    uint32_t rs2;
    uint32_t rs1;
    bool xd;
    bool xs1;
    bool xs2;
    uint32_t rd;
    uint32_t opcode;
    uint64_t rs1val;
    uint64_t rs2val;
};

// issue queue cmd struction
struct __attribute__((packed)) IssueQueueCmd
{
    uint8_t funct7;
    bool xd;
    bool xs1;
    bool xs2;
    uint32_t rs1val;
    uint32_t rs2val;

    IssueQueueCmd() : funct7(0), xd(false), xs1(false), xs2(false), rs1val(0), rs2val(0) {}

    void print() const {
        std::cout << "IssueQueueCmd: "
                  << "funct7=" << std::hex << static_cast<unsigned int>(funct7)
                  << ", xd=" << xd
                  << ", xs1=" << xs1
                  << ", xs2=" << xs2
                  << ", rs1val=" << rs1val
                  << ", rs2val=" << rs2val << std::endl;
    }
};

// 指令编码常量
namespace opcode
{
// Control Group
static constexpr uint32_t SYNC = 0x00;
static constexpr uint32_t SW_CIMC = 0x01;
static constexpr uint32_t LK_LMEM = 0x04;
static constexpr uint32_t UNLK_LMEM = 0x05;
static constexpr uint32_t WR_LMEM = 0x02;
static constexpr uint32_t RD_LMEM = 0x03;

// Data Transfer Group
static constexpr uint32_t TLD_CFG = 0x10;
static constexpr uint32_t TLD_DRV = 0x18;
static constexpr uint32_t TST_CFG = 0x20;
static constexpr uint32_t TST_DRV = 0x28;

// Tensor Manipulation Group
static constexpr uint32_t TM_CFG = 0x30;
static constexpr uint32_t BC_PRE = 0x31;
static constexpr uint32_t BC_DRV = 0x39;
static constexpr uint32_t MOV_DRV = 0x38;
static constexpr uint32_t TRANS_DRV = 0x3A;
static constexpr uint32_t SET_CIMEXP = 0x33;
static constexpr uint32_t GET_CIMEXP = 0x34;
// Matrix Processing Group
static constexpr uint32_t MP_CFG = 0x40;
static constexpr uint32_t CONV_PRE = 0x41;
static constexpr uint32_t CONV_DRV = 0x48;
static constexpr uint32_t DWCONV_DRV = 0x49;
static constexpr uint32_t GEMV_DRV = 0x43;
static constexpr uint32_t GEMM_DRV = 0x55;
static constexpr uint32_t GEMM_PRE = 0x54;
static constexpr uint32_t GEMV_PRE = 0x42;
// Vector Processing Group
static constexpr uint32_t VP_CFG = 0x50;
static constexpr uint32_t VV_V_PRE = 0x51;
static constexpr uint32_t VV_V_DRV = 0x58;
static constexpr uint32_t VS_V_PRE = 0x52;
static constexpr uint32_t VS_V_DRV = 0x59;
static constexpr uint32_t V_S_DRV = 0x5A;
static constexpr uint32_t V_V_DRV = 0x5B;
}  // namespace opcode

// 指令映射表初始化函数 (C++11兼容)
inline std::unordered_map<uint32_t, InstructionInfo> createInstructionMap()
{
    std::unordered_map<uint32_t, InstructionInfo> map;
    
    // Control Group
    map[opcode::SYNC] = {Group::CONTROL, "SYNC", "sync"};
    map[opcode::SW_CIMC] = {Group::CONTROL, "SW_CIMC", "sw_cimc"};
    map[opcode::LK_LMEM] = {Group::CONTROL, "LK_LMEM", "lk_lmem"};
    map[opcode::UNLK_LMEM] = {Group::CONTROL, "UNLK_LMEM", "unlk_lmem"};
    map[opcode::WR_LMEM] = {Group::CONTROL, "WR_LMEM", "wr_lmem"};
    map[opcode::RD_LMEM] = {Group::CONTROL, "RD_LMEM", "rd_lmem"};

    // Data Transfer Group
    map[opcode::TLD_CFG] = {Group::DATA_TRANSFER, "TLD", "tld_cfg"};
    map[opcode::TLD_DRV] = {Group::DATA_TRANSFER, "TLD", "tld_drv"};
    map[opcode::TST_CFG] = {Group::DATA_TRANSFER, "TST", "tst_cfg"};
    map[opcode::TST_DRV] = {Group::DATA_TRANSFER, "TST", "tst_drv"};

    // Tensor Manipulation Group
    map[opcode::TM_CFG] = {Group::TENSOR_MANIPULATION, "MOV", "tm_cfg"};
    map[opcode::BC_PRE] = {Group::TENSOR_MANIPULATION, "BC", "bc_pre"};
    map[opcode::BC_DRV] = {Group::TENSOR_MANIPULATION, "BC", "bc_drv"};
    map[opcode::MOV_DRV] = {Group::TENSOR_MANIPULATION, "MOV", "mov_drv"};
    map[opcode::TRANS_DRV] = {Group::TENSOR_MANIPULATION, "TRANS", "trans_drv"};

    // Matrix Processing Group
    map[opcode::MP_CFG] = {Group::MATRIX_PROCESSING, "CONV", "mp_cfg"};
    map[opcode::CONV_PRE] = {Group::MATRIX_PROCESSING, "CONV", "conv_pre"};
    map[opcode::CONV_DRV] = {Group::MATRIX_PROCESSING, "CONV", "conv_drv"};
    map[opcode::DWCONV_DRV] = {Group::MATRIX_PROCESSING, "DWCONV", "dwconv_drv"};
    map[opcode::GEMV_DRV] = {Group::MATRIX_PROCESSING, "GEMV", "gemv_drv"};
    map[opcode::GEMM_DRV] = {Group::MATRIX_PROCESSING, "GEMM", "gemm_drv"};
    map[opcode::GEMV_PRE] = {Group::MATRIX_PROCESSING, "GEMV", "gemv_pre"};
    map[opcode::GEMM_PRE] = {Group::MATRIX_PROCESSING, "GEMM", "gemm_pre"};
    
    // Vector Processing Group
    map[opcode::VP_CFG] = {Group::VECTOR_PROCESSING, "VV_V", "vp_cfg"};
    map[opcode::VV_V_PRE] = {Group::VECTOR_PROCESSING, "VV_V", "vv_v_pre"};
    map[opcode::VV_V_DRV] = {Group::VECTOR_PROCESSING, "VV_V", "vv_v_drv"};
    map[opcode::VS_V_PRE] = {Group::VECTOR_PROCESSING, "VS_V", "vs_v_pre"};
    map[opcode::VS_V_DRV] = {Group::VECTOR_PROCESSING, "VS_V", "vs_v_drv"};
    map[opcode::V_S_DRV] = {Group::VECTOR_PROCESSING, "V_S", "v_s_drv"};
    map[opcode::V_V_DRV] = {Group::VECTOR_PROCESSING, "V_V", "v_v_drv"};
    
    return map;
}

// 指令映射表
inline const std::unordered_map<uint32_t, InstructionInfo>& getInstructionMap()
{
    static const std::unordered_map<uint32_t, InstructionInfo> INSTRUCTION_MAP = createInstructionMap();
    return INSTRUCTION_MAP;
}

}  // namespace instruction

// NPU Core Data Type Definitions
namespace dtype
{
// Type codes (2 bits)
enum class TypeCode : uint8_t
{
    INT = 0x00,
    FP = 0x01,
    BF = 0x02,
    FP2 = 0x03
};

// Width codes (3 bits)
enum class WidthCode : uint8_t
{
    W4 = 0x02,   // 4-bit
    W8 = 0x03,   // 8-bit
    W16 = 0x04,  // 16-bit
    W32 = 0x05   // 32-bit
};

enum class DataFmt : uint8_t
{
    INT4,
    INT8,
    INT12,
    INT16,
    FP32,
    FP16,
    BF16,
    BBF16,
    FP8E4,
    FP8E5
};

// Predefined data type combinations
static constexpr uint8_t INT4 =
    (static_cast<uint8_t>(TypeCode::INT) << 3) | static_cast<uint8_t>(WidthCode::W4);
static constexpr uint8_t INT8 =
    (static_cast<uint8_t>(TypeCode::INT) << 3) | static_cast<uint8_t>(WidthCode::W8);
static constexpr uint8_t INT16 =
    (static_cast<uint8_t>(TypeCode::INT) << 3) | static_cast<uint8_t>(WidthCode::W16);
static constexpr uint8_t INT32 =
    (static_cast<uint8_t>(TypeCode::INT) << 3) | static_cast<uint8_t>(WidthCode::W32);
static constexpr uint8_t FP16 =
    (static_cast<uint8_t>(TypeCode::FP) << 3) | static_cast<uint8_t>(WidthCode::W16);
static constexpr uint8_t FP32 =
    (static_cast<uint8_t>(TypeCode::FP) << 3) | static_cast<uint8_t>(WidthCode::W32);
static constexpr uint8_t BF16 =
    (static_cast<uint8_t>(TypeCode::BF) << 3) | static_cast<uint8_t>(WidthCode::W16);
static constexpr uint8_t FP8E5 =
    (static_cast<uint8_t>(TypeCode::FP) << 3) | static_cast<uint8_t>(WidthCode::W8);
static constexpr uint8_t FP8E4 =
    (static_cast<uint8_t>(TypeCode::FP2) << 3) | static_cast<uint8_t>(WidthCode::W8);

// static constexpr uint8_t BBF16 = (static_cast<uint8_t>(TypeCode::BBF) << 3) |
// static_cast<uint8_t>(WidthCode::W16);

}  // namespace dtype

namespace vpu
{
// VPU Parameters
static constexpr int VPE_NUM = 8;  // Vector Processing Engine number, must be multiple of 8
static constexpr int CH_SIZE_MAX = VPE_NUM * 32 / NPUConfig::LMEM_WD;
static constexpr int CH_SIZE_WD = Max<Log2Ceil<CH_SIZE_MAX>::value, 1>::value;

// Width mode configuration
enum class WidthMode : uint8_t
{
    SINGLE_WIDTH = 0x00,
    WIDENING = 0x04,
    NARROWING = 0x02
};

// Operation Modes
enum class OpMode : uint8_t
{
    // Add/Subtract
    ADD = 0x00,
    SUB = 0x01,   // I1-I2
    RSUB = 0x02,  // I2-I1

    // Min/Max
    MIN = 0x08,
    MAX = 0x09,

    // Compare
    EQ = 0x10,  // Equal
    NE = 0x11,  // Not Equal
    GT = 0x12,  // Greater Than
    GE = 0x13,  // Greater or Equal
    LT = 0x14,  // Less Than
    LE = 0x15,  // Less or Equal

    // Shift
    SHL = 0x18,        // Left Shift
    SHR_FLOOR = 0x19,  // Right Shift Floor
    SHR_CEIL = 0x1A,   // Right Shift Ceil
    SHR_ROUND = 0x1B,  // Right Shift Round

    // Bitwise Logical
    AND = 0x20,
    OR = 0x21,
    XOR = 0x22,

    // Multiplication
    MUL = 0x28
};

// Function Selection
enum class FunctSel : uint8_t
{
    VV_V = 0x00,  // Vector-Vector to Vector
    VS_V = 0x01,  // Vector-Scalar to Vector
    V_S = 0x02,   // Vector to Scalar
    V_V = 0x03    // Vector to Vector
};

// Configuration Signals Structure
struct ConfigSignals
{
    uint32_t cfg_type_out;        // Element data width of output Vector/Scalar O
    uint32_t cfg_type_in1;        // Element data width of input Vector I1
    uint32_t cfg_type_in2;        // Element data width of input Vector/Scalar I2
    uint32_t cfg_wd_ref;          // Element data width for reference
    uint32_t cfg_wd_sc_out;       // Element data width scale of output Vector/Scalar O
    uint32_t cfg_wd_sc_in1;       // Element data width scale of input Vector I1
    uint32_t cfg_wd_sc_in2;       // Element data width scale of input Vector/Scalar I2
    OpMode cfg_op;                // Operation mode
    uint32_t cfg_rem_dim0_ref;    // Dim0 remainder for reference
    uint32_t cfg_size_dim0b_ref;  // Dim0b size for reference
};

// VP Configuration Register Fields
struct VPConfigFields
{
    uint8_t cfg_type_out : 2;          // Element data width of output Vector/Scalar O
    uint8_t cfg_type_in1 : 2;          // Element data width of input Vector I1
    uint8_t cfg_type_in2 : 2;          // Element data width of input Vector/Scalar I2
    uint8_t cfg_wd_ref : 3;            // Element data width for reference
    uint8_t cfg_wd_sc_out : 1;         // Element data width scale of output Vector/Scalar O
    uint8_t cfg_wd_sc_in1 : 1;         // Element data width scale of input Vector I1
    uint8_t cfg_wd_sc_in2 : 1;         // Element data width scale of input Vector/Scalar I2
    OpMode cfg_op : 6;                 // Operation mode
    uint8_t cfg_rem_dim0_ref : 6;      // Dim0 remainder for reference
    uint16_t cfg_size_dim0b_ref : 11;  // Dim0b size for reference
};

}  // namespace vpu

#endif  // NPU_CONFIG_H