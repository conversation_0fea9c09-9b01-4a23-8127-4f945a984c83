#ifndef REGISTER_FORMATTERS_H
#define REGISTER_FORMATTERS_H

#include "sc_logger.h"
#include "register.h"
#include "npu_config.h"

// fmt::formatter 特化 for hardware_instruction 命名空间中的结构体

// TensorTransferConfig 格式化器
template <>
struct fmt::formatter<hardware_instruction::TensorTransferConfig>
{
    template <typename ParseContext>
    auto parse(ParseContext& ctx) const -> decltype(ctx.begin())
    {
        return ctx.begin();
    }

    template <typename FormatContext>
    auto format(const hardware_instruction::TensorTransferConfig& cfg, FormatContext& ctx) const -> decltype(ctx.out())
    {
        return fmt::format_to(ctx.out(),
                              "TensorTransferConfig(\n"
                              "  type:           {:#x}\n"
                              "  wd:             {:#x}\n"
                              "  rem_dim0:       {:#x}\n"
                              "  size_dim0b:     {:#x}\n"
                              "  size_dim1:      {:#x}\n"
                              "  size_dim2:      {:#x}\n"
                              "  stride_dim1_gmem: {:#x}\n"
                              "  stride_dim2_gmem: {:#x}\n"
                              "  stride_dim1_lmem: {:#x}\n"
                              "  stride_dim2_lmem: {:#x}\n"
                              ")",
                              cfg.cfg_type, cfg.cfg_wd, cfg.cfg_rem_dim0,
                              cfg.cfg_size_dim0b, cfg.cfg_size_dim1, cfg.cfg_size_dim2,
                              cfg.cfg_stride_dim1_gmem, cfg.cfg_stride_dim2_gmem,
                              cfg.cfg_stride_dim1_lmem, cfg.cfg_stride_dim2_lmem);
    }
};

// TensorManipConfig 格式化器
template <>
struct fmt::formatter<hardware_instruction::TensorManipConfig>
{
    template <typename ParseContext>
    auto parse(ParseContext& ctx) const -> decltype(ctx.begin())
    {
        return ctx.begin();
    }

    template <typename FormatContext>
    auto format(const hardware_instruction::TensorManipConfig& cfg, FormatContext& ctx) const -> decltype(ctx.out())
    {
        return fmt::format_to(ctx.out(),
                              "TensorManipConfig(\n"
                              "  type:           {:#x}\n"
                              "  wd:             {:#x}\n"
                              "  rem_dim0:       {:#x}\n"
                              "  size_dim0b:     {:#x}\n"
                              "  size_dim1:      {:#x}\n"
                              "  size_dim2:      {:#x}\n"
                              "  stride_dim1_out: {:#x}\n"
                              "  stride_dim2_out: {:#x}\n"
                              "  stride_dim1_in:  {:#x}\n"
                              "  stride_dim2_in:  {:#x}\n"
                              ")",
                              cfg.cfg_type, cfg.cfg_wd, cfg.cfg_rem_dim0,
                              cfg.cfg_size_dim0b, cfg.cfg_size_dim1, cfg.cfg_size_dim2,
                              cfg.cfg_stride_dim1_out, cfg.cfg_stride_dim2_out,
                              cfg.cfg_stride_dim1_in, cfg.cfg_stride_dim2_in);
    }
};

// MatrixProcessConfig 格式化器
template <>
struct fmt::formatter<hardware_instruction::MatrixProcessConfig>
{
    template <typename ParseContext>
    auto parse(ParseContext& ctx) const -> decltype(ctx.begin())
    {
        return ctx.begin();
    }

    template <typename FormatContext>
    auto format(const hardware_instruction::MatrixProcessConfig& cfg, FormatContext& ctx) const -> decltype(ctx.out())
    {
        return fmt::format_to(ctx.out(),
                              "MatrixProcessConfig(\n"
                              "  Precision:\n"
                              "    type_out:     {:#x}\n"
                              "    type_orig:    {:#x}\n"
                              "    type_in:      {:#x}\n"
                              "    type_wt:      {:#x}\n"
                              "    wd_ref:       {:#x}\n"
                              "    wd_sc_out:    {:#x}\n"
                              "    wd_sc_orig:   {:#x}\n"
                              "    wd_sc_in:     {:#x}\n"
                              "    wd_sc_wt:     {:#x}\n"
                              "  Operation:\n"
                              "    accu:         {:#x}\n"
                              "    act:          {:#x}\n"
                              "    shift:        {:#x}\n"
                              "  TensorO:\n"
                              "    rem_dim0_out: {:#x}\n"
                              "    size_dim0b_out: {:#x}\n"
                              "    size_dim1_out: {:#x}\n"
                              "    size_dim2_out: {:#x}\n"
                              "    stride_dim1_out: {:#x}\n"
                              "    stride_dim2_out: {:#x}\n"
                              "  TensorI:\n"
                              "    rem_dim0_in:  {:#x}\n"
                              "    size_dim0b_in: {:#x}\n"
                              "    size_dim1_in: {:#x}\n"
                              "    size_dim2_in: {:#x}\n"
                              "    stride_dim1_in: {:#x}\n"
                              "    stride_dim2_in: {:#x}\n"
                              "  Kernel:\n"
                              "    k_x:          {:#x}\n"
                              "    k_y:          {:#x}\n"
                              "    slide_x:      {:#x}\n"
                              "    slide_y:      {:#x}\n"
                              "    dl_x:         {:#x}\n"
                              "    dl_y:         {:#x}\n"
                              "    log2trs_x:    {:#x}\n"
                              "    log2trs_y:    {:#x}\n"
                              "  Padding:\n"
                              "    pad_w:        {:#x}\n"
                              "    pad_n:        {:#x}\n"
                              "    pad_val:      {:#x}\n"
                              ")",
                              // Precision
                              cfg.cfg_type_out, cfg.cfg_type_orig, cfg.cfg_type_in, cfg.cfg_type_wt,
                              cfg.cfg_wd_ref, cfg.cfg_wd_sc_out, cfg.cfg_wd_sc_orig, cfg.cfg_wd_sc_in, cfg.cfg_wd_sc_wt,
                              // Operation
                              cfg.cfg_accu, cfg.cfg_act, cfg.cfg_shift,
                              // Tensor O
                              cfg.cfg_rem_dim0_out, cfg.cfg_size_dim0b_out, cfg.cfg_size_dim1_out,
                              cfg.cfg_size_dim2_out, cfg.cfg_stride_dim1_out, cfg.cfg_stride_dim2_out,
                              // Tensor I
                              cfg.cfg_rem_dim0_in, cfg.cfg_size_dim0b_in, cfg.cfg_size_dim1_in,
                              cfg.cfg_size_dim2_in, cfg.cfg_stride_dim1_in, cfg.cfg_stride_dim2_in,
                              // Kernel
                              cfg.cfg_k_x, cfg.cfg_k_y, cfg.cfg_slide_x, cfg.cfg_slide_y,
                              cfg.cfg_dl_x, cfg.cfg_dl_y, cfg.cfg_log2trs_x, cfg.cfg_log2trs_y,
                              // Padding
                              cfg.cfg_pad_w, cfg.cfg_pad_n, cfg.cfg_pad_val);
    }
};

// VectorProcessConfig 格式化器
template <>
struct fmt::formatter<hardware_instruction::VectorProcessConfig>
{
    template <typename ParseContext>
    auto parse(ParseContext& ctx) const -> decltype(ctx.begin())
    {
        return ctx.begin();
    }

    template <typename FormatContext>
    auto format(const hardware_instruction::VectorProcessConfig& cfg, FormatContext& ctx) const -> decltype(ctx.out())
    {
        return fmt::format_to(ctx.out(),
                              "VectorProcessConfig(\n"
                              "  Precision:\n"
                              "    type_out:     {:#x}\n"
                              "    type_in1:     {:#x}\n"
                              "    type_in2:     {:#x}\n"
                              "    wd_ref:       {:#x}\n"
                              "    wd_sc_out:    {:#x}\n"
                              "    wd_sc_in1:    {:#x}\n"
                              "    wd_sc_in2:    {:#x}\n"
                              "  Operation:\n"
                              "    op:           {:#x}\n"
                              "  Size:\n"
                              "    rem_dim0_ref: {:#x}\n"
                              "    size_dim0b_ref: {:#x}\n"
                              ")",
                              cfg.cfg_type_out, cfg.cfg_type_in1, cfg.cfg_type_in2,
                              cfg.cfg_wd_ref, cfg.cfg_wd_sc_out, cfg.cfg_wd_sc_in1, cfg.cfg_wd_sc_in2,
                              cfg.cfg_op,
                              cfg.cfg_rem_dim0_ref, cfg.cfg_size_dim0b_ref);
    }
};

// CimTensorProcessConfig 格式化器
template <>
struct fmt::formatter<hardware_instruction::CimTensorProcessConfig>
{
    template <typename ParseContext>
    auto parse(ParseContext& ctx) const -> decltype(ctx.begin())
    {
        return ctx.begin();
    }

    template <typename FormatContext>
    auto format(const hardware_instruction::CimTensorProcessConfig& cfg, FormatContext& ctx) const -> decltype(ctx.out())
    {
        return fmt::format_to(ctx.out(),
                              "CimTensorProcessConfig(\n"
                              "  Precision:\n"
                              "    type_out:     {:#x}\n"
                              "    type_orig:    {:#x}\n"
                              "    type_in:      {:#x}\n"
                              "    type_wt:      {:#x}\n"
                              "    wd_out:       {:#x}\n"
                              "    wd_orig:      {:#x}\n"
                              "    wd_in:        {:#x}\n"
                              "    wd_wt:        {:#x}\n"
                              "  Operation:\n"
                              "    accu:         {:#x}\n"
                              "    act:          {:#x}\n"
                              "    shift:        {:#x}\n"
                              "  TensorO:\n"
                              "    rem_dim0_out: {:#x}\n"
                              "    size_dim0b_out: {:#x}\n"
                              "    size_dim1_out: {:#x}\n"
                              "    stride_dim1_out: {:#x}\n"
                              "  TensorI:\n"
                              "    rem_dim0_in:  {:#x}\n"
                              "    size_dim0b_in: {:#x}\n"
                              "    size_dim1_in: {:#x}\n"
                              "    stride_dim1_in: {:#x}\n"
                              ")",
                              // Precision
                              cfg.cfg_type_out, cfg.cfg_type_orig, cfg.cfg_type_in, cfg.cfg_type_wt,
                              cfg.cfg_wd_out, cfg.cfg_wd_orig, cfg.cfg_wd_in, cfg.cfg_wd_wt,
                              // Operation
                              cfg.cfg_accu, cfg.cfg_act, cfg.cfg_shift,
                              // Tensor O
                              cfg.cfg_rem_dim0_out, cfg.cfg_size_dim0b_out, cfg.cfg_size_dim1_out, cfg.cfg_stride_dim1_out,
                              // Tensor I
                              cfg.cfg_rem_dim0_in, cfg.cfg_size_dim0b_in, cfg.cfg_size_dim1_in, cfg.cfg_stride_dim1_in);
    }
};

// FieldDef 格式化器
template <>
struct fmt::formatter<hardware_instruction::FieldDef>
{
    template <typename ParseContext>
    auto parse(ParseContext& ctx) const -> decltype(ctx.begin())
    {
        return ctx.begin();
    }

    template <typename FormatContext>
    auto format(const hardware_instruction::FieldDef& field, FormatContext& ctx) const -> decltype(ctx.out())
    {
        return fmt::format_to(ctx.out(),
                              "FieldDef(name:{}, addr:{:#x}, range:[{}:{}])",
                              field.name ? field.name : "null",
                              field.addr, field.left, field.right);
    }
};

// ConfigType 枚举格式化器
template <>
struct fmt::formatter<hardware_instruction::ConfigType>
{
    template <typename ParseContext>
    auto parse(ParseContext& ctx) const -> decltype(ctx.begin())
    {
        return ctx.begin();
    }

    template <typename FormatContext>
    auto format(const hardware_instruction::ConfigType& type, FormatContext& ctx) const -> decltype(ctx.out())
    {
        const char* type_name;
        switch (type)
        {
            case hardware_instruction::ConfigType::TLD:
                type_name = "TLD";
                break;
            case hardware_instruction::ConfigType::TST:
                type_name = "TST";
                break;
            case hardware_instruction::ConfigType::TM:
                type_name = "TM";
                break;
            case hardware_instruction::ConfigType::MP:
                type_name = "MP";
                break;
            case hardware_instruction::ConfigType::VP:
                type_name = "VP";
                break;
            case hardware_instruction::ConfigType::CIM_TP:
                type_name = "CIM_TP";
                break;
            default:
                type_name = "UNKNOWN";
                break;
        }
        return fmt::format_to(ctx.out(), "ConfigType::{}", type_name);
    }
};

// IssueQueueCmd 格式化器
template <>
struct fmt::formatter<instruction::IssueQueueCmd>
{
    template <typename ParseContext>
    auto parse(ParseContext& ctx) const -> decltype(ctx.begin())
    {
        return ctx.begin();
    }

    template <typename FormatContext>
    auto format(const instruction::IssueQueueCmd& cmd, FormatContext& ctx) const -> decltype(ctx.out())
    {
        return fmt::format_to(ctx.out(),
                              "IssueQueueCmd(\n"
                              "  funct7:         {:#x}\n"
                              "  xd:             {}\n"
                              "  xs1:            {}\n"
                              "  xs2:            {}\n"
                              "  rs1val:         {:#x}\n"
                              "  rs2val:         {:#x}\n"
                              ")",
                              static_cast<unsigned int>(cmd.funct7),
                              cmd.xd, cmd.xs1, cmd.xs2, cmd.rs1val, cmd.rs2val);
    }
};

// 实现 register.h 中声明的方法
namespace hardware_instruction
{

template <ConfigType Type>
void ConfigManager<Type>::logConfig() const
{
    typename Traits::Config cfg = getConfig();
    SC_INFO("CONFIG_LOG", "Configuration: {}", cfg);
}

template <ConfigType Type>
void ConfigManager<Type>::debugConfig() const
{
    typename Traits::Config cfg = getConfig();
    SC_DEBUG("CONFIG_DEBUG", "Configuration: {}", cfg);
}

template <ConfigType Type>
void ConfigManager<Type>::logAll() const
{
    SC_INFO("REG_VALUES", "=== Register Values ===");
    for (std::map<uint32_t, sc_dt::sc_uint<32> >::const_iterator it = registers.begin();
         it != registers.end(); ++it)
    {
        SC_INFO("REG_ADDR", "Address {:#x}: {:#x}", it->first, it->second.to_uint());
    }
    SC_INFO("CONFIG_VALUES", "=== Configuration Values ===");
    logConfig();
}

template <ConfigType Type>
void ConfigManager<Type>::debugAll() const
{
    SC_DEBUG("DEBUG_REG_VALUES", "=== Register Values ===");
    for (std::map<uint32_t, sc_dt::sc_uint<32> >::const_iterator it = registers.begin();
         it != registers.end(); ++it)
    {
        SC_DEBUG("DEBUG_REG_ADDR", "Address {:#x}: {:#x}", it->first, it->second.to_uint());
    }
    SC_DEBUG("DEBUG_CONFIG_VALUES", "=== Configuration Values ===");
    debugConfig();
}

// 显式实例化模板
template class ConfigManager<ConfigType::TLD>;
template class ConfigManager<ConfigType::TST>;
template class ConfigManager<ConfigType::TM>;
template class ConfigManager<ConfigType::MP>;
template class ConfigManager<ConfigType::VP>;
template class ConfigManager<ConfigType::CIM_TP>;

}  // namespace hardware_instruction

#endif  // REGISTER_FORMATTERS_H 