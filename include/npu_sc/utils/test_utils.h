#pragma once

#include <cmath>
#include <cstdint>
#include <cstring>
#include <vector>
#include "npu_config.h"
#include "utils.h"  // for glb_mh2f_conv

namespace vpu
{
namespace test
{
using Word256b = std::array<uint8_t, NPUConfig::LMEM_WD / 8>;
class TestUtils
{
  public:
    // Float unpacking
    static std::vector<float> unpackFloat(uint32_t packed, uint8_t prec, bool is_format2)
    {
        std::vector<float> result;

        if (prec == dtype::FP32)
        {
            float val = 0;
            glb_mh2f_conv(packed, dtype::FP32, &val);
            result.push_back(val);
            return result;
        }

        // 对于BF16和FP16
        int count = is_format2 ? 1 : 2;
        for (int i = 0; i < count; i++)
        {
            uint32_t val = (packed >> (i * 16)) & 0xFFFF;
            float float_val = 0;
            glb_mh2f_conv(val, prec, &float_val);
            result.push_back(float_val);
        }

        return result;
    }

    // Integer Format1 packing
    static uint32_t packFormat1_INT4(const std::vector<int32_t>& data)
    {
        uint32_t result = 0;
        for (int i = 0; i < 8; i++)
        {
            result |= ((static_cast<uint32_t>(data[i]) & 0xF) << (i * 4));
        }
        return result;
    }

    static uint32_t packFormat1_INT8(const std::vector<int32_t>& data)
    {
        uint32_t result = 0;
        for (int i = 0; i < 4; i++)
        {
            result |= ((static_cast<uint32_t>(data[i]) & 0xFF) << (i * 8));
        }
        return result;
    }

    static uint32_t packFormat1_INT16(const std::vector<int32_t>& data)
    {
        uint32_t result = 0;
        for (int i = 0; i < 2; i++)
        {
            result |= ((static_cast<uint32_t>(data[i]) & 0xFFFF) << (i * 16));
        }
        return result;
    }

    static uint32_t packFormat1_INT32(const std::vector<int32_t>& data)
    {
        return static_cast<uint32_t>(data[0]);
    }

    // Integer Format2 packing
    static uint32_t packFormat2_INT4(const std::vector<int32_t>& data)
    {
        uint32_t result = 0;
        for (int i = 0; i < 4; i++)
        {
            result |= ((static_cast<uint32_t>(data[i]) & 0xF) << (i * 8));
        }
        return result;
    }

    static uint32_t packFormat2_INT8(const std::vector<int32_t>& data)
    {
        uint32_t result = 0;
        for (int i = 0; i < 2; i++)
        {
            result |= ((static_cast<uint32_t>(data[i]) & 0xFF) << (i * 16));
        }
        return result;
    }

    static uint32_t packFormat2_INT16(const std::vector<int32_t>& data)
    {
        return static_cast<uint32_t>(data[0]) & 0xFFFF;
    }

    // Float packing
    static uint32_t packFloat(const std::vector<int32_t>& data, uint8_t prec, bool is_format2)
    {
        uint32_t result = 0;

        if (prec == dtype::FP32)
        {
            return static_cast<uint32_t>(data[0]);
        }

        if (is_format2)
        {
            result = (static_cast<uint32_t>(data[0] & 0xFFFF));
        }
        else
        {
            result = (static_cast<uint32_t>(data[0] & 0xFFFF)) |
                     (static_cast<uint32_t>(data[1] & 0xFFFF) << 16);
        }
        return result;
    }

    // Generic packing
    static uint32_t packData(const std::vector<int32_t>& data, uint8_t prec, bool is_format2)
    {
        if (prec == dtype::BF16 || prec == dtype::FP16 || prec == dtype::FP32)
        {
            return packFloat(data, prec, is_format2);
        }

        switch (prec)
        {
            case dtype::INT4:
                return is_format2 ? packFormat2_INT4(data) : packFormat1_INT4(data);
            case dtype::INT8:
                return is_format2 ? packFormat2_INT8(data) : packFormat1_INT8(data);
            case dtype::INT16:
                return is_format2 ? packFormat2_INT16(data) : packFormat1_INT16(data);
            case dtype::INT32:
                return packFormat1_INT32(data);
            default:
                return 0;
        }
    }

    // Integer unpacking
    static std::vector<int32_t> unpackData(uint32_t packed, uint8_t prec, bool is_format2)
    {
        std::vector<int32_t> result;
        uint32_t mask;
        int count;
        int shift;

        switch (prec)
        {
            case dtype::INT4:
                mask = 0xF;
                count = is_format2 ? 4 : 8;
                shift = is_format2 ? 8 : 4;
                break;
            case dtype::INT8:
                mask = 0xFF;
                count = is_format2 ? 2 : 4;
                shift = is_format2 ? 16 : 8;
                break;
            case dtype::INT16:
                mask = 0xFFFF;
                count = is_format2 ? 1 : 2;
                shift = 16;
                break;
            case dtype::INT32:
                return {static_cast<int32_t>(packed)};
            default:
                return result;
        }

        for (int i = 0; i < count; i++)
        {
            uint32_t val = (packed >> (i * shift)) & mask;
            switch (prec)
            {
                case dtype::INT4:
                    if (val & 0x8)
                        val |= 0xFFFFFFF0;
                    break;
                case dtype::INT8:
                    if (val & 0x80)
                        val |= 0xFFFFFF00;
                    break;
                case dtype::INT16:
                    if (val & 0x8000)
                        val |= 0xFFFF0000;
                    break;
            }
            result.push_back(static_cast<int32_t>(val));
        }
        return result;
    }

    static Word256b convertToWord256b(const std::vector<uint32_t>& input,
                                      bool remove_padding,
                                      uint8_t precision)
    {
        Word256b result{};  // 初始化为全0

        if (!remove_padding)
        {
            // 逐字节拷贝，确保正确的字节序
            for (size_t i = 0; i < input.size(); i++)
            {
                uint32_t val = input[i];
                result[i * 4 + 0] = (val >> 0) & 0xFF;  // LSB
                result[i * 4 + 1] = (val >> 8) & 0xFF;
                result[i * 4 + 2] = (val >> 16) & 0xFF;
                result[i * 4 + 3] = (val >> 24) & 0xFF;  // MSB
            }
            return result;
        }

        // 去除填充后拼接到低128位
        uint8_t actual_prec = precision & 0x7;
        size_t result_idx = 0;
        uint32_t mask;
        int values_per_word;
        int actual_bits;  // 实际位宽

        switch (static_cast<dtype::WidthCode>(actual_prec))
        {
            case dtype::WidthCode::W4:
                mask = 0xF;
                values_per_word = 4;
                actual_bits = 8;
                break;
            case dtype::WidthCode::W8:
                mask = 0xFF;
                values_per_word = 2;
                actual_bits = 16;
                break;
            case dtype::WidthCode::W16:
                mask = 0xFFFF;
                values_per_word = 1;
                actual_bits = 16;
                break;
            default:
                return result;
        }

        // 处理每个输入的uint32_t
        for (const auto& val : input)
        {
            uint32_t temp = 0;
            // 提取每个word中的有效值并打包
            for (int j = 0; j < values_per_word; ++j)
            {
                switch (static_cast<dtype::WidthCode>(actual_prec))
                {
                    case dtype::WidthCode::W4:
                        // 每4位一组，打包成连续数据
                        temp |= ((val >> (j * actual_bits)) & mask) << (j * actual_bits / 2);
                        if (j == values_per_word - 1)
                        {
                            memcpy(&result[result_idx], &temp, 2);  // INT4需要2字节
                            result_idx += 2;
                        }
                        break;

                    case dtype::WidthCode::W8:
                        // 每8位一组，打包成连续数据
                        temp |= ((val >> (j * actual_bits)) & mask) << (j * actual_bits / 2);
                        if (j == values_per_word - 1)
                        {
                            memcpy(&result[result_idx], &temp, 2);  // INT8需要2字节
                            result_idx += 2;
                        }
                        break;

                    case dtype::WidthCode::W16:
                        // 16位直接写入
                        temp = val & mask;
                        memcpy(&result[result_idx], &temp, 2);  // INT16需要2字节
                        result_idx += 2;
                        break;
                    default:
                        break;
                }
            }
        }

        return result;
    }

    // Float comparison
    static bool isFloatEqual(float a, float b, float tolerance)
    {
        if (std::isnan(a) && std::isnan(b))
            return true;
        if (std::isinf(a) && std::isinf(b) && ((a > 0) == (b > 0)))
            return true;
        return std::abs(a - b) <= tolerance * std::max(1.0f, std::max(std::abs(a), std::abs(b)));
    }
};

}  // namespace test
}  // namespace vpu