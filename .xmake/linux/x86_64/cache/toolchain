{
    tool_target_dcim_macro_cpp_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_tmu_test_base_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_array_wt_fp8e5_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_local_mem_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_feat_tmu_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_float_wt_fp8e5_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_float_wt_bf16_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_engine_wt_fp8e5_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_engine_wt_int8_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_array_wt_int16_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_dcim_engine_cpp_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_array_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_array_wt_int8_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_float_wt_fp16_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_storage_compute_wt_fp8e4_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_array_wt_bf16_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_engine_wt_int16_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_array_wt_fp16_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_engine_wt_fp8e4_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_common_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_storage_compute_wt_fp8e5_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_cluster_linux_x86_64_ld = {
        toolname = "gxx",
        toolchain_info = {
            name = "cross",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            plat = "linux"
        },
        program = "/usr/bin/g++"
    },
    tool_target_test_float_wt_int4_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_lmem_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_engine_wt_fp16_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_cluster_wt_fp8e4_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_float_wt_fp8e4_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_cluster_wt_int8_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_storage_compute_wt_bf16_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_array_wt_int4_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_storage_compute_wt_int8_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_engine_linux_x86_64_ld = {
        toolname = "gxx",
        toolchain_info = {
            name = "cross",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            plat = "linux"
        },
        program = "/usr/bin/g++"
    },
    tool_target_test_float_linux_x86_64_ld = {
        toolname = "gxx",
        toolchain_info = {
            name = "cross",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            plat = "linux"
        },
        program = "/usr/bin/g++"
    },
    tool_target_test_storage_compute_wt_fp16_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_array_linux_x86_64_ld = {
        toolname = "gxx",
        toolchain_info = {
            name = "cross",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            plat = "linux"
        },
        program = "/usr/bin/g++"
    },
    tool_target_test_dcim_cluster_wt_fp8e5_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_storage_compute_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_dcim_cluster_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_float_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_storage_compute_wt_int4_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_tmu_mov_test_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_cluster_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_cluster_wt_fp16_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_engine_wt_int4_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_cluster_wt_int16_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_storage_compute_wt_int16_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_engine_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_engine_wt_bf16_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_cluster_wt_bf16_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_storage_compute_linux_x86_64_ld = {
        toolname = "gxx",
        toolchain_info = {
            name = "cross",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            plat = "linux"
        },
        program = "/usr/bin/g++"
    },
    tool_target_test_dcim_array_wt_fp8e4_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_tmu_bc_test_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_float_wt_int8_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_dcim_cluster_wt_int4_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_test_float_wt_int16_linux_x86_64_cxx = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    },
    tool_target_dcim_macro_linux_x86_64_cc = {
        toolname = "gcc",
        toolchain_info = {
            plat = "linux",
            arch = "x86_64",
            cachekey = "cross_arch_x86_64_plat_linux",
            name = "cross"
        },
        program = "/usr/bin/gcc"
    }
}