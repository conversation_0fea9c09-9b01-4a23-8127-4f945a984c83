{
    find_program = {
        ["/usr/bin/g++"] = "/usr/bin/g++",
        ["/usr/bin/gcc"] = "/usr/bin/gcc"
    },
    find_program_cross_arch_x86_64_plat_linux_checktoolcc = {
        gcc = "/usr/bin/gcc"
    },
    find_program_cross_arch_x86_64_plat_linux_checktoolld = {
        ["g++"] = "/usr/bin/g++"
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["/usr/bin/g++_7.3.0"] = {
            ["--output"] = true,
            ["--dynamic-linker"] = true,
            ["-Map"] = true,
            ["--no-ld-generated-unwind-info"] = true,
            ["-I"] = true,
            ["--enable-extra-pep-debug"] = true,
            ["--omagic"] = true,
            ["-Bgroup"] = true,
            ["-c"] = true,
            ["--fatal-warnings"] = true,
            ["-flto"] = true,
            ["--pop-state"] = true,
            ["--major-subsystem-version"] = true,
            ["--entry"] = true,
            ["--section-start"] = true,
            ["--nmagic"] = true,
            ["-A"] = true,
            ["-nostdlib"] = true,
            ["-Tbss"] = true,
            ["--major-image-version"] = true,
            ["--disable-long-section-names"] = true,
            ["--warn-multiple-gp"] = true,
            ["--section-alignment"] = true,
            ["--cref"] = true,
            ["--disable-auto-import"] = true,
            ["--warn-alternate-em"] = true,
            ["--gc-keep-exported"] = true,
            ["-l"] = true,
            ["--push-state"] = true,
            ["--disable-auto-image-base"] = true,
            ["--ld-generated-unwind-info"] = true,
            ["--ignore-unresolved-symbol"] = true,
            ["--no-whole-archive"] = true,
            ["-Y"] = true,
            ["--dynamic-list-cpp-typeinfo"] = true,
            ["-plugin-opt"] = true,
            ["--enable-auto-image-base"] = true,
            ["--no-check-sections"] = true,
            ["--wdmdriver"] = true,
            ["--export-all-symbols"] = true,
            ["--require-defined"] = true,
            ["--warn-section-align"] = true,
            ["--print-map"] = true,
            ["-a"] = true,
            ["--no-print-gc-sections"] = true,
            ["--reduce-memory-overheads"] = true,
            ["-Ttext-segment"] = true,
            ["--verbose"] = true,
            ["--compat-implib"] = true,
            ["--script"] = true,
            ["--minor-image-version"] = true,
            ["--no-warn-search-mismatch"] = true,
            ["-qmagic"] = true,
            ["--dynamic-list"] = true,
            ["--enable-auto-import"] = true,
            ["--no-allow-shlib-undefined"] = true,
            ["--print-output-format"] = true,
            ["-Bshareable"] = true,
            ["-Qy"] = true,
            ["--help"] = true,
            ["-init"] = true,
            ["--strip-discarded"] = true,
            ["--no-isolation"] = true,
            ["--subsystem"] = true,
            ["-T"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--default-script"] = true,
            ["--no-accept-unknown-input-arch"] = true,
            ["--add-stdcall-alias"] = true,
            ["-F"] = true,
            ["-b"] = true,
            ["--accept-unknown-input-arch"] = true,
            ["--library-path"] = true,
            ["-V"] = true,
            ["--target-help"] = true,
            ["--exclude-modules-for-implib"] = true,
            ["--warn-duplicate-exports"] = true,
            ["--allow-shlib-undefined"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--defsym"] = true,
            ["--warn-unresolved-symbols"] = true,
            ["--emit-relocs"] = true,
            ["--no-gc-sections"] = true,
            ["--warn-shared-textrel"] = true,
            ["--sort-section"] = true,
            ["--minor-subsystem-version"] = true,
            ["--heap"] = true,
            ["--as-needed"] = true,
            ["--library"] = true,
            ["--split-by-file"] = true,
            ["--disable-large-address-aware"] = true,
            ["-rpath"] = true,
            ["--force-exe-suffix"] = true,
            ["--auxiliary"] = true,
            ["--no-strip-discarded"] = true,
            ["-rpath-link"] = true,
            ["-Bsymbolic"] = true,
            ["-plugin"] = true,
            ["--just-symbols"] = true,
            ["--architecture"] = true,
            ["-dT"] = true,
            ["--no-copy-dt-needed-entries"] = true,
            ["--demangle"] = true,
            ["--exclude-libs"] = true,
            ["--embedded-relocs"] = true,
            ["--enable-new-dtags"] = true,
            ["--copy-dt-needed-entries"] = true,
            ["--minor-os-version"] = true,
            ["--warn-once"] = true,
            ["--large-address-aware"] = true,
            ["--no-demangle"] = true,
            ["--gpsize"] = true,
            ["--no-bind"] = true,
            ["-o"] = true,
            ["--strip-debug"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--print-gc-sections"] = true,
            ["--no-warn-mismatch"] = true,
            ["--eh-frame-hdr"] = true,
            ["--no-undefined"] = true,
            ["--forceinteg"] = true,
            ["-y"] = true,
            ["--undefined"] = true,
            ["--image-base"] = true,
            ["--no-as-needed"] = true,
            ["--discard-all"] = true,
            ["-plugin-save-temps"] = true,
            ["-g"] = true,
            ["--no-keep-memory"] = true,
            ["--no-export-dynamic"] = true,
            ["--exclude-symbols"] = true,
            ["--gc-sections"] = true,
            ["--nxcompat"] = true,
            ["--dynamicbase"] = true,
            ["--major-os-version"] = true,
            ["--dll"] = true,
            ["-P"] = true,
            ["--discard-locals"] = true,
            ["--no-fatal-warnings"] = true,
            ["--no-map-whole-files"] = true,
            ["-Tldata-segment"] = true,
            ["-Bsymbolic-functions"] = true,
            ["-fini"] = true,
            ["-Tdata"] = true,
            ["--filter"] = true,
            ["--oformat"] = true,
            ["-Trodata-segment"] = true,
            ["--end-group"] = true,
            ["--pic-executable"] = true,
            ["-soname"] = true,
            ["-Ur"] = true,
            ["--print-sysroot"] = true,
            ["-z"] = true,
            ["--tsaware"] = true,
            ["-Ttext"] = true,
            ["--traditional-format"] = true,
            ["--relocatable"] = true,
            ["--allow-multiple-definition"] = true,
            ["-h"] = true,
            ["-e"] = true,
            ["-assert"] = true,
            ["--split-by-reloc"] = true,
            ["--default-symver"] = true,
            ["--task-link"] = true,
            ["--exclude-all-symbols"] = true,
            ["--no-eh-frame-hdr"] = true,
            ["--map-whole-files"] = true,
            ["--unique"] = true,
            ["--retain-symbols-file"] = true,
            ["-O"] = true,
            ["-G"] = true,
            ["-debug"] = true,
            ["--dynamic-list-data"] = true,
            ["--stack"] = true,
            ["--default-imported-symver"] = true,
            ["--print-memory-usage"] = true,
            ["-u"] = true,
            ["--relax"] = true,
            ["--mri-script"] = true,
            ["--export-dynamic"] = true,
            ["--wrap"] = true,
            ["--version-exports-section"] = true,
            ["--format"] = true,
            ["--no-relax"] = true,
            ["--orphan-handling"] = true,
            ["--trace"] = true,
            ["-m"] = true,
            ["--spare-dynamic-tags"] = true,
            ["--trace-symbol"] = true,
            ["--dynamic-list-cpp-new"] = true,
            ["--no-seh"] = true,
            ["--out-implib"] = true,
            ["--whole-archive"] = true,
            ["--file-alignment"] = true,
            ["--warn-common"] = true,
            ["--error-unresolved-symbols"] = true,
            ["-dp"] = true,
            ["--support-old-code"] = true,
            ["--disable-new-dtags"] = true,
            ["--force-group-allocation"] = true,
            ["--enable-long-section-names"] = true,
            ["--high-entropy-va"] = true,
            ["-R"] = true,
            ["--sort-common"] = true,
            ["--no-dynamic-linker"] = true,
            ["-f"] = true,
            ["--strip-all"] = true,
            ["--output-def"] = true,
            ["-L"] = true,
            ["--version"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--warn-constructors"] = true,
            ["--check-sections"] = true,
            ["--kill-at"] = true,
            ["--start-group"] = true,
            ["--enable-extra-pe-debug"] = true,
            ["--no-omagic"] = true,
            ["--no-undefined-version"] = true,
            ["-static"] = true,
            ["--discard-none"] = true,
            ["--stats"] = true,
            ["-EL"] = true,
            ["--no-define-common"] = true,
            ["--version-script"] = true
        }
    },
    ["lib.detect.has_flags"] = {
        ["linux_x86_64_/usr/bin/g++_7.3.0_ld__-m64 -m64 -target x86_64-linux-gnu -m64 -m64 -target x86_64-linux-gnu_-fPIC"] = false,
        ["linux_x86_64_/usr/bin/gcc_7.3.0_cxx__-m64 -m64 -target x86_64-linux-gnu_-fPIC"] = false,
        ["linux_x86_64_/usr/bin/gcc_7.3.0_cc__-m64 -m64 -target x86_64-linux-gnu_-fPIC"] = false
    },
    find_program_cross_arch_x86_64_plat_linux_checktoolcxx = {
        gcc = "/usr/bin/gcc"
    },
    ["core.tools.gcc.has_cflags"] = {
        ["/usr/bin/gcc_7.3.0"] = {
            ["-print-search-dirs"] = true,
            ["-print-sysroot-headers-suffix"] = true,
            ["-o"] = true,
            ["-print-multi-os-directory"] = true,
            ["--target-help"] = true,
            ["--help"] = true,
            ["-print-multi-directory"] = true,
            ["-Xassembler"] = true,
            ["-pass-exit-codes"] = true,
            ["--version"] = true,
            ["-S"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-Xpreprocessor"] = true,
            ["-Xlinker"] = true,
            ["-c"] = true,
            ["-print-multi-lib"] = true,
            ["-print-sysroot"] = true,
            ["--param"] = true,
            ["-save-temps"] = true,
            ["-no-canonical-prefixes"] = true,
            ["-pie"] = true,
            ["-print-multiarch"] = true,
            ["-pipe"] = true,
            ["-dumpmachine"] = true,
            ["-time"] = true,
            ["-v"] = true,
            ["-x"] = true,
            ["-dumpversion"] = true,
            ["-E"] = true,
            ["-shared"] = true,
            ["-B"] = true,
            ["-dumpspecs"] = true
        }
    },
    find_programver = {
        ["/usr/bin/g++"] = "7.3.0",
        ["/usr/bin/gcc"] = "7.3.0"
    }
}