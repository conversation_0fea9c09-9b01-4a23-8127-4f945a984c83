#
# //------------------------------------------------------------//
# //   Copyright 2012 Synopsys, Inc                             //
# //   All Rights Reserved Worldwid                             //
# //                                                            //
# //   Licensed under the Apache License, Version 2.0 (the      //
# //   "License"); you may not use this file except in          //
# //   compliance with the License.  You may obtain a copy of   //
# //   the License at                                           //
# //                                                            //
# //       http://www.apache.org/licenses/LICENSE-2.0           //
# //                                                            //
# //   Unless required by applicable law or agreed to in        //
# //   writing, software distributed under the License is       //
# //   distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR   //
# //   CONDITIONS OF ANY KIND, either express or implied.  See  //
# //   the License for the specific language governing          //
# //   permissions and limitations under the License.           //
# //------------------------------------------------------------//

UVMC_HOME ?= ../..

# Include makefile.inc for include paths configuration
include include.mk
include vcscfg.mk


help:
	@echo " -----------------------------------------------------------------";
	@echo "|                  UVMC EXAMPLES - CONNECTIONS                    |";
	@echo " -----------------------------------------------------------------";
	@echo "|                                                                 |";
	@echo "| Usage:                                                          |";
	@echo "|                                                                 |";
	@echo "|   make [UVM_HOME=path] [UVMC_HOME=path] <example>               |";
	@echo "|                                                                 |";
	@echo "| where <example> is one or more of:                              |";
	@echo "|                                                                 |";
	@echo "|   sv2sc_struct : SV producer --> SC consumer                    |";
	@echo "|                  Connection is made via UVMC                    |";
	@echo "|   local_mem_test    : SV producer --> SC local_mem slave             |";
	@echo "|                  Mixed SystemC/SystemVerilog simulation         |";
		@echo "|   feat_tmu_test     : SV producer --> SC feat_tmu cfg_socket     |";
		@echo "|                  Mixed SystemC/SystemVerilog simulation         |";
	@echo "|                                                                 |";
	@echo "| UVM_HOME and UVMC_HOME specify the location of the source       |";
	@echo "| headers and macro definitions needed by the examples. You must  |";
	@echo "| specify their locations via UVM_HOME and UVMC_HOME environment  |";
	@echo "| variables or make command line options. Command line options    |";
	@echo "| override any envrionment variable settings.                     |";
	@echo "|                                                                 |";
	@echo "| The UVM and UVMC libraries must be compiled prior to running    |";
	@echo "| any example. If the libraries are not at their default location |";
	@echo "| (UVMC_HOME/lib) then you must specify their location via the    |";
	@echo "| UVM_LIB and/or UVMC_LIB environment variables or make command   |";
	@echo "| line options. Make command line options take precedence.        |";
	@echo "|                                                                 |";
	@echo "| Other options:                                                  |";
	@echo "|                                                                 |";
	@echo "|   all   : Run all examples                                      |";
	@echo "|   clean : Remove simulation files and directories               |";
	@echo "|   help  : Print this help information                           |";
	@echo "|                                                                 |";
	@echo "|                                                                 |";
	@echo " -----------------------------------------------------------------";

all: sv2sc_struct

# New target for mixed simulation with local_mem
local_mem_test:
	$(MAKE) -f Makefile clean comp run EXAMPLE=local_mem_test

# New target for mixed simulation with feat_tmu
feat_tmu_test:
	$(MAKE) -f Makefile clean comp run EXAMPLE=feat_tmu_test

sv2sc_struct:
	$(MAKE) -f Makefile clean comp run EXAMPLE=sv2sc_struct

comp:
	$(SYSCAN)  -cflags "$(INCLUDE_FLAGS) $(CXXFLAGS_EXTRA)"  $(NPU_SC_SOURCES) $(CIM_CLUSTER_SOURCES) $(LOCAL_MEM_SOURCES) $(FEAT_TMU_SOURCES) test/$(EXAMPLE).cpp
	$(VLOGAN) +incdir+common test/$(EXAMPLE).sv +define+UVM_OBJECT_MUST_HAVE_CONSTRUCTOR
	$(VCS_ELAB) sv_main sc_main


sim: run

run:	
	$(SIMV) $(TRACE_OPT) $(ARGS) +UVM_NO_RELNOTES 2>&1 |tee $(EXAMPLE)_run.log
	grep "UVM_ERROR : *0$$" $(EXAMPLE)_run.log
	grep "UVM_FATAL : *0$$" $(EXAMPLE)_run.log

# Debug target
debug:
	@echo "VCS_HOME: $(VCS_HOME)"
	@echo "UVMC_HOME: $(UVMC_HOME)"
	@echo "UVM_HOME: $(UVM_HOME)"
	@echo "INCLUDE_FLAGS: $(INCLUDE_FLAGS)"
	@echo "CXXFLAGS_EXTRA: $(CXXFLAGS_EXTRA)"

.PHONY: all clean comp sim run debug help
