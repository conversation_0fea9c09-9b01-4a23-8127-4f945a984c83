#
# makefile.inc - Include paths configuration for subprojects
# This file contains all the include path definitions for various subprojects
#

# Base include directory
INCLUDE_BASE := include

# AC Types include paths
AC_TYPES_BASE := $(INCLUDE_BASE)/ac_types-master
AC_TYPES_INCLUDE := $(AC_TYPES_BASE)/include

# NPU SystemC utilities include paths  
NPU_SC_BASE := $(INCLUDE_BASE)/npu_sc
NPU_SC_INCLUDE := $(NPU_SC_BASE)
NPU_SC_UTILS_INCLUDE := $(NPU_SC_BASE)/utils
# SPDLOG include paths
SPDLOG_BASE := $(INCLUDE_BASE)/spdlog
SPDLOG_INCLUDE := $(SPDLOG_BASE)
SPDLOG_CFG_INCLUDE := $(SPDLOG_BASE)/cfg
SPDLOG_DETAILS_INCLUDE := $(SPDLOG_BASE)/details
SPDLOG_FMT_INCLUDE := $(SPDLOG_BASE)/fmt
SPDLOG_FMT_BUNDLED_INCLUDE := $(SPDLOG_BASE)/fmt/bundled
SPDLOG_SINKS_INCLUDE := $(SPDLOG_BASE)/sinks

# CIM Cluster include paths
CIM_CLUSTER_BASE := src/cim_cluster
CIM_CLUSTER_INCLUDE := $(CIM_CLUSTER_BASE)/inc
CIM_CLUSTER_SRC := $(CIM_CLUSTER_BASE)/src

# Local Memory include paths
LOCAL_MEM_BASE := src/local_mem
LOCAL_MEM_INCLUDE := $(LOCAL_MEM_BASE)

# FeatTMU include paths
FEAT_TMU_BASE := src/feat_tmu
FEAT_TMU_INCLUDE := $(FEAT_TMU_BASE)

# Consolidated include flags for compiler
INCLUDE_FLAGS := -I$(INCLUDE_BASE) \
                 -I$(AC_TYPES_INCLUDE) \
                 -I$(NPU_SC_INCLUDE) \
                 -I$(NPU_SC_UTILS_INCLUDE) \
                 -I$(SPDLOG_INCLUDE) \
                 -I$(SPDLOG_CFG_INCLUDE) \
                 -I$(SPDLOG_DETAILS_INCLUDE) \
                 -I$(SPDLOG_FMT_INCLUDE) \
                 -I$(SPDLOG_FMT_BUNDLED_INCLUDE) \
                 -I$(SPDLOG_SINKS_INCLUDE) \
                 -I$(CIM_CLUSTER_INCLUDE) \
                 -I$(LOCAL_MEM_INCLUDE) \
                 -I$(FEAT_TMU_INCLUDE) \
                 -Icommon

# Additional compiler flags
CXXFLAGS_EXTRA := -std=c++11

# Source file definitions for SystemC modules
CIM_CLUSTER_SOURCES := $(wildcard $(CIM_CLUSTER_SRC)/*.cpp) $(wildcard $(CIM_CLUSTER_SRC)/*.c)
LOCAL_MEM_SOURCES := $(wildcard $(LOCAL_MEM_BASE)/*.cpp)
FEAT_TMU_SOURCES := $(wildcard $(FEAT_TMU_BASE)/*.cpp)
NPU_SC_SOURCES := $(wildcard $(NPU_SC_BASE)/*.cpp)
# Export variables for use in other makefiles
export INCLUDE_FLAGS
export CXXFLAGS_EXTRA
export AC_TYPES_INCLUDE
export NPU_SC_INCLUDE
export NPU_SC_UTILS_INCLUDE
export SPDLOG_INCLUDE
export CIM_CLUSTER_INCLUDE
export CIM_CLUSTER_SOURCES
export LOCAL_MEM_INCLUDE
export LOCAL_MEM_SOURCES
export FEAT_TMU_INCLUDE
export FEAT_TMU_SOURCES
export NPU_SC_SOURCES

# Tree structure as reference:
# include/
# ├── ac_types-master/
# │   ├── gdb/
# │   └── include/           <- AC_TYPES_INCLUDE
# ├── npu_sc/               <- NPU_SC_INCLUDE
# │   └── utils/             <- NPU_SC_UTILS_INCLUDE
# └── spdlog/                <- SPDLOG_INCLUDE
#     ├── cfg/               <- SPDLOG_CFG_INCLUDE
#     ├── details/           <- SPDLOG_DETAILS_INCLUDE
#     ├── fmt/               <- SPDLOG_FMT_INCLUDE
#     │   └── bundled/       <- SPDLOG_FMT_BUNDLED_INCLUDE
#     └── sinks/             <- SPDLOG_SINKS_INCLUDE
#
# src/
# ├── cim_cluster/           <- CIM_CLUSTER_BASE
# │   ├── inc/               <- CIM_CLUSTER_INCLUDE
# │   └── src/               <- CIM_CLUSTER_SRC
# └── local_mem/             <- LOCAL_MEM_BASE, LOCAL_MEM_INCLUDE

# Debug: Print include paths (uncomment for debugging)
# $(info AC_TYPES_INCLUDE: $(AC_TYPES_INCLUDE))
# $(info NPU_SC_INCLUDE: $(NPU_SC_INCLUDE))
# $(info NPU_SC_UTILS_INCLUDE: $(NPU_SC_UTILS_INCLUDE))
# $(info SPDLOG_INCLUDE: $(SPDLOG_INCLUDE))
# $(info INCLUDE_FLAGS: $(INCLUDE_FLAGS)) 