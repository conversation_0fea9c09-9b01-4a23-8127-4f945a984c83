//
//-----------------------------------------------------------//
//   Copyright 2024 Mixed SystemC/SystemVerilog Simulation   //
//                                                           //
//   Licensed under the Apache License, Version 2.0 (the     //
//   "License"); you may not use this file except in         //
//   compliance with the License.  You may obtain a copy of  //
//   the License at                                          //
//                                                           //
//       http://www.apache.org/licenses/LICENSE-2.0          //
//                                                           //
//   Unless required by applicable law or agreed to in       //
//   writing, software distributed under the License is      //
//   distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR  //
//   CONDITIONS OF ANY KIND, either express or implied.      //
//   See the License for the specific language governing     //
//   permissions and limitations under the License.          //
//-----------------------------------------------------------//

//-----------------------------------------------------------------------------
// Title: Mixed SystemC/SystemVerilog Simulation - FeatTMU SystemVerilog Top Module
//
// This is the top-level SystemVerilog module for the mixed simulation.
// It instantiates the feat_tmu producer and sets up UVMC connections to
// communicate with the SystemC feat_tmu cfg_socket.
//
// The sv_main module:
// - Creates an instance of the feat_tmu_producer
// - Registers the producer's initiator socket with UVMC using "feat_tmu_cfg"
// - Configures simulation parameters
// - Starts UVM simulation
//
//-----------------------------------------------------------------------------

import uvm_pkg::*;
import uvmc_pkg::*;

`include "feat_tmu_producer.sv"

// Top-level SystemVerilog module for FeatTMU testing
module sv_main;

    // Create the feat_tmu producer instance
    feat_tmu_producer prod = new("prod");

    initial begin
        // Connect producer's initiator socket to UVMC using "feat_tmu_cfg" lookup string
        // This will connect to the SystemC feat_tmu cfg_socket
        uvmc_tlm #()::connect(prod.out, "feat_tmu_cfg");

        // Configure number of commands (optional - producer has its own test sequence)
        uvm_config_db #(int)::set(null, "prod", "num_commands", 20);

        // Set UVM verbosity for better debugging
        // uvm_config_db #(uvm_verbosity)::set(null, "*", "recording_detail", UVM_FULL);

        // Start UVM test
        run_test();
    end

endmodule
