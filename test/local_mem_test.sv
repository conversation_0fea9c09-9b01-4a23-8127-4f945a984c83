//
//-----------------------------------------------------------//
//   Copyright 2024 Mixed SystemC/SystemVerilog Simulation   //
//                                                           //
//   Licensed under the Apache License, Version 2.0 (the     //
//   "License"); you may not use this file except in         //
//   compliance with the License.  You may obtain a copy of  //
//   the License at                                          //
//                                                           //
//       http://www.apache.org/licenses/LICENSE-2.0          //
//                                                           //
//   Unless required by applicable law or agreed to in       //
//   writing, software distributed under the License is      //
//   distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR  //
//   CONDITIONS OF ANY KIND, either express or implied.      //
//   See the License for the specific language governing     //
//   permissions and limitations under the License.          //
//-----------------------------------------------------------//

//-----------------------------------------------------------------------------
// Title: Mixed SystemC/SystemVerilog Simulation - SystemVerilog Top Module
//
// This is the top-level SystemVerilog module for the mixed simulation.
// It instantiates the memory producer and sets up UVMC connections to
// communicate with the SystemC local_mem slave.
//
// The sv_main module:
// - Creates an instance of the memory_producer
// - Registers the producer's initiator socket with UVMC using "local_mem"
// - Configures simulation parameters
// - Starts UVM simulation
//
//-----------------------------------------------------------------------------

import uvm_pkg::*;
import uvmc_pkg::*;

`include "memory_producer.sv"

// Top-level SystemVerilog module - simplified like the working example
module sv_main;

    // Create the memory producer instance
    memory_producer prod = new("prod");

    initial begin
        // Connect producer's initiator socket to UVMC using "local_mem" lookup string
        // This will connect to the SystemC local_mem target socket
        uvmc_tlm #()::connect(prod.out, "local_mem");

        // Configure number of transactions
        uvm_config_db #(int)::set(null, "prod", "num_transactions", 10);

        // Start UVM test
        run_test();
    end

endmodule
