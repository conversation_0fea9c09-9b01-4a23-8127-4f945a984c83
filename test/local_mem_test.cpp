//
//-----------------------------------------------------------//
//   Copyright 2024 Mixed SystemC/SystemVerilog Simulation   //
//                                                           //
//   Licensed under the Apache License, Version 2.0 (the     //
//   "License"); you may not use this file except in         //
//   compliance with the License.  You may obtain a copy of  //
//   the License at                                          //
//                                                           //
//       http://www.apache.org/licenses/LICENSE-2.0          //
//                                                           //
//   Unless required by applicable law or agreed to in       //
//   writing, software distributed under the License is      //
//   distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR  //
//   CONDITIONS OF ANY KIND, either express or implied.      //
//   See the License for the specific language governing     //
//   permissions and limitations under the License.          //
//-----------------------------------------------------------//

//-----------------------------------------------------------------------------
// Title: Mixed SystemC/SystemVerilog Simulation - SystemC Side
//
// This file implements the SystemC side of a mixed simulation environment
// where SystemVerilog producers drive SystemC local memory slaves via UVMC.
//
// The sc_main function:
// - Instantiates the local_mem SystemC module as a slave
// - Creates and optionally links a CIM cluster functional model
// - Registers the local_mem target socket with UVMC for SV communication
// - Starts SystemC simulation
//
//-----------------------------------------------------------------------------

#include "uvmc.h"
using namespace uvmc;

#include "local_mem.h"
#include "dcim_cluster.hpp"
#include "utils/sc_logger.h"
#include <csignal>
#include <cstdlib>

using namespace npu_sc;

// Global simulation monitor class to handle end_of_simulation callbacks
class SimulationMonitor : public sc_core::sc_module {
public:
    SC_HAS_PROCESS(SimulationMonitor);
    SimulationMonitor(sc_core::sc_module_name name)
        : sc_module(name) {
        SC_INFO("SIM_MONITOR", "Simulation monitor created");
    }

    // SystemC end_of_simulation callback - called when simulation ends
    virtual void end_of_simulation() override {
        SC_INFO("SIM_MONITOR", "=== SIMULATION ENDING - PRINTING FINAL STATISTICS ===");
        // Print sc_logger statistics
        sc_logger::print_id_statistics();
    }
};



int sc_main(int argc, char* argv[]) {
    // Initialize logging system
    sc_logger::initialize(
        spdlog::level::info,        // Console output level
        "mixed_sim.log",            // Log file name
        spdlog::level::debug        // File output level
    );

    SC_INFO("SC_MAIN", "Starting mixed SystemC/SystemVerilog simulation");
    SC_INFO("SC_MAIN", "SystemC version: {}", sc_core::sc_version());

    // Create CIM cluster functional model (optional)
    DcimCluster* dcim_cluster = nullptr;
    try {
        dcim_cluster = new DcimCluster();
        SC_INFO("SC_MAIN", "DcimCluster functional model created successfully");
    } catch (const std::exception& e) {
        SC_WARN("SC_MAIN", "Failed to create DcimCluster: {}. Continuing without functional model.", e.what());
        dcim_cluster = nullptr;
    }

    // Create the local memory instance
    LocalMemory local_mem("local_mem", dcim_cluster);

    // Create simulation monitor to handle end_of_simulation callbacks
    SimulationMonitor sim_monitor("sim_monitor");

    // Register the local memory target socket with UVMC using lookup string "local_mem"
    // This will be connected to the SystemVerilog producer's initiator socket
    uvmc_connect(local_mem.target_socket, "local_mem");

    SC_INFO("SC_MAIN", "UVMC connection registered with lookup string 'local_mem'");
    SC_INFO("SC_MAIN", "Starting SystemC simulation...");

    // Start SystemC simulation
    sc_start();
    // Cleanup
    delete dcim_cluster;

    return 0;
}
