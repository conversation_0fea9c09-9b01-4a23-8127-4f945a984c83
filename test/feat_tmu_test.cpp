//
//-----------------------------------------------------------//
//   Copyright 2024 Mixed SystemC/SystemVerilog Simulation   //
//                                                           //
//   Licensed under the Apache License, Version 2.0 (the     //
//   "License"); you may not use this file except in         //
//   compliance with the License.  You may obtain a copy of  //
//   the License at                                          //
//                                                           //
//       http://www.apache.org/licenses/LICENSE-2.0          //
//                                                           //
//   Unless required by applicable law or agreed to in       //
//   writing, software distributed under the License is      //
//   distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR  //
//   CONDITIONS OF ANY KIND, either express or implied.      //
//   See the License for the specific language governing     //
//   permissions and limitations under the License.          //
//-----------------------------------------------------------//

//-----------------------------------------------------------------------------
// Title: Mixed SystemC/SystemVerilog Simulation - FeatTMU Test
//
// This file implements the SystemC side of a mixed simulation environment
// where SystemVerilog producers drive SystemC feat_tmu module via UVMC.
//
// The sc_main function:
// - Instantiates the feat_tmu SystemC module
// - Instantiates the local_mem SystemC module as memory backend
// - Creates and optionally links a CIM cluster functional model
// - Connects feat_tmu.mem_socket to local_mem.target_socket
// - Registers the feat_tmu cfg_socket with UVMC for SV communication
// - Starts SystemC simulation
//
//-----------------------------------------------------------------------------

#include "uvmc.h"
using namespace uvmc;

#include "feat_tmu.h"
#include "local_mem.h"
#include "dcim_cluster.hpp"
#include "utils/sc_logger.h"
#include <csignal>
#include <cstdlib>

using namespace npu_sc;
using namespace feat_tmu;

// Global simulation monitor class to handle end_of_simulation callbacks
class SimulationMonitor : public sc_core::sc_module {
public:
    SC_HAS_PROCESS(SimulationMonitor);
    SimulationMonitor(sc_core::sc_module_name name)
        : sc_module(name) {
        SC_INFO("SIM_MONITOR", "Simulation monitor created");
    }

    // SystemC end_of_simulation callback - called when simulation ends
    virtual void end_of_simulation() override {
        SC_INFO("SIM_MONITOR", "=== SIMULATION ENDING - PRINTING FINAL STATISTICS ===");
        // Print sc_logger statistics
        sc_logger::print_id_statistics();
    }
};

int sc_main(int argc, char* argv[]) {
    // Initialize logging system
    sc_logger::initialize(
        spdlog::level::info,        // Console output level
        "feat_tmu_mixed_sim.log",   // Log file name
        spdlog::level::debug        // File output level
    );

    SC_INFO("SC_MAIN", "Starting mixed SystemC/SystemVerilog simulation for FeatTMU");
    SC_INFO("SC_MAIN", "SystemC version: {}", sc_core::sc_version());

    // Create CIM cluster functional model (optional)
    DcimCluster* dcim_cluster = nullptr;
    try {
        dcim_cluster = new DcimCluster();
        SC_INFO("SC_MAIN", "DcimCluster functional model created successfully");
    } catch (const std::exception& e) {
        SC_WARN("SC_MAIN", "Failed to create DcimCluster: {}. Continuing without functional model.", e.what());
        dcim_cluster = nullptr;
    }

    // Create the local memory instance
    LocalMemory local_mem("local_mem", dcim_cluster);
    SC_INFO("SC_MAIN", "LocalMemory instance created");

    // Create the feat_tmu instance
    FeatTMU feat_tmu("feat_tmu");
    SC_INFO("SC_MAIN", "FeatTMU instance created");

    // Connect feat_tmu memory socket to local_mem target socket
    feat_tmu.mem_socket.bind(local_mem.target_socket);
    SC_INFO("SC_MAIN", "Connected feat_tmu.mem_socket to local_mem.target_socket");

    // Create simulation monitor to handle end_of_simulation callbacks
    SimulationMonitor sim_monitor("sim_monitor");

    // Register the feat_tmu cfg_socket with UVMC using lookup string "feat_tmu_cfg"
    // This will be connected to the SystemVerilog producer's initiator socket
    uvmc_connect(feat_tmu.cfg_socket, "feat_tmu_cfg");

    SC_INFO("SC_MAIN", "UVMC connection registered with lookup string 'feat_tmu_cfg'");
    SC_INFO("SC_MAIN", "Starting SystemC simulation...");

    // Start SystemC simulation
    sc_start();
    
    // Cleanup
    delete dcim_cluster;

    return 0;
}
