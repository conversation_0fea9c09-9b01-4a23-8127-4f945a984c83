add_rules("mode.debug","mode.coverage")
set_project("npu_sc")
add_rules("plugin.compile_commands.autoupdate", {outputdir = ".vscode"})
add_requires("magic_enum 0.9.6")
add_requires("gtest 1.15.2")
add_requires("nlohmann_json 3.11.3")
add_requires("spdlog 1.15.2")
set_languages("cxx11")

-- set_policy("build.sanitizer.address", true)
-- set_policy("build.sanitizer.undefined", true)
-- set_policy("build.sanitizer.leak", true)

set_warnings("all", "extra", "pedantic")

-- 调试模式下不进行优化 -O0
if is_mode("debug") then
    set_optimize("none")
end

target("common")
    set_kind("static")
    add_files("include/npu_sc/npu_config.cpp")
    add_includedirs("/home/<USER>/Synopsys/vcs/V-2023.12-SP2/etc/uvmc-2.3.3-v23.0.1d/src/connect/sc", {public = true})
    add_includedirs("/home/<USER>/Synopsys/vcs/V-2023.12-SP2/include/systemc233", {public = true})
    add_includedirs("/home/<USER>/Synopsys/vcs/V-2023.12-SP2/include/systemc233/tlm_utils", {public = true})
    add_links("systemc", {public = true})
    add_includedirs("./include/npu_sc", {public = true})
    add_rpathdirs("/home/<USER>/Synopsys/vcs/V-2023.12-SP2/lib/linux64", {public = true})
    add_defines("SC_INCLUDE_DYNAMIC_PROCESSES", {public = true})


target("utils")
    set_kind("static")
    add_includedirs("./include/npu_sc/utils", {public = true})
    add_deps("ac_types")
    add_packages("magic_enum")



target("sc_logger")
    set_kind("static")
    add_headerfiles("include/npu_sc/utils/sc_logger.h")
    add_includedirs("include/npu_sc/utils", {public = true})
    add_deps("common")
    add_packages("spdlog")


target("ac_types")
    set_kind("static")
    add_includedirs("include/ac_types-master", {public = true})

-- includes("src/scoreboard/xmake.lua")
-- includes("src/cmd_scheduler/xmake.lua")
-- includes("src/tsu/xmake.lua")
-- includes("src/tlu/xmake.lua")
-- includes("src/dev_tsu_tlu/xmake.lua")
-- includes("src/cmd_decoder/xmake.lua")
-- includes("src/vpu/xmake.lua")
includes("src/local_mem/xmake.lua")
-- includes("src/feat_mpu/xmake.lua")

includes("src/cim_cluster/xmake.lua")
includes("src/feat_tmu/xmake.lua")

