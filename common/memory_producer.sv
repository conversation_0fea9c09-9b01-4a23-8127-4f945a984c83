//
//-----------------------------------------------------------//
//   Copyright 2024 Mixed SystemC/SystemVerilog Simulation   //
//                                                           //
//   Licensed under the Apache License, Version 2.0 (the     //
//   "License"); you may not use this file except in         //
//   compliance with the License.  You may obtain a copy of  //
//   the License at                                          //
//                                                           //
//       http://www.apache.org/licenses/LICENSE-2.0          //
//                                                           //
//   Unless required by applicable law or agreed to in       //
//   writing, software distributed under the License is      //
//   distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR  //
//   CONDITIONS OF ANY KIND, either express or implied.      //
//   See the License for the specific language governing     //
//   permissions and limitations under the License.          //
//-----------------------------------------------------------//

//-----------------------------------------------------------------------------
// Title: Memory Producer for Mixed SystemC/SystemVerilog Simulation
//
// This SystemVerilog module generates memory read/write transactions to drive
// the SystemC local_mem slave module via UVMC connections.
//
// The producer generates TLM generic payload transactions for memory access:
// - SPAD (Scratchpad) memory accesses
// - CIM (Compute-in-Memory) cluster accesses
// - Both read and write operations
// - Different data patterns and addressing modes
//
//-----------------------------------------------------------------------------

import uvm_pkg::*;
`include "uvm_macros.svh"

// Memory transaction sequence item
class memory_transaction extends uvm_sequence_item;

    // Transaction fields
    rand bit [63:0] address;        // 64-bit address
    rand bit [31:0] length;         // Transaction length in bytes
    rand bit        is_write;       // 1 = write, 0 = read
    rand bit [7:0]  data[];         // Dynamic array for data

    // Address constraints for local memory layout
    // SPAD units: addresses 0x0 to 0x7FFF (unit 0-3)
    // CIM units:  addresses 0x8000 to 0xFFFF (unit 4)
    constraint addr_c {
        // Ensure 256-bit (32-byte) alignment
        address[4:0] == 5'b0;
        // Limit to reasonable address range for testing
        address < 64'h1000;  // Smaller range for testing
    }

    constraint length_c {
        // Standard word size is 32 bytes (256 bits)
        length == 32;  // Fixed to 32 bytes for simplicity
    }

    constraint data_size_c {
        data.size() == length;
    }

    `uvm_object_utils_begin(memory_transaction)
        `uvm_field_int(address, UVM_DEFAULT | UVM_HEX)
        `uvm_field_int(length, UVM_DEFAULT)
        `uvm_field_int(is_write, UVM_DEFAULT)
        `uvm_field_array_int(data, UVM_DEFAULT | UVM_HEX)
    `uvm_object_utils_end

    function new(string name = "memory_transaction");
        super.new(name);
    endfunction

    function string convert2string();
        string s;
        s = $sformatf("addr=%0h len=%0d %s",
                     address, length, is_write ? "WRITE" : "READ");
        if (is_write && data.size() > 0) begin
            s = {s, $sformatf(" data[0:3]=%0h_%0h_%0h_%0h",
                             data[0], data[1], data[2], data[3])};
        end
        return s;
    endfunction

endclass

// Memory producer driver
class memory_producer extends uvm_driver #(memory_transaction);
    
    // TLM initiator socket for UVMC connection
    uvm_tlm_b_initiator_socket #() out;
    uvm_analysis_port #(memory_transaction) ap;
    
    `uvm_component_utils(memory_producer)
    
    function new(string name, uvm_component parent = null);
        super.new(name, parent);
        out = new("out", this);
        ap = new("ap", this);
    endfunction
    
    task run_phase(uvm_phase phase);
        memory_transaction mem_trans;
        uvm_tlm_gp gp;
        uvm_tlm_time delay;
        int num_transactions = 10;
        bit [63:0] test_addresses[$];  // Queue to store test addresses

        // Keep the run phase from ending
        if (phase.phase_done != null) begin
            phase.raise_objection(this);
        end

        // Get number of transactions from config
        void'(uvm_config_db #(int)::get(this, "", "num_transactions", num_transactions));

        `uvm_info("PRODUCER", $sformatf("Starting memory producer with %0d transactions",
                 num_transactions), UVM_LOW)

        // Generate and send transactions (alternating write/read)
        for (int i = 0; i < num_transactions; i++) begin

            // Create transaction
            mem_trans = memory_transaction::type_id::create($sformatf("mem_trans_%0d", i));

            // Alternate between write and read
            if (i % 2 == 0) begin
                // Even transactions: WRITE with incremental data
                mem_trans.is_write = 1;
                mem_trans.address = (i/2) * 32;  // Address 0, 32, 64, 96, ...
                mem_trans.length = 32;
                mem_trans.data = new[32];

                // Fill with incremental data: 00, 01, 02, ...
                for (int j = 0; j < 32; j++) begin
                    mem_trans.data[j] = (i/2 * 32 + j) & 8'hFF;
                end

                // Store address for later read
                test_addresses.push_back(mem_trans.address);

            end else begin
                // Odd transactions: READ from previously written address
                mem_trans.is_write = 0;
                mem_trans.address = test_addresses[i/2];  // Read from corresponding write address
                mem_trans.length = 32;
                mem_trans.data = new[32];
            end
            
            // Create TLM generic payload
            gp = new();
            delay = new("delay", 1e-12);
            
            // Set up the generic payload
            gp.set_address(mem_trans.address);
            gp.set_data_length(mem_trans.length);
            gp.set_streaming_width(mem_trans.length);
            gp.set_byte_enable_length(0);  // No byte enables
            gp.set_dmi_allowed(0);
            gp.set_response_status(UVM_TLM_INCOMPLETE_RESPONSE);
            
            if (mem_trans.is_write) begin
                gp.set_command(UVM_TLM_WRITE_COMMAND);
                // Set data using the correct method like in producer_struct.sv
                gp.m_data = new[mem_trans.length];
                for (int j = 0; j < mem_trans.length; j++) begin
                    gp.m_data[j] = mem_trans.data[j];
                end
                gp.m_length = mem_trans.length;
            end else begin
                gp.set_command(UVM_TLM_READ_COMMAND);
                // For read, allocate buffer
                gp.m_data = new[mem_trans.length];
                gp.m_length = mem_trans.length;
            end
            
            if (mem_trans.is_write) begin
                `uvm_info("PRODUCER", $sformatf("Sending WRITE transaction %0d: addr=%0h len=%0d data[0:3]=%0h_%0h_%0h_%0h",
                         i, mem_trans.address, mem_trans.length,
                         mem_trans.data[0], mem_trans.data[1], mem_trans.data[2], mem_trans.data[3]), UVM_MEDIUM)
            end else begin
                `uvm_info("PRODUCER", $sformatf("Sending READ transaction %0d: addr=%0h len=%0d",
                         i, mem_trans.address, mem_trans.length), UVM_MEDIUM)
            end
            `uvm_info("PRODUCER/PKT/SEND",{"\n",gp.sprint()},UVM_MEDIUM)
            // Send transaction via TLM
            delay.set_abstime(10, 1e-9);
            out.b_transport(gp, delay);
            
            // Check response
            if (gp.get_response_status() == UVM_TLM_OK_RESPONSE) begin
                `uvm_info("PRODUCER", $sformatf("Transaction %0d completed successfully", i), UVM_MEDIUM)
                
                // For read transactions, copy back the data from gp.m_data
                if (!mem_trans.is_write) begin
                    for (int j = 0; j < mem_trans.length; j++) begin
                        mem_trans.data[j] = gp.m_data[j];
                    end
                    `uvm_info("PRODUCER", $sformatf("Read data[0:7]: %0h_%0h_%0h_%0h_%0h_%0h_%0h_%0h",
                             mem_trans.data[0], mem_trans.data[1], mem_trans.data[2], mem_trans.data[3],
                             mem_trans.data[4], mem_trans.data[5], mem_trans.data[6], mem_trans.data[7]), UVM_MEDIUM)

                    // Verify read data matches expected pattern
                    begin
                        bit data_correct = 1;
                        int expected_base = (i/2) * 32;
                        for (int j = 0; j < mem_trans.length; j++) begin
                            bit [7:0] expected_data = (expected_base + j) & 8'hFF;
                            if (mem_trans.data[j] !== expected_data) begin
                                data_correct = 0;
                                break;
                            end
                        end

                        if (data_correct) begin
                            `uvm_info("PRODUCER", $sformatf("READ VERIFICATION PASSED for transaction %0d", i), UVM_LOW)
                        end else begin
                            `uvm_error("PRODUCER", $sformatf("READ VERIFICATION FAILED for transaction %0d", i))
                        end
                    end
                end
            end else begin
                `uvm_error("PRODUCER", $sformatf("Transaction %0d failed with status %s", 
                          i, gp.get_response_status().name()))
            end
            
            // Send to analysis port
            ap.write(mem_trans);
            
            // Add some delay between transactions
            #100ns;
        end
        
        `uvm_info("PRODUCER", "Memory producer completed all transactions", UVM_LOW)
        
        // Wait a bit before ending
        #1us;
        
        if (phase.phase_done != null) begin
            phase.drop_objection(this);
        end
        
    endtask
    
endclass
