//
//-----------------------------------------------------------//
//   Copyright 2024 Mixed SystemC/SystemVerilog Simulation   //
//                                                           //
//   Licensed under the Apache License, Version 2.0 (the     //
//   "License"); you may not use this file except in         //
//   compliance with the License.  You may obtain a copy of  //
//   the License at                                          //
//                                                           //
//       http://www.apache.org/licenses/LICENSE-2.0          //
//                                                           //
//   Unless required by applicable law or agreed to in       //
//   writing, software distributed under the License is      //
//   distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR  //
//   CONDITIONS OF ANY KIND, either express or implied.      //
//   See the License for the specific language governing     //
//   permissions and limitations under the License.          //
//-----------------------------------------------------------//

//-----------------------------------------------------------------------------
// Title: FeatTMU Producer for Mixed SystemC/SystemVerilog Simulation
//
// This SystemVerilog module generates IssueQueueCmd transactions to drive
// the SystemC feat_tmu module's cfg_socket via UVMC connections.
//
// The producer generates various TMU command sequences:
// - TM_CFG: TMU configuration commands
// - BC_PRE/BC_DRV: Broadcast operations
// - MOV_DRV: Move operations
// - TRANS_DRV: Transpose operations
// - SET_CIMEXP/GET_CIMEXP: CIM exponent operations
//
// This follows the pattern established by TMUTestBase::send_command()
//-----------------------------------------------------------------------------

import uvm_pkg::*;
`include "uvm_macros.svh"

// TMU operation opcodes - matching npu_config.h opcode namespace
package tmu_opcodes;
    // Tensor Manipulation Group
    parameter bit [7:0] TM_CFG      = 8'h30;
    parameter bit [7:0] BC_PRE      = 8'h31;
    parameter bit [7:0] BC_DRV      = 8'h39;
    parameter bit [7:0] MOV_DRV     = 8'h38;
    parameter bit [7:0] TRANS_DRV   = 8'h3A;
    parameter bit [7:0] SET_CIMEXP  = 8'h33;
    parameter bit [7:0] GET_CIMEXP  = 8'h34;
endpackage

// IssueQueueCmd transaction sequence item
class tmu_command_transaction extends uvm_sequence_item;

    // Transaction fields matching IssueQueueCmd structure
    rand bit [7:0]  funct7;         // Operation code
    rand bit        xd;             // Destination register enable
    rand bit        xs1;            // Source register 1 enable  
    rand bit        xs2;            // Source register 2 enable
    rand bit [31:0] rs1val;         // Source register 1 value
    rand bit [31:0] rs2val;         // Source register 2 value

    // Constraints for realistic TMU operations
    constraint funct7_c {
        funct7 inside {
            tmu_opcodes::TM_CFG,
            tmu_opcodes::BC_PRE,
            tmu_opcodes::BC_DRV,
            tmu_opcodes::MOV_DRV,
            tmu_opcodes::TRANS_DRV,
            tmu_opcodes::SET_CIMEXP,
            tmu_opcodes::GET_CIMEXP
        };
    }

    // For TM_CFG operations, rs1val should be register index (0-6)
    constraint tm_cfg_c {
        if (funct7 == tmu_opcodes::TM_CFG) {
            rs1val < 7;  // Register index 0-6
        }
    }

    `uvm_object_utils_begin(tmu_command_transaction)
        `uvm_field_int(funct7, UVM_DEFAULT | UVM_HEX)
        `uvm_field_int(xd, UVM_DEFAULT)
        `uvm_field_int(xs1, UVM_DEFAULT)
        `uvm_field_int(xs2, UVM_DEFAULT)
        `uvm_field_int(rs1val, UVM_DEFAULT | UVM_HEX)
        `uvm_field_int(rs2val, UVM_DEFAULT | UVM_HEX)
    `uvm_object_utils_end

    function new(string name = "tmu_command_transaction");
        super.new(name);
    endfunction

    function string convert2string();
        string s;
        s = $sformatf("funct7=%0h xd=%0b xs1=%0b xs2=%0b rs1val=%0h rs2val=%0h",
                     funct7, xd, xs1, xs2, rs1val, rs2val);
        return s;
    endfunction

endclass

// FeatTMU producer driver
class feat_tmu_producer extends uvm_driver #(tmu_command_transaction);
    
    // TLM initiator socket for UVMC connection
    uvm_tlm_b_initiator_socket #() out;
    uvm_analysis_port #(tmu_command_transaction) ap;
    
    `uvm_component_utils(feat_tmu_producer)
    
    function new(string name, uvm_component parent = null);
        super.new(name, parent);
        out = new("out", this);
        ap = new("ap", this);
    endfunction
    
    task run_phase(uvm_phase phase);
        tmu_command_transaction tmu_cmd;
        uvm_tlm_gp gp;
        uvm_tlm_time delay;
        int num_commands = 20;
        
        // Keep the run phase from ending
        if (phase.phase_done != null) begin
            phase.raise_objection(this);
        end

        // Get number of commands from config
        void'(uvm_config_db #(int)::get(this, "", "num_commands", num_commands));

        `uvm_info("TMU_PRODUCER", $sformatf("Starting feat_tmu producer with %0d commands",
                 num_commands), UVM_LOW)

        // Generate and send TMU command sequence
        send_tmu_test_sequence();
        
        `uvm_info("TMU_PRODUCER", "FeatTMU producer completed all commands", UVM_LOW)
        
        // Wait a bit before ending
        #1us;
        
        if (phase.phase_done != null) begin
            phase.drop_objection(this);
        end
        
    endtask

    // Send a complete TMU test sequence
    task send_tmu_test_sequence();
        `uvm_info("TMU_PRODUCER", "=== Starting TMU Test Sequence ===", UVM_LOW)
        
        // 1. Configure TMU with basic settings
        send_tmu_configuration();
        
        // 2. Test broadcast operation
        send_broadcast_sequence();
        
        // 3. Test move operation  
        send_move_sequence();
        
        // 4. Test transpose operation
        send_transpose_sequence();
        
        // 5. Test CIM exponent operations
        send_cim_exp_sequence();
        
        `uvm_info("TMU_PRODUCER", "=== TMU Test Sequence Complete ===", UVM_LOW)
    endtask

    // Configure TMU with 7 configuration registers
    task send_tmu_configuration();
        tmu_command_transaction cfg_cmd;
        
        `uvm_info("TMU_PRODUCER", "Configuring TMU with 7 registers...", UVM_MEDIUM)
        
        for (int i = 0; i < 7; i++) begin
            cfg_cmd = tmu_command_transaction::type_id::create($sformatf("cfg_cmd_%0d", i));
            cfg_cmd.funct7 = tmu_opcodes::TM_CFG;
            cfg_cmd.xd = 0;
            cfg_cmd.xs1 = 0;
            cfg_cmd.xs2 = 0;
            cfg_cmd.rs1val = i;  // Register index
            
            // Set some example configuration values
            case (i)
                0: cfg_cmd.rs2val = 32'h00000001;  // cfg_size_dim0b
                1: cfg_cmd.rs2val = 32'h00000040;  // cfg_size_dim1 = 64
                2: cfg_cmd.rs2val = 32'h00000004;  // cfg_size_dim2 = 4
                3: cfg_cmd.rs2val = 32'h00000000;  // cfg_rem_dim0
                4: cfg_cmd.rs2val = 32'h00010001;  // strides
                5: cfg_cmd.rs2val = 32'h00010001;  // more strides
                6: cfg_cmd.rs2val = 32'h00000002;  // cfg_type and cfg_wd
                default: cfg_cmd.rs2val = 32'h00000000;
            endcase
            
            send_command(cfg_cmd);
            #10ns;  // Small delay between config commands
        end
        
        `uvm_info("TMU_PRODUCER", "TMU configuration complete", UVM_MEDIUM)
    endtask

    // Send broadcast operation sequence
    task send_broadcast_sequence();
        tmu_command_transaction bc_cmd;
        
        `uvm_info("TMU_PRODUCER", "Testing broadcast operation...", UVM_MEDIUM)
        
        // BC_PRE: Set scalar value
        bc_cmd = tmu_command_transaction::type_id::create("bc_pre_cmd");
        bc_cmd.funct7 = tmu_opcodes::BC_PRE;
        bc_cmd.xd = 0;
        bc_cmd.xs1 = 0;
        bc_cmd.xs2 = 0;
        bc_cmd.rs1val = 32'h12345678;  // Scalar value to broadcast
        bc_cmd.rs2val = 32'h00000000;
        send_command(bc_cmd);
        #10ns;
        
        // BC_DRV: Execute broadcast to memory address
        bc_cmd = tmu_command_transaction::type_id::create("bc_drv_cmd");
        bc_cmd.funct7 = tmu_opcodes::BC_DRV;
        bc_cmd.xd = 0;
        bc_cmd.xs1 = 0;
        bc_cmd.xs2 = 0;
        bc_cmd.rs1val = 32'h00000000;  // Output address (SPAD unit 0)
        bc_cmd.rs2val = 32'h00000000;
        send_command(bc_cmd);
        #100ns;  // Wait for operation to complete
        
        `uvm_info("TMU_PRODUCER", "Broadcast operation complete", UVM_MEDIUM)
    endtask

    // Send move operation sequence
    task send_move_sequence();
        tmu_command_transaction mov_cmd;
        
        `uvm_info("TMU_PRODUCER", "Testing move operation...", UVM_MEDIUM)
        
        mov_cmd = tmu_command_transaction::type_id::create("mov_drv_cmd");
        mov_cmd.funct7 = tmu_opcodes::MOV_DRV;
        mov_cmd.xd = 0;
        mov_cmd.xs1 = 0;
        mov_cmd.xs2 = 0;
        mov_cmd.rs1val = 32'h00000100;  // Output address
        mov_cmd.rs2val = 32'h00000000;  // Input address
        send_command(mov_cmd);
        #100ns;  // Wait for operation to complete
        
        `uvm_info("TMU_PRODUCER", "Move operation complete", UVM_MEDIUM)
    endtask

    // Send transpose operation sequence
    task send_transpose_sequence();
        tmu_command_transaction trans_cmd;
        
        `uvm_info("TMU_PRODUCER", "Testing transpose operation...", UVM_MEDIUM)
        
        trans_cmd = tmu_command_transaction::type_id::create("trans_drv_cmd");
        trans_cmd.funct7 = tmu_opcodes::TRANS_DRV;
        trans_cmd.xd = 0;
        trans_cmd.xs1 = 0;
        trans_cmd.xs2 = 0;
        trans_cmd.rs1val = 32'h00000200;  // Output address
        trans_cmd.rs2val = 32'h00000100;  // Input address
        send_command(trans_cmd);
        #100ns;  // Wait for operation to complete
        
        `uvm_info("TMU_PRODUCER", "Transpose operation complete", UVM_MEDIUM)
    endtask

    // Send CIM exponent operation sequence
    task send_cim_exp_sequence();
        tmu_command_transaction exp_cmd;
        
        `uvm_info("TMU_PRODUCER", "Testing CIM exponent operations...", UVM_MEDIUM)
        
        // SET_CIMEXP: Set exponent values
        exp_cmd = tmu_command_transaction::type_id::create("set_cimexp_cmd");
        exp_cmd.funct7 = tmu_opcodes::SET_CIMEXP;
        exp_cmd.xd = 0;
        exp_cmd.xs1 = 0;
        exp_cmd.xs2 = 0;
        exp_cmd.rs1val = 32'h00008000;  // CIMC address
        exp_cmd.rs2val = 32'h00000300;  // SPAD source address
        send_command(exp_cmd);
        #100ns;
        
        // GET_CIMEXP: Read exponent values
        exp_cmd = tmu_command_transaction::type_id::create("get_cimexp_cmd");
        exp_cmd.funct7 = tmu_opcodes::GET_CIMEXP;
        exp_cmd.xd = 0;
        exp_cmd.xs1 = 0;
        exp_cmd.xs2 = 0;
        exp_cmd.rs1val = 32'h00008000;  // CIMC source address
        exp_cmd.rs2val = 32'h00000400;  // SPAD destination address
        send_command(exp_cmd);
        #100ns;
        
        `uvm_info("TMU_PRODUCER", "CIM exponent operations complete", UVM_MEDIUM)
    endtask

    // Send individual command via TLM
    task send_command(tmu_command_transaction cmd);
        uvm_tlm_gp gp;
        uvm_tlm_time delay;
        byte cmd_bytes[];
        
        // Create TLM generic payload
        gp = new();
        delay = new("delay", 1e-12);
        
        // Pack the command structure into bytes (matching IssueQueueCmd layout)
        cmd_bytes = new[12];  // sizeof(IssueQueueCmd) = 12 bytes (1+1+1+1+4+4)
        cmd_bytes[0] = cmd.funct7;
        cmd_bytes[1] = cmd.xd ? 8'h01 : 8'h00;
        cmd_bytes[2] = cmd.xs1 ? 8'h01 : 8'h00;
        cmd_bytes[3] = cmd.xs2 ? 8'h01 : 8'h00;
        // rs1val (little endian)
        cmd_bytes[4] = cmd.rs1val[7:0];
        cmd_bytes[5] = cmd.rs1val[15:8];
        cmd_bytes[6] = cmd.rs1val[23:16];
        cmd_bytes[7] = cmd.rs1val[31:24];
        // rs2val (little endian)
        cmd_bytes[8] = cmd.rs2val[7:0];
        cmd_bytes[9] = cmd.rs2val[15:8];
        cmd_bytes[10] = cmd.rs2val[23:16];
        cmd_bytes[11] = cmd.rs2val[31:24];

        // Set up the generic payload
        gp.set_command(UVM_TLM_WRITE_COMMAND);
        gp.set_address(64'h0);  // Address not used for cfg_socket
        gp.set_data_length(12);
        gp.set_streaming_width(12);
        gp.set_byte_enable_length(0);
        gp.set_dmi_allowed(0);
        gp.set_response_status(UVM_TLM_INCOMPLETE_RESPONSE);
        
        // Set data
        gp.m_data = new[12];
        for (int i = 0; i < 12; i++) begin
            gp.m_data[i] = cmd_bytes[i];
        end
        gp.m_length = 12;
        
        `uvm_info("TMU_PRODUCER", $sformatf("Sending TMU command: %s", cmd.convert2string()), UVM_MEDIUM)
        
        // Send transaction via TLM
        delay.set_abstime(10, 1e-9);
        out.b_transport(gp, delay);
        
        // Check response
        if (gp.get_response_status() == UVM_TLM_OK_RESPONSE) begin
            `uvm_info("TMU_PRODUCER", "Command sent successfully", UVM_HIGH)
        end else begin
            `uvm_error("TMU_PRODUCER", $sformatf("Command failed with status %s", 
                      gp.get_response_status().name()))
        end
        
        // Send to analysis port
        ap.write(cmd);
    endtask
    
endclass
