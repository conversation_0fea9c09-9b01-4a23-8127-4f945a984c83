//
//-----------------------------------------------------------//
//   Copyright 2021 Siemens EDA                              //
//                                                           //
//   Licensed under the Apache License, Version 2.0 (the     //
//   "License"); you may not use this file except in         //
//   compliance with the License.  You may obtain a copy of  //
//   the License at                                          //
//                                                           //
//       http://www.apache.org/licenses/LICENSE-2.0          //
//                                                           //
//   Unless required by applicable law or agreed to in       //
//   writing, software distributed under the License is      //
//   distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR  //
//   CONDITIONS OF ANY KIND, either express or implied.      //
//   See the License for the specific language governing     //
//   permissions and limitations under the License.          //
//-----------------------------------------------------------//

//-----------------------------------------------------------------------------
// Title: UVMC Connection Common Code - SV Producer
//
// Topic: Description
//
// A simple SV producer TLM model that generates a configurable number of
// ~uvm_tlm_generic_payload~ transactions. The model uses the TLM2
// blocking interface, whose semantic guarantees the transaction is fully
// completed upon return from the b_transport call. Thus, we can reuse the
// transaction each iterationi and need only allocate the transaction once.
//
// This example uses the ~uvm_tlm_b_initiator_socket~, which only uses the
// ~b_transport~ TLM interface. It's default transaction type is the
// ~uvm_tlm_generic_payload~, which is what this example uses. 
//
// Normally, a monitor, not the producer, emits observed transactions 
// through an analysis port. We use the analysis port here only to illustrate
// external connectivity.
//
// While trivial in functionality, the model demonstrates use of TLM ports
// to facilitate external communication. 
//
// - Users of the model are not coupled to its internal implementation, using
//   only the provided TLM port and socket to communicate.
//
// - The model itself does not refer to anything outside its encapsulated
//   implementation. It does not know nor care about what might
//   be driving its ~in~ socket or who might be listening on its ~ap~
//   analysis port.
//-----------------------------------------------------------------------------

// (inline source)
import uvm_pkg::*; 
`include "uvm_macros.svh"

typedef struct packed {
  bit   [7:0] funct7;
  bit   [7:0] xd;
  bit   [7:0] xs1;
  bit   [7:0] xs2;
  bit  [31:0] rs1val;
  bit  [31:0] rs2val;
} IssueQueueCmd_s;


// UVM 序列项，包含与结构体同样的字段，用于驱动和监控
class IssueQueueCmd_tr extends uvm_sequence_item;
  rand bit [7:0]  funct7;
  rand bit        xd;
  rand bit        xs1;
  rand bit        xs2;
  rand bit [31:0] rs1val;
  rand bit [31:0] rs2val;

   `uvm_object_utils_begin(IssueQueueCmd_tr)
     `uvm_field_int(funct7, UVM_DEFAULT)
     `uvm_field_int(xd, UVM_DEFAULT)
     `uvm_field_int(xs1, UVM_DEFAULT)
     `uvm_field_int(xs2, UVM_DEFAULT)
     `uvm_field_int(rs1val, UVM_DEFAULT)
     `uvm_field_int(rs2val, UVM_DEFAULT)
   `uvm_object_utils_end

   function new(string name = "IssueQueueCmd_tr");
      super.new(name);
   endfunction
  // 打印方法
  function void do_print (uvm_printer printer);
    super.do_print(printer);
    printer.print_field_int("funct7", funct7, 8);
    printer.print_field_int("xd",      xd,     1);
    printer.print_field_int("xs1",     xs1,    1);
    printer.print_field_int("xs2",     xs2,    1);
    printer.print_field_int("rs1val",  rs1val,32);
    printer.print_field_int("rs2val",  rs2val,32);
  endfunction

  function string convert2string();
    return $sformatf("funct7=%0h xd=%0b xs1=%0b xs2=%0b rs1val=%0h rs2val=%0h",
                     funct7, xd, xs1, xs2, rs1val, rs2val);
  endfunction
  
  function void do_pack(uvm_packer packer);
    // 关键步骤：设置字节序以匹配目标C++架构（通常为x86，即小端）
    packer.big_endian = 0;

    // 按照C++紧凑结构体的确切顺序和大小进行打包
    // `uvm_pack_int` 是一个方便的宏，它调用 packer.pack_field_int
    `uvm_pack_int(funct7) // 自动打包8比特 (sizeof(byte))
    
    // 关键：将1比特的'bool'值打包为1字节（8比特）
    `uvm_pack_intN(xd, 8)
    `uvm_pack_intN(xs1, 8)
    `uvm_pack_intN(xs2, 8)

    // 打包32位整数，遵循小端字节序
    `uvm_pack_int(rs1val) // 自动打包32比特 (sizeof(int unsigned))
    `uvm_pack_int(rs2val) // 自动打包32比特
  endfunction : do_pack

  function void do_unpack(uvm_packer packer);
      // 确保解包时也使用相同的字节序
      packer.big_endian = 0;

      // 按照与打包完全相同的顺序和大小进行解包
      `uvm_unpack_int(funct7)
      `uvm_unpack_intN(xd, 8)
      `uvm_unpack_intN(xs1, 8)
      `uvm_unpack_intN(xs2, 8)
      `uvm_unpack_int(rs1val)
      `uvm_unpack_int(rs2val)
  endfunction : do_unpack
endclass

class producer extends uvm_driver #(IssueQueueCmd_tr);

   uvm_tlm_b_initiator_socket #() out;
   uvm_analysis_port #(IssueQueueCmd_tr) ap; 

   `uvm_component_utils(producer)
   
   function new(string name, uvm_component parent=null);
      super.new(name,parent);
      out = new("out", this);
      ap = new("ap", this);
   endfunction

   task run_phase (uvm_phase phase);
      IssueQueueCmd_tr tr;
      // Allocate GP once
      uvm_tlm_gp gp = new;
      byte  packed_data[];
      uvm_tlm_time delay = new("del",1e-12);
      int num_trans = 2;
      int objection_raised = 0;

      // Keep the "run" phase from ending
      if( phase.phase_done != null ) begin
          phase.raise_objection(this);
          objection_raised = 1;
      end

      // Get number of transactions desired (default=2)
      void'(uvm_config_db #(uvm_bitstream_t)::get(this,"","num_trans",num_trans));

      // Iterate N times, randomizing transaction, setting delay
      for (int i = 0; i < num_trans; i++) begin

         // 创建并随机化或设置cmd_item的字段
         tr = IssueQueueCmd_tr::type_id::create("tr");
         assert(tr.randomize());

         // 手动打包字段为字节数组，模拟C++结构体的内存布局
         packed_data = new[12]; // funct7(1) + xd(1) + xs1(1) + xs2(1) + rs1val(4) + rs2val(4) = 12 bytes
         packed_data[0] = tr.funct7;
         packed_data[1] = tr.xd;
         packed_data[2] = tr.xs1;
         packed_data[3] = tr.xs2;
         // 打包32位整数为小端字节序
         {packed_data[7], packed_data[6], packed_data[5], packed_data[4]} = tr.rs1val;
         {packed_data[11], packed_data[10], packed_data[9], packed_data[8]} = tr.rs2val;

         // 现在, packed_data 包含了12字节的、与C++结构体内存布局完全一致的数据
         `uvm_info("PACK_INFO", $sformatf("Packed data size: %0d bytes",$size(packed_data)), UVM_LOW)

         delay.set_abstime(10,1e-9);
         
         // 设置其他TLM属性
         gp.set_command(UVM_TLM_WRITE_COMMAND);
         gp.set_address(0); // 地址可以根据应用场景设置，或设为0
         gp.set_response_status(UVM_TLM_INCOMPLETE_RESPONSE);
         
         // 直接设置 m_data 和 m_length 字段
         gp.m_data = new[$size(packed_data)];
         for (int j = 0; j < $size(packed_data); j++) begin
            gp.m_data[j] = packed_data[j];
         end
         gp.m_length = $size(packed_data);
         
         `uvm_info("PRODUCER/PKT/SEND",{"\n",gp.sprint()},UVM_MEDIUM)

         out.b_transport(gp,delay);
         ap.write(tr);  // Send the transaction object, not the generic payload
      end
      #100;
      `uvm_info("PRODUCER/END_TEST",
                "Dropping objection to ending the test",UVM_LOW)
      if( objection_raised == 1 ) phase.drop_objection(this);
   endtask

endclass
