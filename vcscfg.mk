#
# //------------------------------------------------------------//
# //   Copyright 2012 Synopsys, Inc                             //
# //   All Rights Reserved Worldwid                             //
# //                                                            //
# //   Licensed under the Apache License, Version 2.0 (the      //
# //   "License"); you may not use this file except in          //
# //   compliance with the License.  You may obtain a copy of   //
# //   the License at                                           //
# //                                                            //
# //       http://www.apache.org/licenses/LICENSE-2.0           //
# //                                                            //
# //   Unless required by applicable law or agreed to in        //
# //   writing, software distributed under the License is       //
# //   distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR   //
# //   CONDITIONS OF ANY KIND, either express or implied.  See  //
# //   the License for the specific language governing          //
# //   permissions and limitations under the License.           //
# //------------------------------------------------------------//


UVMC_HOME ?= ..

VERBOSE=+UVM_VERBOSITY=UVM_MEDIUM

SYSCAN = syscan -cpp g++ -cc gcc $(VCS_BITS) \
    -cflags -g -cflags -DVCS -cflags -I. \
    -tlm2 -cflags -I${VCS_HOME}/include/systemc233/tlm_utils \
    -cflags -I${UVMC_HOME}/src/connect/sc ${UVMC_HOME}/src/connect/sc/uvmc.cpp

VLOGAN = vlogan -q $(VCS_BITS) \
    -sverilog +incdir+${UVM_HOME}/src ${UVM_HOME}/src/uvm_pkg.sv \
    +incdir+${UVMC_HOME}/src/connect/sv \
    ${UVMC_HOME}/src/connect/sv/uvmc_pkg.sv -timescale=1ns/1ps

VCS_ELAB = vcs -q -sysc=deltasync -lca $(VCS_BITS) -sysc -timescale=1ns/1ps

VCS = vcs -q -sysc=deltasync -lca $(VCS_BITS) \
    -sverilog +incdir+${UVM_HOME}/src ${UVM_HOME}/src/uvm_pkg.sv \
    +incdir+${UVMC_HOME}/src/connect/sv \
    ${UVMC_HOME}/src/connect/sv/uvmc_pkg.sv -timescale=1ns/1ps -sysc

COMPILE_UVM_DPI = \
    g++ -c -fPIC -DVCS -I$(VCS_HOME)/include ${UVM_HOME}/src/dpi/uvm_dpi.cc \
        -o uvm_dpi.o; \
    g++ -shared -o uvm_dpi.so uvm_dpi.o

#-sysc=deltasync //default in 12.09
# -sysc=blocksync //default in 11.12

SIMV = $(COMPILE_UVM_DPI); ./simv -q -sv_lib uvm_dpi

ifeq ($(BITS),64)
VCS_BITS=-full64
else
VCS_BITS=
endif

check_uvm_home:
ifndef UVM_HOME
	@echo "ERROR: UVM_HOME environment variable is not defined"
	abort-mission
endif

clean:
	rm -rf simv* work csrc ucli.key vc_hdrs.h vcs.log AN* *.log *.log.cmp *.vpd DVE* .vlogan* uvm_dpi.*
